query ProductCategories($locale: LanguageCodeEnum!, $first: Int) {
	categories(first: $first, level: 0) {
		edges {
			node {
				...CategoryChildren
			}
		}
	}
}

fragment CategoryBase on Category {
	id
	name
	slug
	description
		media: metafield(key: "media")
    sortNumber: metafield(key: "sortNumber")
  metadata {
          key
          value
        }
}

fragment CategoryWithTranslation on Category {
	...CategoryBase
	...CategoryLocaleItem
}

fragment CategoryChildren on Category {
	...CategoryWithTranslation
	children(first: $first) {
		edges {
			node {
				...CategoryWithTranslation
				children(first: $first) {
					edges {
						node {
							...CategoryWithTranslation
						}
					}
				}
			}
		}
	}
}
