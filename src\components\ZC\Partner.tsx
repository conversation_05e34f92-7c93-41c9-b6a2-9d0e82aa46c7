"use client";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import Marquee from "react-fast-marquee";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

const DATA = [
    { img: "/image/home/<USER>/1.png" },
    { img: "/image/home/<USER>/2.png" },
    { img: "/image/home/<USER>/3.png" },
    { img: "/image/home/<USER>/4.png" },
    { img: "/image/home/<USER>/5.png" },
    { img: "/image/home/<USER>/6.png" },
    { img: "/image/home/<USER>/7.png" },
    { img: "/image/home/<USER>/8.png" },
    { img: "/image/home/<USER>/9.png" },
    { img: "/image/home/<USER>/10.png" },
];

export default function Partner() {
    // 复制数据多次以确保无缝轮播
    const duplicatedData = [...DATA, ...DATA, ...DATA];
    
    return (
        <div className="box-border w-full py-8 md:py-10 bg-[#f7f7f7]">
            <Marquee speed={40} gradient={false} pauseOnHover={true} loop={0} play={true}>
                {duplicatedData.map((item, index) => (
                    <div key={index} className="flex-shrink-0 mx-6 md:mx-10">
                        <SEOOptimizedImage
                            src={item.img}
                            quality={100}
                            // unoptimized
                            alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} Partner ${(index % DATA.length) + 1}`}
                            className={`h-[32px] max-md:h-[24px] ${(index % DATA.length) === 6 ? "h-[55px] max-md:h-[40px]" : ""} w-auto object-contain duration-300`}
                            width={120}
                            height={40}
                        />
                    </div>
                ))}
            </Marquee>
        </div>
    );
}