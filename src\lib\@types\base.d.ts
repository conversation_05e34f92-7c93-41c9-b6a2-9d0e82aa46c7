import { BuilderRenderState } from "@builder.io/sdk-react-nextjs/types/esm/context/types";
import { BuilderBlock } from "@builder.io/sdk-react-nextjs";
import {
	BuilderComponentsProp,
	BuilderLinkComponentProp,
} from "@builder.io/sdk-react-nextjs/types/cjs/types/builder-props";
import { BuilderContextInterface } from "@builder.io/sdk-react-nextjs/types/cjs/context/types";
import { Channel } from "@/lib/@types/api/channel";
import { TypedDocumentString } from "@/gql/graphql";

export type TBaseResponse<T extends any> = {
	code: number;
	msg: string;
	data: T;
};

export interface IBuilderRenderState extends BuilderRenderState, BuilderContextInterface {
	rootState: {
		languageMapChannel: {
			languageMapChannel: Channel.ILanguageMapChannel;
			channelLanguageMap: Channel.IChannelMap["detail"]["channel_language_mapping"];
			channelList: Channel.ILanguageMapChannel;
		};
		locale: string;
	};
}

export interface IBuilderContext {
	builderContext: IBuilderRenderState;
}

export interface IBuilderProps extends IBuilderContext, BuilderComponentsProp, BuilderLinkComponentProp {
	builderBlock: BuilderBlock;
}

export type TFilterServerProps<T> = Omit<T, "builderComponents" | "builderLinkComponent">;

/*************************************************************************/
// 这面这两个用于推到gql的ts类型
/* VariablesType<typeof ProductCardListDocument> */
export type VariablesType<D> = D extends TypedDocumentString<any, infer V> ? V : never;

export type DataType<D> = D extends TypedDocumentString<infer R, any> ? R : never;
/*************************************************************************/

export interface LabelValueItem<V extends string | undefined = string> {
	label: string;
	value: V;
}

export interface KeyValueItem {
	key: string;
	value: string;
}

export interface MyPageProps {
	params: {
		page: string[];
		locale: string;
		slug: string;
	};
	searchParams: Record<string, string>;
}

export type SlugMyPageProps = MyPageProps & {
	params: {
		slug: string;
	};
};
