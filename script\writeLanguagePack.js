import path from 'path'
import fs from 'fs'
import pkg from '@next/env';
const { loadEnvConfig } = pkg;
import { dirname } from 'path';
import { fileURLToPath } from "node:url";


const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// const fetch = require("node-fetch");  // 使用 require 导入 node-fetch 模块

// 加载环境变量配置
loadEnvConfig(process.cwd());

// 定义语言数据保存的路径
const savePath = path.join(__dirname, "../language.json");

/**
 * 异步获取语言列表并保存
 * 该函数首先从环境变量中获取配置的URL和凭证，然后通过fetch API获取语言列表和对应的国旗图标数据，
 * 最后将处理后的语言列表保存到本地文件中
 */
export const getLangs = async () => {
	try {
		// 构造请求语言列表的URL
		const baseUrl = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/translation/language_list`;
		// 从环境变量中获取用户名和令牌
		const username = process.env.NEXT_PUBLIC_SOFT_USERNAME;
		const ml_token = process.env.NEXT_PUBLIC_SOFT_TRANSLATE_MLTOKEN;

		// 发起获取语言列表的请求
		const res = await fetch(baseUrl, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				username,
				ml_token,
			},
		}).then((r) => r.json());

		// 如果获取语言列表成功，则处理数据
		if (res.code === 200) {
			let list = res.detail.language_list;
			// 提取语言代码
			const filterCode = list.map((item) => item.code);
			// 构造请求国旗图标的URL
			let flagBaseUrl = `${process.env.NEXT_PUBLIC_TRANSLATE_URL}/api/locale_map_flag`;
			flagBaseUrl = flagBaseUrl + `?code=${filterCode.join(",")}`;

			// 发起获取国旗图标的请求
			const flagIcons = await fetch(flagBaseUrl).then((r) => r.json());
                        await uploadLangPack(filterCode)


			// 如果获取国旗图标成功，则合并数据并保存
			if (flagIcons.code === 200) {
				list = list.map((item) => {
					item.flag = flagIcons.data[item.code];
					return item;
				});
				// 将处理后的语言列表保存到文件
				fs.writeFileSync(savePath, JSON.stringify(list));
			}
		}
	} catch (e) {
		// 异常处理：打印错误并抛出
		console.error("写入语言失败：", e);
		throw e;
	}
};

// 导出函数以便在其他地方调用
export const uploadLangPack = async (filterCode) => {
	try {
		console.log(`--------------开始翻译本地文件,翻译语言：`,filterCode);

		const url=`${process.env.NEXT_PUBLIC_AI_URL}/api/v1/ml_web/dict_upload_translate`
		const infoDict = {};
		const langDir = path.join(process.cwd(), 'src/lib/lang');
		const files = fs.readdirSync(langDir);
		for (const file of files) {
			if (file.endsWith('.json')) {
				const filePath = path.join(langDir, file);
				const fileContent = fs.readFileSync(filePath, 'utf-8');
				Object.assign(infoDict,JSON.parse(fileContent))
			}
		}
		// const info_dict = fs.readFileSync(path.join(__dirname, "../src/lib/lang/en.json"), "utf-8");
		const res = await fetch(url, {
			method:'POST',
			headers: {
				'Content-Type': 'application/json', // 指定请求体的格式为JSON
			},
			body:JSON.stringify({
				language_list:filterCode,
				info_dict: infoDict,
				domain: new URL(process.env.NEXT_PUBLIC_SITE_URL).hostname
			})
		}).then((r) => r.json());
		console.log('翻译结果',res);
if(res.status===200){
	console.log('--------------翻译本地文件结束-----------');
	return 'ok'
}
	} catch (e) {

		console.log("上传语言包Error:", e);
		throw e
	}
};

