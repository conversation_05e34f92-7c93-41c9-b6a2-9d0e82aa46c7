"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";

import { useEffect, useState } from "react";
import { useTranslations,useLocale } from "next-intl";
import { Link } from "@/navigation";
import { observeElementIntersection } from "@/lib/utils/util";
import React from "react";
import Indicator from "../Indicator";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { defaultLocale } from "@/config";
const spaceBetween = 0;
const slidesPerView = 1;



const Banner1 = ({ isActive }) => {
	const t = useTranslations("banner");
	const [key, setKey] = useState(0);
	const locale = useLocale();
	useEffect(() => {
		if (isActive) {
			setKey(prev => prev + 1);
		}
	}, [isActive]);

	return (
		<div className="relative">
			<SEOOptimizedImage quality={100} src={"/image/home/<USER>/quasar.png"} unoptimized alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} Home Banner 1`} priority className="w-full  object-cover" width={1920} height={700} />
			<motion.div
				key={key}
				initial="hidden"
				animate="visible"
				variants={containerVariants}
				className="absolute inset-0 flex flex-col left-[50%]  justify-center text-white pr-4"
			>
				<motion.h1 variants={itemVariants} className={`text-xl md:text-2xl 2xl:text-6xl  ${locale === defaultLocale ? "ib" : "font-semibold"} uppercase tracking-wider max-sm:text-base`}>
					{t("rallyToDaily")}
				</motion.h1>
				{/* <motion.h2 variants={itemVariants} className="text-xl md:text-2xl  xl:text-5xl lg:text-3xl  uppercase tracking-wider 2xl:mt-8 mt-2 max-sm:text-base">
					{t("with")}
				</motion.h2> */}
				<motion.div variants={itemVariants} className="mt-10 max-2xl:mt-8 max-sm:mt-4">
					<Link href="/products/paddle" className="bg-[#83c000] text-white  rounded irs py-3 px-6 max-sm:py-2 max-sm:px-4 hover:bg-lime-500 transition-colors duration-300 text-sm hover:text-white hover:bg-opacity-80">
						{t("Shopnow")}
					</Link>
				</motion.div>
			</motion.div>
		</div>
	);
};
const Banner2 = ({ isActive }) => {
	const t = useTranslations("banner");
	const [key, setKey] = useState(1);

	useEffect(() => {
		if (isActive) {
			setKey(prev => prev + 1);
		}
	}, [isActive]);
	return (
		<div className="relative">
			<SEOOptimizedImage quality={100} src={"/image/home/<USER>/no-compress/2-new-33.png"} unoptimized alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} Home Banner 1`} priority className="w-full  object-cover" width={1920} height={700} />
			<motion.div
				key={key}
				initial="hidden"
				animate="visible"
				variants={containerVariants}
				className="absolute inset-0 flex flex-col justify-center text-white"
			>
				<motion.div variants={itemVariants} className="absolute 1921xl:left-[33%] 2xl:bottom-[13%] 2xl:left-[31%] md:bottom-[10%] md:left-[30%] max-lg:left-[5%] max-lg:bottom-[50%]">
					<Link href="/products/ip-collection" className="bg-[#83c000] text-white  rounded irs py-3 px-6 max-sm:py-2 max-sm:px-4 hover:bg-lime-500 transition-colors duration-300 text-sm hover:text-white hover:bg-opacity-80">
						{t("Shopnow")}
					</Link>
				</motion.div>
			</motion.div>
		</div>
	);
};

const Banner3 = ({ isActive }) => {
	const t = useTranslations("banner");
	const [key, setKey] = useState(2);
	const locale = useLocale();
	useEffect(() => {
		if (isActive) {
			setKey(prev => prev + 1);
		}
	}, [isActive]);
	return (
		<div className="">
			<SEOOptimizedImage quality={100} src={"/image/home/<USER>/no-compress/usa.png"} unoptimized alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} Home Banner 3`} priority className="w-full  object-cover" width={1920} height={700} />
			<motion.div
				key={key}
				initial="hidden"
				animate="visible"
				variants={containerVariants}
				className="absolute inset-0 flex flex-col left-[50%]  justify-center text-white pr-4"
			>
				<motion.h1 variants={itemVariants} className={`text-xl  ${locale === defaultLocale ? "ib" : "font-semibold"} md:text-2xl 2xl:text-6xl  uppercase tracking-wider max-sm:text-base`}>
					{t("USAP_APPROVED_PADDLES")}
				</motion.h1>
				<motion.div variants={itemVariants} className="mt-10 max-2xl:mt-8 max-sm:mt-4">
					<Link href="/products/usap-approved" className="bg-[#83c000] text-white rounded irs py-3 px-6 max-sm:py-2 max-sm:px-4  hover:bg-lime-500 transition-colors duration-300 text-sm hover:text-white hover:bg-opacity-80">
						{t("Shopnow")}
					</Link>
				</motion.div>
			</motion.div>
		</div>
	);
};

const Banners = [Banner1, Banner2, Banner3];

function HomeBanner() {
	const [activeIndex, setActiveIndex] = useState(0);

	return (
		<div className="banner relative">
			<Swiper
				modules={[Navigation, Autoplay, Pagination]}
				className="max-h-[1500px] relative  overflow-hidden  max-lg:!h-auto"
				spaceBetween={spaceBetween}
				slidesPerView={slidesPerView}
				navigation={false}
				loop={true}
				onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
				pagination={{
					clickable: true,
					bulletActiveClass: "swiper-pagination-bullet-active",
					bulletClass: "swiper-pagination-bullet",
				}}
				autoplay={{
					delay: 5000,
					disableOnInteraction: false,
				}}
			>
				{Banners.map((Banner, index) => (
					<SwiperSlide key={index}>
						<Banner isActive={activeIndex === index} />
					</SwiperSlide>
				))}
			</Swiper>
		</div>
	);
}
export default React.memo(HomeBanner);
