"use client";
import { Pagination, PaginationProps } from "antd";
import React from "react";

const SearchPagination = ({ page, total, ChangePagination }) => {
	const paginationProps: PaginationProps = {
		current: page,
		onChange: (page) => {
			ChangePagination(page);
		},
		defaultCurrent: 1,
		total: total,
		showSizeChanger: false,
	};

	return (
		<div className="flex w-full justify-center">
			<Pagination {...paginationProps} />
		</div>
	);
};

export default SearchPagination;
