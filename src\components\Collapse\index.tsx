"use client";
import React, { ReactElement, ReactNode, useEffect, useRef } from "react";

import styles from "./index.module.css";
function Collapse({ children, open }: { children: ReactNode; open: boolean }) {
  const ref = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    updateSize();
  }, [open]);
  const updateSize = () => {
    if (!!ref.current && !!contentRef.current) {
      ref.current.style.setProperty("height", `${open ? contentRef.current.offsetHeight : 0}px`);
    }
  };
  return (
    <>
      <div ref={ref} className={`${styles.collapse} ${open ? styles.show : styles.hidden}`}>
        <div ref={contentRef} className="">
          {children}
        </div>
      </div>
    </>
  );
}
export default Collapse;
