"use client"
import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage'
import { Link, usePathname } from '@/navigation'
import { HomeTaile } from '../Contact/ConcatPage'
import { useTranslations, useLocale } from 'next-intl'
import Image from 'next/image'
import { defaultLocale } from '@/config'
interface BreadcrumbBannerProps {
  // 图片相关
  backgroundImage?: string
  productImage?: string
  imageAlt?: string

  // 文案相关
  title: string
  description?: string
  textPosition?: 'left' | 'right' | 'center'

  // 样式相关
  className?: string
  containerClassName?: string
  textClassName?: string

  // 其他选项
  showBreadcrumb?: boolean
  breadcrumbItems?: Array<{ label: string; href?: string }>
}

export default function BreadcrumbBanner({
  backgroundImage = "",
  productImage,
  imageAlt,
  title,
  description,
  textPosition = 'left',
  className = '',
  containerClassName = '',
  textClassName = '',
  showBreadcrumb = false,
  breadcrumbItems = []
}: BreadcrumbBannerProps) {

  // 优化的文字入场动画
  const containerAnimation = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  }

  const titleAnimation = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const descriptionAnimation = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay: 0.1,
        ease: "easeOut"
      }
    }
  }
  const pathname = usePathname()
  const t = useTranslations()
  const locale = useLocale();
  const [bannerImage, setBannerImage] = useState(backgroundImage)
  const [isDefault, setIsDefault] = useState(false);
  const [dynamicTextPosition, setDynamicTextPosition] = useState(textPosition);
  const [dynamicDescription, setDynamicDescription] = useState(description);
  const [showTitle, setShowTitle] = useState(true);
  const [objectPosition, setObjectPosition] = useState("center center");
  const [dynamicTextClassName, setDynamicTextClassName] = useState(textClassName);
  const [textOffset, setTextOffset] = useState(0);

  useEffect(() => {
    // 如果已经提供了自定义背景图片，则直接使用
    if (backgroundImage) {
      setBannerImage(backgroundImage)
      setIsDefault(false);
      setDynamicTextPosition(textPosition);
      setDynamicDescription(description);
      setDynamicTextClassName(textClassName);
      setTextOffset(0);
      setShowTitle(true);
      return
    }

    // 检查当前路径是否为产品分类页面
    const productsPathMatch = pathname.match(/\/products\/([^/]+)/)

    if (productsPathMatch && productsPathMatch[1]) {
      const category = productsPathMatch[1]
      // 根据不同的产品分类返回不同的图片和位置
      const categoryMap = {
        'paddle': {
          image: '/image/bread/no-compress/p-all.png',
          textPosition: 'left' as 'left' | 'right' | 'center',
          description: t("base.paddle"),
        },
        'axis-series': {
          image: '/image/bread/no-compress/axis.png',
          textPosition: 'left' as 'left' | 'right' | 'center',
          description: t("base.axis"),
          textClassName: "2xl:pb-[100px]",
        },
        'ip-collection': {
          image: '/image/bread/ip-collection.png',
        },
        'nova-series': {
          image: '/image/bread/nova-series.png',
          textPosition: 'left' as 'left' | 'right' | 'center',
          description: t("base.nova")
        },
        'quasar-series': {
          image: '/image/bread/no-compress/quasar.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
          description: t("base.quasar"),
        },
        'pulse-series': {
          image: '/image/bread/no-compress/pulsehf.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
          description: t("base.pulse"),
        },
        'usap-approved': {
          image: '/image/bread/usap-approved.png',
          textPosition: 'left' as 'left' | 'right' | 'center',
          description: t("base.usap")
        },
        'ball': {
          image: '/image/bread/no-compress/pickleball.png',
        },
        'apparel': {
          image: '/image/bread/no-compress/apparel.png',
        },
        'accessories': {
          image: '/image/bread/no-compress/ACCESSORIES.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
        },
        'paddle-accessories': {
          image: '/image/bread/no-compress/paddle accessories banner.png',
          textPosition: 'left' as 'left' | 'right' | 'center',
          textOffset: 140,
        },
        'eyewear': {
          image: '/image/bread/no-compress/eyewear banner.png',
          textPosition: 'left' as 'left' | 'right' | 'center',
          textOffset: 300,
        },
        'jewelry': {
          image: '/image/bread/no-compress/jewelry banner.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
          textOffset: -300,
        },
        'headwear': {
          image: '/image/bread/no-compress/headwear1.png',
          textPosition: 'left' as 'left' | 'right' | 'center',
          textOffset: 300,
        },
        'others': {
          image: '/image/bread/no-compress/others banner.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
          textOffset: -300,
        },
        'bags': {
          image: '/image/bread/no-compress/bags banner.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
          textOffset: -350,
        },
        'social-set': {
          image: '/image/bread/no-compress/SOCIAL SET banner.png',
          textPosition: 'center' as 'left' | 'right' | 'center',
          description: "Colorful, social, and expressive, this set is built for sharing the game with friends.Each paddle reflects a personality, inspired by MBTIʼs well- known types and the bold, dopamine- charged color trends you see everywhere."
        },
        'mens': {
          image: '/image/bread/no-compress/mens-apparel.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
          textOffset: -350,
        },
        'womens': {
          image: '/image/bread/no-compress/womens-apparel.png',
          textPosition: 'right' as 'left' | 'right' | 'center',
          textOffset: -350,
        },
      }

      // 如果找到匹配的分类
      if (categoryMap[category]) {
        setBannerImage(categoryMap[category].image)
        setDynamicTextPosition(categoryMap[category].textPosition || textPosition);
        setDynamicDescription(categoryMap[category].description || description);
        setDynamicTextClassName(categoryMap[category].textClassName || textClassName);
        setObjectPosition(categoryMap[category].objectPosition || 'center center');
        setTextOffset(categoryMap[category].textOffset || 0);
        setIsDefault(false);
        setShowTitle(category !== 'ip-collection');
      } else {
        // 默认设为空，使用渐变背景
        setBannerImage("")
        setDynamicTextPosition(textPosition);
        setDynamicDescription(description);
        setDynamicTextClassName(textClassName);
        setObjectPosition('center center');
        setTextOffset(0);
        setIsDefault(true);
        setShowTitle(true);
      }
    } else {
      setBannerImage("")
      setDynamicTextPosition(textPosition);
      setDynamicDescription(description);
      setDynamicTextClassName(textClassName);
      setObjectPosition('center center');
      setTextOffset(0);
      setIsDefault(true);
      setShowTitle(true);
    }
  }, [pathname, backgroundImage, textPosition, description, objectPosition])

  if (!bannerImage) {
    return <HomeTaile msg={title} />
  }

  return (
    <section className={`relative ${isDefault ? 'h-[225px] max-md:h-[205px]' : ""} overflow-hidden bg-gray-300 ${className}`}>
      {/* 背景图片 - 单独容器，不影响noscript */}
      {/* <div className="absolute inset-0 w-full h-full"> */}
      <SEOOptimizedImage
        src={bannerImage}
        alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} ${title} Banner`}
        width={1920}
        height={600}
        className={`w-full h-auto ${dynamicDescription ? 'max-md:min-h-[200px]' : ''} object-cover`}
        priority
        unoptimized
        quality={100}
        imageStyle={{
          objectPosition: objectPosition
        }}
      />
      {/* 遮罩层 */}
      {/* <div className="absolute inset-0 bg-black/40"></div> */}
      {/* </div> */}

      {/* 内容区域 */}
      <div className={`container h-full ${containerClassName} absolute inset-0`}>
        <div className={`grid grid-cols-1 gap-8 items-center h-full`}>

          {/* 文字内容 - 独立动画容器，垂直居中 */}
          <motion.div
            className={`flex flex-col h-full justify-center ${isDefault ? 'items-center' : (dynamicTextPosition === 'right' ? 'items-end' : (dynamicTextPosition === 'center' ? 'items-center' : 'items-start'))}  ${dynamicTextClassName}`}
            variants={containerAnimation}
            initial="hidden"
            animate="visible"
          >
            <div>
              {/* 标题 */}
              {showTitle && (
                <h1
                  className={`text-3xl md:text-4xl  lg:text-5xl mb-4  ${locale === defaultLocale ? "ib" : "font-semibold"} leading-tight uppercase ${isDefault ? 'text-black' : 'text-white'} ${dynamicTextPosition === 'center' ? 'text-center' : ''} ${textOffset ? '2xl:transform 2xl:translate-x-[var(--text-offset)]' : ''}`}
                  style={{
                    '--text-offset': textOffset ? `${textOffset}px` : undefined
                  } as React.CSSProperties}
                // variants={titleAnimation}
                // initial="hidden"
                // animate="visible"
                >
                  {title}
                </h1>
              )}


              {/* 描述 */}
              {dynamicDescription && (
                <motion.p
                  className={`text-base leading-relaxed max-w-[600px] irs ${isDefault ? 'text-black text-center' : 'text-white'} ${dynamicTextPosition === 'center' ? 'text-center' : ''}`}
                  variants={descriptionAnimation}
                  initial="hidden"
                  animate="visible"
                >
                  {dynamicDescription}
                </motion.p>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}