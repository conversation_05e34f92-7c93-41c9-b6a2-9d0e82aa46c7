"use client";

import React, { useEffect, useState } from "react";
// import Image from 'next/image';
import * as Icon from "@phosphor-icons/react/dist/ssr";
import { useLocale, useTranslations } from "next-intl";
import { redirect } from "next/navigation";
import FlagIcon from "../../Flag/index";
import { SITE_LOCALES } from "@/lib/constant";
import { type TLocale } from "@/lib/@types/locale";
import { defaultLocale } from "@/config";
import { Link } from "@/navigation";
import { LinkedinLogo, TiktokLogo } from "@phosphor-icons/react";
import { SocialList } from "@/components/Social/FollowUs";
import Image from "next/image";
import Search from "../Menu/search";
import { useRouter } from "next/navigation";
import NavUser from "@/components/User/nav-user";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import CountBadge from "@/components/Badge/count-badge";
// import CartDrawer from "@/components/Product/ShoppingCart/cartDrawer"; // 注释掉原有的抽屉组件
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { getInquiry } from "@/lib/utils/util";
import { useLoveStore } from "@/lib/store/love.store";
import { menus } from "@/lib/menu";
import clsx from "clsx";
import useMenuMobile from "@/store/useMenuMobile";
import { contactInfo, contactObj } from "@/lib/contacts";
import Translate from "@/components/Translate";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
// Import Swiper styles
import "swiper/css";
import Svg from "../Menu/Svg";
import { useCompareStore } from "@/lib/store/Compare.store";
import MobileContact from "@/components/MobileContact";
import MobileLogin from "@/components/MobileContact/MobileLogin";
import { executeGraphQL } from "@/lib/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";
import { ProductCategoriesDocument, ProductCategoriesQuery } from "@/gql/graphql";
// 引入Ant Design图标和组件
import { XOutlined, FacebookOutlined, InstagramFilled, YoutubeFilled, LinkedinFilled, TwitterOutlined, TikTokOutlined } from "@ant-design/icons";
import { Collapse } from "antd";
// 定义缓存数据类型接口
interface CategoryCache {
	data: {
		edges: Array<{
			node: {
				id: string;
				slug: string;
				name: string;
				translation?: {
					name: string;
				};
				children?: {
					edges: Array<{
						node: {
							id: string;
							slug: string;
							name: string;
							translation?: {
								name: string;
							};
						}
					}>;
				};
			}
		}>;
		length?: number;
	};
	timestamp: number;
}

interface Props {
	props: string;
	slogan?: string;
}

// 添加骨架屏组件
const CategorySkeleton = () => {
	const t = useTranslations("menu");
	const locale = useLocale()
	const category = [{
		id: "1001",
		slug: "paddle",
		name: t("paddle")
	},
	{
		id: "1002",
		slug: "balls",
		name: t("balls")
	},
	{
		id: "1003",
		slug: "apparel",
		name: t("apparel")
	},
	{
		id: "1004",
		slug: "accessories",
		name: t("accessories")
	},
	];
	return (
		<>
			{/* {[1, 2, 3].map((item) => (
				<li className="group h-full" key={`skeleton-${item}`}>
					<div className="flex h-full items-center justify-center text-[18px] text-white duration-300 max-lg:text-[17px]">
						<div className="h-5 w-20 rounded bg-gray-200"></div>
					</div>
				</li>
			))} */}
			{
				category.map((item) => (
					<li className={`group h-full transition-opacity duration-300`} key={item.id}>
						<Link
							href={`/products/${item.slug}`}
							className="flex h-full items-center justify-center text-[18px] text-white duration-300 max-lg:text-[17px] ib "
						>
							<div
								title={item.name}
								className={clsx(
									"text-center text-[16px] hover:text-main text-[#282828] uppercase"
								)}
							>
								{item.name}
							</div>
						</Link>
					</li>
				))
			}
		</>
	);
};

// 添加移动端骨架屏组件
const MobileCategorySkeleton = () => {
	const t = useTranslations("menu");
	const locale = useLocale()
	const category = [{
		id: "1001",
		slug: "paddle",
		name: t("paddle")
	},
	{
		id: "1002",
		slug: "balls",
		name: t("balls")
	},
	{
		id: "1003",
		slug: "apparel",
		name: t("apparel")
	},
	{
		id: "1004",
		slug: "accessories",
		name: t("accessories")
	},
	];
	return (
		<>
			{/* {[1, 2, 3].map((item) => (
				<li className="mb-1 h-full" key={`mobile-skeleton-${item}`}>
					<div className="box-border flex justify-start border-b-[1px] border-[#e5e7eb] p-2">
						<div className="h-5 w-24 rounded bg-gray-200"></div>
					</div>
				</li>
			))} */}
			{
				category.map((item) => (
					<li className="mb-1 h-full transition-opacity duration-300" key={item.id}>
						<Link
							href={`/products/${item.slug}`}
							className={`box-border ${locale === defaultLocale ? "ib" : "font-semibold"}   flex justify-start border-b-[1px] border-[#e5e7eb] p-2 text-black hover:text-black duration-300 uppercase`}
						>
							<span className="">{item.name}</span>
						</Link>
					</li>
				))
			}
		</>
	);
};

const TopNavOne: React.FC<Props> = ({ props, slogan }) => {
	const { findCheckout } = useShoppingCart();
	const t = useTranslations();
	const [currentSlug, setCurrentSlug] = useState("");
	const locale = useLocale();
	const { openMenuMobile, handleMenuMobile } = useMenuMobile();

	//喜欢
	const [loveCount, setLoveCount] = useState(0);
	const { loveIds } = useLoveStore();
	//对比
	const [CompareCount, setCompareCount] = useState(0);
	let { compareIds } = useCompareStore()
	// 购物车
	const { cartList } = useShoppingCartStore();
	const [count, setCount] = useState(0);
	const [Inquirycount, setInquirycount] = useState(0);
	//是否变色
	const [isVisible, setIsVisible] = useState<boolean>(false);

	useEffect(() => {
		// 1. 刷新购物车数据
		findCheckout("default-channel");

		const handleResize = () => {
			let Inquirydata = getInquiry();
			setInquirycount(Inquirydata.length);
		};
		handleResize();
		window.addEventListener("storage", handleResize);
		const handleScroll = () => {
			const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

			if (currentSlug == "/") {
				// 在首页，根据滚动位置设置 isVisible
				if (scrollTop > 230) {
					setIsVisible(true);
				} else {
					setIsVisible(false);
				}
			} else {
				setIsVisible(true);
			}
		};

		window.addEventListener("scroll", handleScroll);
		// Cleanup function to remove event listener
		return () => {
			window.removeEventListener("storage", handleResize);
			window.removeEventListener("scroll", handleScroll);
		};
	}, [currentSlug]);

	const currentLocales: TLocale[] = SITE_LOCALES;

	let router = useRouter();

	useEffect(() => {
		setCount(cartList?.lines?.length || 0);
	}, [cartList]);

	const getFlag = (code: string) => {
		return SITE_LOCALES.find((item) => {
			return item.code === code;
		})!.flag;
	}

	useEffect(() => {
		setLoveCount(() => {
			return loveIds.length > 99 ? 99 : loveIds.length;
		});
		setCompareCount(() => {
			return compareIds.length > 99 ? 99 : compareIds.length;
		});
	}, [loveIds, compareIds]);



	const GoToPage = (url: string) => {
		router.push(url);
		handleMenuMobile();
		// setExpandedCategory(null); // 重置展开状态
	};

	const [categories, setCategories] = useState<any>([]);
	const [categoriesLoaded, setCategoriesLoaded] = useState(false);
	const [loadingCategories, setLoadingCategories] = useState(true); // 添加加载状态

	// 从本地存储获取缓存的分类数据
	useEffect(() => {
		let isMounted = true;
		let hasCache = false; // 标记是否有缓存数据
		// 先尝试从本地存储获取缓存的分类，用于快速显示
		try {
			const cachedData = localStorage.getItem('category_cache');
			if (cachedData) {
				const { data, timestamp } = JSON.parse(cachedData) as CategoryCache;
				// 检查缓存是否存在（不限制过期时间，始终先使用缓存快速渲染）
				if (data && data.edges && data.edges.length > 0) {
					setCategories(data);
					setCategoriesLoaded(true);
					setLoadingCategories(false); // 有缓存数据时，立即设置为非加载状态
					hasCache = true; // 标记已有缓存数据
				}
			}
		} catch (error) {
			console.error("Error loading cached categories:", error);
		}

		// 无论是否有缓存，都从服务器获取最新数据
		const fetchCategory = async () => {
			// 只有在没有缓存数据时，才设置加载状态为true
			if (!hasCache) {
				setLoadingCategories(true);
			}
			try {
				const result = await executeGraphQL(ProductCategoriesDocument, {
					withAuth: false,
					variables: { locale: handleGraphqlLocale(locale || defaultLocale), first: 10 },
				});
				if (result && result.categories && isMounted) {
					// 检查数据是否发生变化
					const newData = result.categories;
					const currentData = JSON.stringify(newData);
					const oldData = localStorage.getItem('category_cache')
						? JSON.stringify((JSON.parse(localStorage.getItem('category_cache')!) as CategoryCache).data)
						: '';

					// 如果数据有变化，更新UI
					if (currentData !== oldData) {
						setCategories(newData);
					}

					// 更新缓存
					try {
						localStorage.setItem('category_cache', JSON.stringify({
							data: newData,
							timestamp: Date.now()
						} as CategoryCache));
					} catch (error) {
						console.error("Error caching categories:", error);
					}
				}
			} catch (error) {
				console.error("Failed to fetch categories:", error);
			} finally {
				if (isMounted) {
					setCategoriesLoaded(true);
					setLoadingCategories(false);
				}
			}
		};
		fetchCategory();

		// 清理函数
		return () => {
			isMounted = false;
		};
	}, []);
	return (
		<>
			<div className={`top-nav`}>
				<div className={clsx("w-full bg-white shadow-sm")}>
					<div className="container box-border h-[90px] max-xl:hidden">
						<div className=" flex h-full items-center justify-between text-white ">
							<div
								className={clsx(
									"hidden max-xl:block max-md:hidden text-[#282828]"
								)}
							>
								<div className=" menu-mobile-icon flex items-center " onClick={handleMenuMobile}>
									<i className="ri-align-left text-2xl "></i>
									{/* <em className="text-blackmx-2 mx-2 ">{t("nav.MENU")}</em> */}
								</div>
							</div>

							<div className="max-xl:hidden  max-lg:flex max-lg:justify-center max-xl: flex items-center ">
								<Link href={"/"} className="">
									<Image
										src="/image/logo-r.png"
										width={1000}
										height={1000}
										alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} logo`}
										className="object-contain w-[200px] h-[90px]"
										priority
										unoptimized
									></Image>
								</Link>
							</div>
							<nav className="h-full max-xl:hidden">
								<ul className="flex h-full items-center gap-x-5 2xl:gap-x-10">
									{menus.map((item) => {
										if (item.id === 1 && item.show) { // 如果是Home菜单项
											return (
												<React.Fragment key={item.id}>
													<li className={`group h-full `}>
														<Link
															href={item.link}
															className="flex h-full items-center  justify-center text-[18px] text-white duration-300 max-lg:text-[17px] "
														>
															<div
																title={t(item.name)}
																className={clsx(
																	`text-center text-[16px] ${locale === defaultLocale ? "ib" : "font-semibold"}  hover:text-main text-[#282828] uppercase relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-right after:scale-x-0 after:transform after:bg-main after:transition-transform group-hover:after:origin-left group-hover:after:scale-x-100 duration-300`
																)}
															>
																{t(item.name)}
															</div>
														</Link>
													</li>

													{/* 分类加载中显示骨架屏 - 只在无缓存且加载中时显示 */}
													{loadingCategories && !categoriesLoaded && <CategorySkeleton />}

													{/* 分类菜单 - 支持二级分类下拉 */}
													{categoriesLoaded && categories && categories.edges && categories.edges.length > 0 &&
														categories.edges.slice(0, 4).map((category) => {
															const hasChildren = category.node.children && category.node.children.edges && category.node.children.edges.length > 0;

															return (
																<li className={`group h-full transition-opacity duration-300 relative`} key={category.node.id}>
																	<Link
																		href={`/products/${category.node.slug}`}
																		className="flex h-full items-center justify-center text-[18px] text-white duration-300 max-lg:text-[17px]"
																	>
																		<div
																			title={category.node.name}
																			className={clsx(
																				`text-center text-[16px] ${locale === defaultLocale ? "ib" : "font-semibold"}  hover:text-main text-[#282828] flex items-center gap-1 uppercase relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-right after:scale-x-0 after:transform after:bg-main after:transition-transform group-hover:after:origin-left group-hover:after:scale-x-100 duration-300`
																			)}
																		>
																			{locale === defaultLocale ? category.node.name : (category.node.translation?.name || category.node.name)}
																			{hasChildren && (
																				<svg className="h-3 w-3 text-gray-500 transition-transform duration-200 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
																					<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
																				</svg>
																			)}
																		</div>
																	</Link>

																	{/* 二级分类下拉菜单 */}
																	{hasChildren && (
																		<div className="absolute left-0 top-full z-50 min-w-[200px] bg-white shadow-lg border border-gray-200  opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
																			<div className="">
																				{category.node.children.edges.map((child) => (
																					<Link
																						key={child.node.id}
																						href={`/products/${child.node.slug}`}
																						className={`block  ${locale === defaultLocale ? "ib" : "font-semibold"}  px-4 py-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-main transition-colors duration-200`}
																					>
																						{locale === defaultLocale ? child.node.name : (child.node.translation?.name || child.node.name)}
																					</Link>
																				))}
																			</div>
																		</div>
																	)}
																</li>
															);
														})
													}
												</React.Fragment>
											);
										}

										// 其他菜单项正常显示
										return (
											item.show && (
												<li className={`group h-full `} key={item.id}>
													<Link
														href={item.link}
														className="flex h-full items-center  justify-center text-[18px] text-white duration-300 max-lg:text-[17px] "
													>
														<div
															title={t(item.name)}
															className={clsx(
																`text-center text-[16px] ${locale === defaultLocale ? "ib" : "font-semibold"}  hover:text-main text-[#282828] uppercase relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-right after:scale-x-0 after:transform after:bg-main after:transition-transform group-hover:after:origin-left group-hover:after:scale-x-100 duration-300`
															)}
														>
															{t(item.name)}
														</div>
													</Link>
												</li>
											)
										);
									})}
								</ul>
							</nav>

							<div className=" flex items-center justify-end gap-2">
								{/* 搜索 */}
								<Search isVisible={isVisible} />

								{/* 用户 */}
								<div className="c-flex cursor-pointer  px-2 max-lg:hidden">
									<NavUser>
										<div className="c-flex h-[42px]  rounded-full ">
											<div className="s-flex gap-x-2 text-black">
												<i
													className={clsx(
														"ri-user-line  !text-xl text-[#282828] hover:text-black",
													)}
												></i>
											</div>
										</div>
									</NavUser>
								</div>

								{/* 对比 */}
								{/* <div className="max-lg:hidden">
									<Link
										href={"/compare"}
										className="c-flex relative group  h-[42px] w-[42px]  rounded-full  text-black hover:!text-mainColor"
									>
										<Svg isScrolled={true} ishead={true} />
										{!!CompareCount && (
											<span className="c-flex absolute right-0 top-0 h-[18px] w-[18px] rounded-full bg-[#d53a3d] text-[10px] text-white">
												{CompareCount}
											</span>
										)}
									</Link>
								</div> */}

								{/* /爱心/ */}
								{/* <li className="c-flex cursor-pointer max-lg:!hidden">
									<Link
										href={"/collection"}
										className="c-flex relative  h-[42px] w-[42px]  rounded-full  text-black hover:!text-black"
									>
										<i
											className={clsx(
												"ri-heart-3-line ri-xl hover:!text-mainColor",
												!isVisible ? "!text-white" : "!text-block",
											)}
										></i>
										{!!loveCount && (
											<span className="c-flex absolute right-0 top-0 h-[18px] w-[18px] rounded-full bg-[#d53a3d] text-[10px] text-white">
												{loveCount}
											</span>
										)}
									</Link>
								</li> */}
								<li className="c-flex cursor-pointer px-2 max-md:!hidden">
									<Link href={"/inquiry"} className="text-black ">
										<div className="c-flex   ">
											<i
												className={clsx(
													`ri-file-list-line ri-xl relative text-[#282828] hover:!text-main `
												)}
											>
												<CountBadge count={Inquirycount} />
											</i>
										</div>
									</Link>
								</li>

								{/* 购物车 - 修改为跳转到购物车页面 */}
								{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
									<li className="c-flex cursor-pointer px-2 max-md:!hidden">
										<Link href="/cart" className="c-flex">
											<div className="c-flex   ">
												<i
													className={clsx(
														`ri-shopping-bag-line ri-xl  relative text-[#282828] hover:text-black`,
													)}
												>
													<CountBadge count={count} />
												</i>
											</div>
										</Link>
									</li>
								)}

								{/* 原有的抽屉方式（已注释）
								{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
									<li className="c-flex cursor-pointer px-2 max-md:!hidden">
										<CartDrawer>
											<div className="c-flex   ">
												<i
													className={clsx(
														`ri-shopping-bag-line ri-xl  relative text-[#282828] hover:text-black`,
													)}
												>
													<CountBadge count={count} />
												</i>
											</div>
										</CartDrawer>
									</li>
								)}
								*/}
								<div className="max-xl:hidden">
									{process.env.NEXT_PUBLIC_IS_I18N == "true" && <Translate />}
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* 手机端 */}
				<div className="relative mx-auto hidden h-[64px] bg-white px-[16px] max-xl:block max-xl:!w-full">
					<div className="header-main flex h-full w-full items-center">
						{/* 左侧菜单按钮 */}
						<div className="menu-mobile-icon flex items-center xl:hidden cursor-pointer" onClick={handleMenuMobile}>
							<i className="ri-align-left text-2xl text-black"></i>
							{/* <em className="text-blackmx-2 mx-2 text-black">{t("nav.MENU")}</em> */}
						</div>

						{/* 居中的logo */}
						<div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
							<Link href={"/"} className="flex items-center !text-black">
								<Image
									src="/image/logo-r.png"
									width={200}
									height={42}
									alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} logo`}
									className="object-contain h-[54px]"
									priority
								></Image>
							</Link>
						</div>

						{/* 右侧搜索按钮 */}
						<div className="ml-auto flex items-center gap-2">
							<Search isVisible={isVisible} />
						</div>
					</div>
				</div>

				{/* 移动端菜单遮罩层 */}
				{openMenuMobile && (
					<div
						className="fixed inset-0 z-40 bg-black bg-opacity-50 xl:hidden"
						onClick={handleMenuMobile}
					/>
				)}
				{/* //手机端打开 */}
				<div
					id="menu-mobile"
					className={`relative box-border  bg-white pt-[30px] overflow-y-auto overflow-x-hidden p-3 ${openMenuMobile ? "open" : ""}`}
				>
					<div className="menu-container  w-full">
						<div className="menu-main h-full overflow-hidden w-full mb-3">
							<div className="heading relative  px-3 w-full">
								<div
									className="close-menu-mobile-btn bg-surface cursor-pointer flex h-6 w-6  items-center justify-center rounded-full"
									onClick={handleMenuMobile}
								>
									<i className="ri-close-line text-3xl text-black"></i>
								</div>
							</div>
							<div className="list-nav mt-6">
								<ul>
									{menus.map((item) => {
										// 处理移动端菜单 - Home菜单项后插入分类
										if (item.id === 1 && item.show) {
											return (
												<React.Fragment key={item.id}>
													<li className="mb-1 h-full">
														<div
															onClick={() => GoToPage(item.link)}
															className={`box-border ${locale === defaultLocale ? "ib" : "font-semibold"}  flex justify-start border-b-[1px] border-[#e5e7eb] p-2 text-black duration-300 uppercase`}
														>
															<span>{t(item.name)}</span>
														</div>
													</li>

													{/* 移动端分类加载中显示骨架屏 - 只在无缓存且加载中时显示 */}
													{loadingCategories && !categoriesLoaded && <MobileCategorySkeleton />}

													{/* 移动端分类菜单 - 使用 Antd Collapse 组件 */}
													{categoriesLoaded && categories && categories.edges && categories.edges.length > 0 &&
														categories.edges.map((category) => {
															const hasChildren = category.node.children && category.node.children.edges && category.node.children.edges.length > 0;
															const categoryName = locale === defaultLocale ? category.node.name : (category.node.translation?.name || category.node.name);

															// 如果有子分类，使用 Collapse 组件
															if (hasChildren) {
																const collapseItems = [
																	{
																		key: category.node.id,
																		label: (
																			<span
																				onClick={(e) => {
																					e.stopPropagation();
																					GoToPage(`/products/${category.node.slug}`);
																				}}
																				className={`flex-1 cursor-pointer ${locale === defaultLocale ? "" : "font-semibold"} text-black hover:text-main transition-colors uppercase`}
																			>
																				{categoryName}
																			</span>
																		),
																		children: (
																			<div className="-mx-4 px-4 py-2">
																				{category.node.children.edges.map((child) => (
																					<div
																						key={child.node.id}
																						onClick={() => GoToPage(`/products/${child.node.slug}`)}
																						className={`py-4 pl-2 ${locale === defaultLocale ? "ib" : "font-semibold"}  text-main cursor-pointer hover:text-main transition-colors border-b border-gray-200 last:border-b-0 sans`}
																					>
																						<span>{locale === defaultLocale ? child.node.name : (child.node.translation?.name || child.node.name)}</span>
																					</div>
																				))}
																			</div>
																		),
																	},
																];

																return (
																	<li key={category.node.id} className="mb-1 h-full">
																		<div className="border-b-[1px] px-2 border-[#e5e7eb]">
																			<Collapse
																				items={collapseItems}
																				ghost
																				size="small"
																				expandIconPosition="end"
																				className="mobile-category-collapse"
																				style={{
																					backgroundColor: 'transparent',
																				}}
																			/>
																		</div>
																	</li>
																);
															}

															// 如果没有子分类，显示普通的分类链接
															return (
																<li key={category.node.id} className="mb-1 h-full">
																	<div
																		onClick={() => GoToPage(`/products/${category.node.slug}`)}
																		className={`box-border ${locale === defaultLocale ? "ib" : "font-semibold"} flex justify-start border-b-[1px] border-[#e5e7eb] p-2 text-black duration-300 cursor-pointer hover:bg-gray-50 transition-colors uppercase`}
																	>
																		<span>{categoryName}</span>
																	</div>
																</li>
															);
														})
													}
												</React.Fragment>
											);
										}

										// 其他菜单项正常显示
										return (
											item.show && (
												<li className="mb-1 h-full " key={item.id}>
													<div
														onClick={() => GoToPage(item.link)}
														className={`box-border  ${locale === defaultLocale ? "ib" : "font-semibold"}   flex justify-start border-b-[1px] border-[#e5e7eb] p-2 text-black duration-300 uppercase`}
													>
														<span>{t(item.name)}</span>
													</div>
												</li>
											)
										);
									})}
								</ul>
							</div>
						</div>



						<MobileContact GoToPage={GoToPage} handleMenuMobile={handleMenuMobile} />


					</div>
					<MobileLogin handleMenuMobile={handleMenuMobile} />
				</div>
			</div>
		</>
	);
};

export default TopNavOne;
