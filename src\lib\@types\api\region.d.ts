export declare namespace RegionType {
	interface BaseResp<T> {
		code: number;
		msg: string;
		status: string;
		result: T;
	}

	interface Country {
		name: string;
		isoCode: string;
		flag: string;
		phonecode: string;
		currency: string;
		latitude: string;
		longitude: string;
		timezones: {
			zoneName: string;
			gmtOffset: number;
			gmtOffsetName: string;
			abbreviation: string;
			tzName: string;
		}[];
	}

	interface Region {
		name: string;
		isoCode: string;
		countryCode: string;
		latitude: string;
		longitude: string;
	}
}
