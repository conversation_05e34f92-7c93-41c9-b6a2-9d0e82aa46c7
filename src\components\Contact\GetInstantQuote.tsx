"use client";
import { But<PERSON> } from "antd";
import { ArrowRightOutlined } from "@ant-design/icons";
import React from "react";
import { useTranslations } from "next-intl";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";

const GetInstantQuoteButton = ({ className, text,product }: { text?: React.ReactNode; className?: string;product?:any }) => {
	const t = useTranslations();
	const { openModalInquiry } = useModalInquiryContext();

  
	return (
		<div className={`flex justify-center ${className}`} >
			<Button
				size="large"
				type="primary"
				shape="round"
				className="group border  text-white hover:!text-black "
				onClick={() => openModalInquiry(false)}
			>
				{text || t("base.getInstantQuote")}
				<ArrowRightOutlined className="transform transition-all duration-700 group-hover:translate-x-2 " />
			</Button>
		</div>
	);
};

export default GetInstantQuoteButton;
