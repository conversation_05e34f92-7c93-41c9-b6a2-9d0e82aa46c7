"use client";

import { useState } from "react";
import {useRouter  } from "@/navigation";
import { SearchIcon, Loader2 } from "lucide-react";

export const SearchBar = ({ channel }: { channel: string }) => {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		const formData = new FormData(e.currentTarget);
		const search = formData.get("search") as string;
		
		if (search && search.trim().length > 0) {
			setIsLoading(true);
			router.push(`/search?query=${encodeURIComponent(search)}`);
		}
	};

	return (
		<form
			onSubmit={handleSubmit}
			className="group relative my-2 flex !w-full items-center justify-items-center text-sm lg:w-80"
		>
			<label className="w-full">
				<span className="sr-only">search for products</span>
				<input
					type="text"
					name="search"
					placeholder="Search for products..."
					autoComplete="on"
					required
					disabled={isLoading}
					className="h-10 w-full rounded-md border border-neutral-300 bg-transparent bg-white px-4 py-2 pr-10 text-sm text-black placeholder:text-neutral-500 focus:border-black focus:ring-black disabled:opacity-50"
				/>
			</label>
			<div className="absolute inset-y-0 right-0">
				<button
					type="submit"
					disabled={isLoading}
					className="inline-flex aspect-square w-10 items-center justify-center text-neutral-500 hover:text-neutral-700 focus:text-neutral-700 group-invalid:pointer-events-none group-invalid:opacity-80 disabled:opacity-50"
				>
					<span className="sr-only">search</span>
					{isLoading ? (
						<Loader2 className="h-5 w-5 animate-spin" />
					) : (
						<SearchIcon aria-hidden className="h-5 w-5" />
					)}
				</button>
			</div>
		</form>
	);
};
