'use client'
import { useState } from "react";
import CountUp from "react-countup";
import VisibilitySensor from "react-visibility-sensor";
export function CountUpNumber({ end ,className}: { end: string ,className?:string}) {
  const [isVisible, setIsVisible] = useState(false);

  const parsedEnd = parseInt(end.replace(/,/g, "")); // 移除逗号并转换为整数

  const onVisibilityChange = (isVisible: boolean) => {
    if (isVisible) {
      setIsVisible(true);
    } else {
      setIsVisible(false); // 重置 isVisible 状态
    }
  };

  return (
    <VisibilitySensor onChange={onVisibilityChange} delayedCall>
        <CountUp
          end={isVisible ? parsedEnd : 0}
          duration={2}
          separator="" // 移除分隔符
          formattingFn={(value) => value.toString()} // 自定义格式化函数
          className={className}
        />
    </VisibilitySensor>
  );
}