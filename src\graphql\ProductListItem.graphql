fragment ProductListItem on Product {
	id
	...ProductLocaleItem
	name
	slug
	rating
	pricing {
		priceRange {
			start {
				gross {
					amount
					currency
				}
			}
			stop {
				gross {
					amount
					currency
				}
			}
		}
	}
	variants {
		...ProductDetailVariantItem
	}
	attributes {
		attribute {
			name
			slug
			translation(languageCode: $locale) {
				name
			}
		}
		values {
			name
			value
			translation(languageCode: $locale) {
				name
			}
			inputType
			file {
				contentType
				url
			}
		}
	}
	descriptionJson
	media: metafield(key: "media")
	metadata {
		key
		value
	}
	translation(languageCode: $locale) {
		descriptionJson
		name
	}
	category {
		id
		...CategoryLocaleItem
		name
		slug
	}
	thumbnail(size: 1024, format: WEBP) {
		url
		alt
	}
}

fragment ProductDetailVariantItem on ProductVariant {
	quantityAvailable
	sku
	media: metafield(key: "media")
	weight {
		unit
		value
	}
	attributes {
		attribute {
			name
			translation(languageCode: $locale) {
				name
			}
		}
		values {
			inputType
			file {
				url
				contentType
			}
			name
			translation(languageCode: $locale) {
				name
			}
		}
	}
	pricing {
		price {
			tax {
				amount
				currency
			}
			net {
				amount
				currency
			}
			gross {
				amount
				currency
			}
		}
	}
	translation(languageCode: $locale) {
		name
	}
	name
	id
}
