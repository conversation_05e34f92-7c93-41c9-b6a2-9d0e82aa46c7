"use client";
import { useTranslations } from "next-intl";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useLocale } from "next-intl";
import { defaultLocale } from "@/config";
const collections = [
    {
        title: "IPCollectionRCxMeowzart",
        image: "/image/home/<USER>",
        href: "/products/ip-collection",
        className: "lg:col-span-7 col-span-4 row-span-1"
    },
    {
        title: "Dopamine Social Set",
        image: "/image/home/<USER>",
        href: "/products/social-set",
        className: "lg:col-span-5 col-span-4 row-span-1"
    },
    {
        title: "rcc",
        image: "/image/home/<USER>",
        href: "/product/rc-airline-sweat-absorbing-towel",
        className: "lg:col-span-5 col-span-4 row-span-1"
    },
    {
        title: "pe",
        image: "/image/home/<USER>",
        href: "/products/eyewear",
        className: "lg:col-span-7 col-span-4 row-span-1"
    },
    {
        title: "USAP Approved",
        image: "/image/home/<USER>",
        href: " /products/usap-approved",
        className: "lg:col-span-7 col-span-4 row-span-1"
    },
    {
        title: "paj",
        image: " /image/home/<USER>",
        href: "/products/jewelry",
        className: "lg:col-span-5 col-span-4 row-span-1"
    }
];

export default function PopularCollections() {
    const t = useTranslations();
    const locale = useLocale();
    return (
        <section className="w-full bg-white py-16 md:pt-20 md:pb-36">
            <div className="container">
                <h2
                    className={`mb-8 text-center text-2xl  ${locale === defaultLocale ? "ib" : "font-semibold"} uppercase text-black md:mb-12 md:text-3xl lg:text-4xl`}
                >
                    {t("home.PopularCollections")}
                </h2>

                <div className="grid grid-cols-4 lg:grid-cols-12 gap-2 max-w-[1100px] mx-auto">
                    {collections.map((collection, index) => (
                        <motion.div
                            key={collection.title}
                            className={`group relative overflow-hidden ${collection.className}`}
                        >
                            <Link href={collection.href} className="block relative">
                                <div className="group overflow-hidden h-full">
                                    <SEOOptimizedImage
                                        src={collection.image}
                                        alt={t(`home.${collection.title}`)}
                                        width={500}
                                        height={500}
                                        quality={100}
                                        unoptimized
                                        className="w-full h-auto max-h-[230px] object-cover transition-transform duration-700"
                                    />
                                    {/* 玻璃滑过效果 - 只用于图片 */}
                                    {/* <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-[700ms] ease-out">
                                        <div className="h-full w-1/3 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12 blur-sm"></div>
                                    </div> */}
                                </div>
                                <div className="absolute inset-0 ">
                                    <motion.h3
                                        className="absolute bottom-6 left-4 text-lg font-medium text-white md:text-2xl lg:text-2xl ib"
                                    >
                                        {t(`home.${collection.title}`)}
                                    </motion.h3>
                                </div>
                            </Link>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    );
}