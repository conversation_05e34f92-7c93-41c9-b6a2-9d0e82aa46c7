import md5 from "md5";
import { type KeyValueItem } from "@/lib/@types/base";
import { type LanguageCodeEnum } from "@/gql/graphql";
import { type ProductApiType } from "@/lib/@types/api/product";
import { message } from "antd";
// 获取当前是哪个频道

export const i18nKey = (str: string): string => {
	if (!str) return str;
	return md5(str);
};

// 生成一个随机uuid
export const generateUuid = () => {
	return md5(
		"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
			let r = (Math.random() * 16) | 0,
				v = c == "x" ? r : (r & 0x3) | 0x8;
			return v.toString(16);
		}),
	);
};

// 是否为开发环境
export const isDev = process.env.NODE_ENV === "development";
//获取当前域名
export const cApiUrl = isDev ? process.env.NEXT_PUBLIC_DEV_URL : process.env.NEXT_PUBLIC_SITE_URL;

// 防抖函数
export function debounce(func: Function, delay: number): Function {
	let timer: NodeJS.Timeout;

	return function (this: any, ...args: any[]) {
		const context = this;

		clearTimeout(timer);
		timer = setTimeout(() => {
			func.apply(context, args);
		}, delay);
	};
}

// 剔除builder服务端组件传递给客户端组件的某些值会出现报错

// 将 locale “-” 转成"_" 全部大写 请求多语言数据时需要处理
export const handleGraphqlLocale = (locale: string) => {
	return locale.replace("-", "_").toUpperCase() as LanguageCodeEnum;
};

export const formatMoney = (amount: number, currency: string) =>
	new Intl.NumberFormat("en-US", {
		style: "currency",
		currency,
	}).format(amount);

export const formatMoneyRange = (
	range: {
		start?: { amount: number; currency: string } | null;
		stop?: { amount: number; currency: string } | null;
	} | null,
) => {
	const { start, stop } = range || {};
	const startMoney = start && formatMoney(start.amount, start.currency);
	const stopMoney = stop && formatMoney(stop.amount, stop.currency);

	if (startMoney === stopMoney) {
		return startMoney;
	}

	return `${startMoney} - ${stopMoney}`;
};

export const showI18nName = (props: any, key = "name") => {
	// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
	return props?.translations?.[key] || props?.[key] || "";
};

// 判断是否是一个json
export const isJson = (str: string) => {
	try {
		JSON.parse(str);
	} catch (e) {
		return false;
	}
	return true;
};

export const handleMediaMetadata = (metadata: KeyValueItem[] | undefined) => {
	if (!metadata || !metadata?.length) return undefined;
	const meta = metadata.find((item) => item.key === "media");
	if (!meta || !isJson(meta.value)) return undefined;
	return {
		media: JSON.parse(meta.value) as ProductApiType.MediaMetadata[],
	};
};

export const handleMediaMp4Metadata = (metadata: KeyValueItem[] | undefined) => {
	if (!metadata || !metadata?.length) return undefined;
	const meta = metadata.find((item) => item.key === "cover_mp4");
	if (!meta || !isJson(meta.value)) return undefined;
	return {
		mp4: JSON.parse(meta.value) as {
			id: string;
			name: string;
			url: string;
		}[],
	};
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export type ObserveElementFunction = (target: IntersectionObserverEntry["target"]) => void;

export type BaseAxiosResponse<D extends any> = {
	code: number;
	data: D;
	message: string;
};

export function observeElementIntersection(
	className: string,
	enter: ObserveElementFunction,
	leave?: ObserveElementFunction,
) {
	const elements = document.querySelectorAll(`${className}`);

	const observer = new IntersectionObserver((entries) => {
		entries.forEach((entry) => {
			if (entry.isIntersecting) {
				enter(entry.target);
			} else {
				leave && leave(entry.target);
			}
		});
	});

	elements.forEach((element) => {
		observer.observe(element);
	});
}

// 设置html的overflow
export function setOverflow(overflow: string) {
	document.documentElement.style.overflow = overflow;
}



function escapeXml(unsafe: string) {
	return unsafe.replace(/[<>&'"]/g, function (c) {
		switch (c) {
			case "<":
				return "&lt;";
			case ">":
				return "&gt;";
			case "&":
				return "&amp;";
			case "'":
				return "&apos;";
			case '"':
				return "&quot;";
			default:
				return c;
		}
	});
}

export const createSitemap = (urls: string[]): string => {
	return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
  ${urls
		.map(
			(url) => `
    <url>
      <loc>${escapeXml(url)}</loc>
      <lastmod>${new Date().toISOString()}</lastmod>
      <changefreq>daily</changefreq>
      <priority>0.7</priority>
    </url>`,
		)
		.join("\n")}
</urlset>`;
};

export const createLanguageSitemap = (urls: string[]): string => {
  return `<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${urls
   .map(
    (url) => `
    <sitemap>
      <loc>${escapeXml(url)}</loc>
    </sitemap>`,
   )
   .join("\n")}
</sitemapindex>`;
};

export const defaultLocale = "en";

export type GetSeoMenusResponse = {
	id: number | string;
	link: string;
	name: string;
	flag: number;
	isEditPage: boolean;
};

export const generateLink = (link: string) => {
	if (link.startsWith("/")) {
		return `${process.env.NEXT_PUBLIC_SITE_URL}${link}`;
	} else {
		return `${process.env.NEXT_PUBLIC_SITE_URL}${link}`;
	}
};

// const data = {
//   id: "xxx",
//   quantity: 1,
//   name: "type",
//   pricing: {},
//   product: {
//     id: 'UHJvZHVjdDoxMw==',
//     name: 'Innovative PET Bottles: Recyclable and Stylish Solutions',
//     translation: {},
//     metadata: [],
//     slug: 'innovative-pet-bottles-recyclable-and-stylish-solutions'
//   }
// };
//获取本地的存储
export function getInquiry(): any {
	return JSON.parse(localStorage.getItem("inquiryData") || "[]");
}

export function setInquiry(product, count,VariantActiveName) {
	let locaInquiry = getInquiry();

  let localStoragedata={
    name: product.name,
    slug: product.slug,
    id: product.id,
    metadata:[{'key':'media','value':product.media}],
    media:product.media,

  }
  // console.log(product,'product',localStoragedata);
	if (locaInquiry.length > 0) {


		let filterProduct = locaInquiry.filter((item) => item.variant.product.id == product.id)[0];

		if (filterProduct) {
			filterProduct.variant.product = localStoragedata;
			filterProduct.quantity = filterProduct.quantity + count;
			message.success("add inquiry");
			return localStorage.setItem("inquiryData", JSON.stringify(locaInquiry));
		} else {
			const data = {
				quantity: count,
   
				   variant:{
					   name:VariantActiveName,
					   product: localStoragedata,
				   }
				   
			   }
			locaInquiry.push(data);
			message.success("add inquiry");
			localStorage.setItem("inquiryData", JSON.stringify(locaInquiry));
		}
	} else {
		const data = {
             quantity: count,

				variant:{
					name:VariantActiveName,
					product: localStoragedata,
				}
				
			}
		
			message.success("add inquiry");
		 localStorage.setItem("inquiryData", JSON.stringify([data]));
	}

	  // 手动触发 storage 事件
	  const storageEvent = new Event('storage');
	  window.dispatchEvent(storageEvent);
	
	  // message.success("add inquiry");
}


export function deleteInquiryByProductId(productId) {
	let locaInquiry = getInquiry();
  
	// 查找包含特定商品ID的项
	const index = locaInquiry.findIndex((item) => item.variant.product.id === productId);
  
	if (index !== -1) {
	  // 找到匹配的项后删除
	  locaInquiry.splice(index, 1);
  
	  // 更新本地存储数据
	  localStorage.setItem("inquiryData", JSON.stringify(locaInquiry));
  
	  // 手动触发 storage 事件
	  const storageEvent = new Event('storage');
	  window.dispatchEvent(storageEvent);
  
	  message.success("Inquiry item deleted successfully");
	} else {
	  message.error("Inquiry item not found");
	}
  }



 export const containerVariants = {
		hidden: { opacity: 0 },
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.2,
			},
		},
	};

  export	const itemVariants = {
		hidden: { opacity: 0, y: 20 },
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.5,
			},
		},
	};

  export let header = {
    username: "pintreel",
    token: "pt-6fgh52df466dsdfg",
  };

  export const shippingTitle = "center.f26a7189f413324e9058edcb137ad3de4be1";

  

  export function productTranslationName(str) {
    if (typeof str !== 'string') {
      return str;
    }
  
    return str.replace(/<\d+>/g, '');
  }