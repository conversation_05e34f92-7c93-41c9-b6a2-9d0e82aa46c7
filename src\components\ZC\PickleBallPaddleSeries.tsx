"use client";
import { getCollectionProducts } from "@/lib/api/product";
import { useTranslations } from "next-intl";
import React, { useEffect, useState, useRef } from "react";
import ProductCard from "@/components/Product/product-card";
import { Link } from "@/navigation";
import clsx from "clsx";
import { defaultLocale } from "@/config"
const seriesOrder = ["nova-series", "axis-series", "pulse-series", "quasar-series"];

const SkeletonCard = () => (
    <div className="w-full">
        <div className="bg-gray-100 aspect-square w-full "></div>
        <div className="h-4 bg-gray-100 mt-4 w-3/4"></div>
        <div className="h-4 bg-gray-100 mt-2 w-1/4"></div>
    </div>
);


export default function PickleBallPaddleSeries({ collectionList, channel, locale }) {
    const t = useTranslations();
    const [collections, setCollections] = useState<any[]>([]);
    const [activeCollectionSlug, setActiveCollectionSlug] = useState<string>('');
    const [products, setProducts] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const tabsRef = useRef<(HTMLButtonElement | null)[]>([]);
    const [underlineStyle, setUnderlineStyle] = useState({ left: 0, width: 0 });


    useEffect(() => {
        console.log("collectionList=====", collectionList);

        if (collectionList?.edges) {
            const filtered = collectionList.edges
                .filter(edge => seriesOrder.includes(edge.node.slug))
                .sort((a, b) => seriesOrder.indexOf(a.node.slug) - seriesOrder.indexOf(b.node.slug));

            setCollections(filtered);
            if (filtered.length > 0) {
                setActiveCollectionSlug(filtered[0].node.slug);
            }
        } else {
            setCollections([]);
        }
    }, [collectionList]);

    useEffect(() => {
        const activeIndex = collections.findIndex(({ node }) => node.slug === activeCollectionSlug);

        if (activeIndex !== -1 && tabsRef.current[activeIndex]) {
            const activeTab = tabsRef.current[activeIndex]!;
            setUnderlineStyle({
                left: activeTab.offsetLeft,
                width: activeTab.offsetWidth,
            });

            if (window.innerWidth < 768) {
                activeTab.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }
        }
    }, [activeCollectionSlug, collections]);

    useEffect(() => {
        if (!activeCollectionSlug) return;

        const fetchProducts = async () => {
            setLoading(true);
            try {
                const productData = await getCollectionProducts({
                    slug: activeCollectionSlug,
                    locale,
                    channel
                });
                console.log("productData=====", productData);
                const productEdges = productData?.collection?.products?.edges || [];
                setProducts(productEdges.slice(0, 4));
            } catch (error) {
                console.error("Failed to fetch products:", error);
                setProducts([]);
            } finally {
                setLoading(false);
            }
        };

        fetchProducts();
    }, [activeCollectionSlug]);

    return (
        <div className="py-16 md:py-28 bg-white">
            <div className="container">
                <h2 className={`text-xl md:text-2xl 2xl:text-4xl  text-center uppercase tracking-wider mb-8 md:mb-12 ${locale === defaultLocale ? "ib" : "font-semibold"}`}>
                    {t('home.pickleballPaddleSeries')}
                </h2>

                <div className="relative flex justify-start md:justify-center items-center gap-4 md:gap-8 mb-8 md:mb-12 border-b border-gray-200 overflow-x-auto flex-nowrap [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none] max-w-[650px] mx-auto">
                    {collections.map(({ node }, index) => (
                        <button
                            ref={el => tabsRef.current[index] = el}
                            key={node.id}
                            onClick={() => setActiveCollectionSlug(node.slug)}
                            className={clsx(
                                "py-3 px-2 md:px-4 text-sm  uppercase tracking-wider transition-colors duration-300 whitespace-nowrap",
                                {
                                    "text-black": activeCollectionSlug === node.slug,
                                    "ib": locale === defaultLocale,
                                    "font-semibold": locale !== defaultLocale,
                                    "text-gray-500 hover:text-black": activeCollectionSlug !== node.slug,
                                }
                            )}
                        >
                            {locale === defaultLocale ? node.name : (node.translation?.name || node.name)}
                        </button>
                    ))}
                    <div
                        className="absolute bottom-[-1px] h-0.5 bg-black transition-all duration-300 ease-in-out"
                        style={{ left: underlineStyle.left, width: underlineStyle.width }}
                    />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-12">
                    {loading ? (
                        Array.from({ length: 4 }).map((_, i) => <SkeletonCard key={i} />)
                    ) : (
                        products.map(({ node }) => <ProductCard key={node.id} productItem={node} showCompare={false} locale={locale} />)
                    )}
                </div>

                <div className="text-center mt-12 md:mt-16">
                    <Link
                        href="/products/paddle"
                        className="inline-block irs px-8 py-3 border border-[#b1b1b1] text-[#b9b9b9] hover:text-white tracking-wider hover:bg-main hover:border-main transition-all duration-300"
                    >
                        {t('home.viewAllPaddles')}
                    </Link>
                </div>
            </div>
        </div>
    );
}