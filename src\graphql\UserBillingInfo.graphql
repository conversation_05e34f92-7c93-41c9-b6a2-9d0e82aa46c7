mutation AccountAddressCreate(
	$city: String
	$companyName: String
	$country: CountryCode
	$countryArea: String
	$firstName: String
	$lastName: String
	$phone: String
	$streetAddress1: String
	$postalCode: String
) {
	accountAddressCreate(
		input: {
			city: $city
			companyName: $companyName
			country: $country
			countryArea: $countryArea
			firstName: $firstName
			lastName: $lastName
			phone: $phone
			streetAddress1: $streetAddress1
			postalCode: $postalCode
		}
	) {
		address {
      id
			city
			lastName
			companyName
			country {
				country
			}
			firstName
			countryArea
			streetAddress2
			streetAddress1
			phone
			countryArea
			postalCode
		}
		errors {
			field
			message
			code
		}
	}
}
