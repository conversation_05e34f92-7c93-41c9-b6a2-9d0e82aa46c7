import { invariant } from "ts-invariant";
import { type TypedDocumentString } from "@/gql/graphql";
import { useUserStore } from "@/lib/store/user";

type GraphQLErrorResponse = {
	errors: readonly {
		message: string;
	}[];
};

type GraphQLRespone<T> = { data: T } | GraphQLErrorResponse;

export async function executeGraphQL<Result, Variables>(
	operation: TypedDocumentString<Result, Variables>,
	options: {
		headers?: HeadersInit;
		cache?: RequestCache;
		revalidate?: number;
		clientWithAuth?: boolean;
		serverWithAuth?: boolean;
    withAuth?: boolean;
	} & (Variables extends Record<string, never> ? { variables?: never } : { variables: Variables }),
): Promise<Result> {
	invariant(process.env.NEXT_PUBLIC_SALEOR_API_URL, "Missing NEXT_PUBLIC_SALEOR_API_URL env variable");
	const { variables, headers, cache, revalidate, clientWithAuth = false, serverWithAuth = true , withAuth = true} = options;
	let token = "";
	// 判断是否是客户端并且需要传递校验令牌
	if (typeof window !== "undefined" && clientWithAuth) {
		token = useUserStore?.getState()?.userInfo?.token || ("" as string);
	} else if (serverWithAuth) {
		token = process.env.NEXT_PUBLIC_SALEOR_APP_TOKEN;
	}

	const auth: Record<string, string> = {
		Authorization: `Bearer ${token}`,
	};

  
	!token && delete auth.Authorization;
  
	const input = {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			...auth,
			...headers,
		},
		body: JSON.stringify({
			query: operation.toString(),
			...(variables && { variables }),
		}),
		cache: cache,
		next: { revalidate },
	};

	const response = await fetch(process.env.NEXT_PUBLIC_SALEOR_API_URL, input);

	if (!response.ok) {
		const body = await (async () => {
			try {
				return await response.text();
			} catch {
				return "";
			}
		})();
		console.log(input.body);
		// throw new HTTPError(response, body);
	}

	const body = (await response.json()) as GraphQLRespone<Result>;

	// if ("errors" in body) {
	// 	throw new GraphQLError(body);
	// }
  // @ts-ignore
	return body.data;
}

class GraphQLError extends Error {
	constructor(public errorResponse: GraphQLErrorResponse) {
		const message = errorResponse.errors.map((error) => error.message).join("\n");
		super(message);
		this.name = this.constructor.name;
		Object.setPrototypeOf(this, new.target.prototype);
	}
}

class HTTPError extends Error {
	constructor(response: Response, body: string) {
		const message = `HTTP error ${response.status}: ${response.statusText}\n${body}`;
		super(message);
		this.name = this.constructor.name;
		Object.setPrototypeOf(this, new.target.prototype);
	}
}
