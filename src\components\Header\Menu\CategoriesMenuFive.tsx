import { Link } from "@/navigation.ts";
import { defaultLocale } from "@/config.ts";
import React from "react";
import { useCategory } from "@/context/CategoriesContext.tsx";
import { ProductCategoriesQuery } from "@/gql/graphql.ts";
import { useTranslations } from "next-intl";

const CategoriesMenuFive = ({ locale, children, name, link }: { locale: string, children: any, name:any, link:any }) => {
    const t = useTranslations();

    return <div
        className=" invisible opacity-0 group-hover:opacity-100 group-hover:visible group-hover:scale-y-100 max-h-[500px] absolute  bg-white  px-3 py-5 w-fit rounded-b-sm shadow transition-all duration-500 ease-in-out transform scale-y-0 origin-top">
        <ul className="container">
            <li
                className="relative group">
                <Link href={link} className="text-black flex  items-center justify-between">
                    <div className="flex items-center gap-x-2 border-b border-b-gray-400 py-2">
                        <div
                            className="font-bold capitalize">{name}</div>
                    </div>
                </Link>
                {
                    <div
                        className="w-full transition-all transform duration-700   rounded-b-sm   overflow-hidden  z-10 ">
                        <ul className="w-full">
                            {children?.map((item, index) => {
                                return <li key={index} className="w-full  inline-block    transition-all duration-700 p-1  relative">
                                    <Link href={item.link} className="w-full text-black flex    items-center">
                                        {t(item.name)}

                                    </Link>
                                </li>;
                            })}
                        </ul>
                    </div>
                }
            </li>
        </ul>
    </div>
}

export default CategoriesMenuFive
