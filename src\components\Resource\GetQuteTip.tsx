"use client";
import Image from "next/image";
import GetInstantQuoteButton from "@/components/Contact/GetInstantQuote";
import React from "react";

const GetQuteTip = ({ tip }: { tip: string }) => {
	return (
		<div className="container my-10 flex items-center justify-between gap-2 max-md:flex-col max-md:gap-4">
			<Image
				src="/image/tips.png"
				width={25}
				height={20}
				alt="material Selection bottom"
				quality={100}
				className="h-[24] w-[25] object-cover"
			></Image>
			{/*<BulbOutlined className="!text-themeWarning500 font-bold text-3xl" />*/}
			<div className="text-center text-[15px] font-bold">{tip}</div>
			<GetInstantQuoteButton></GetInstantQuoteButton>
		</div>
	);
};
export default GetQuteTip;
