import path from 'path'
import fs from 'fs'
import pkg from '@next/env';
const { loadEnvConfig } = pkg;
import { dirname } from 'path';
import { fileURLToPath } from "node:url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// const fetch = require("node-fetch");  // 导入 node-fetch 模块

// 加载当前项目的环境配置
loadEnvConfig(process.cwd());

/**
 * 获取机器学习令牌的异步函数
 * 此函数通过环境变量中的配置来获取用户名，并请求API以获取新的机器学习令牌
 * 然后，它将获取到的令牌更新到环境变量文件中
 */
export const getMLToken = async () => {
	try {
		// 构造API请求的URL和配置
		const baseUrl = `${process.env.NEXT_PUBLIC_AI_URL}/api/v1/ml_web/get_ml_token`;
		const username = process.env.NEXT_PUBLIC_SOFT_USERNAME;
		const res = await fetch(baseUrl, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				username,
			},
		}).then((r) => r.json());

		// 检查返回码，如果成功则更新环境变量文件
		if (res.code === 200) {
			const token = res.result.data.ml_token;
			const envPath = path.join(__dirname, "../.env");
			let envContent = fs.readFileSync(envPath, "utf8");
			const envTokenKey = "NEXT_PUBLIC_SOFT_TRANSLATE_MLTOKEN";
			const regex = new RegExp(`^${envTokenKey}=.*`, "m");
			if (envContent.match(regex)) {
				envContent = envContent.replace(regex, `${envTokenKey}=${token}`);
			} else {
				// 如果环境变量文件中不存在相应的键值对，则添加新的 token
				envContent += `\n${envTokenKey}=${token}\n`;
			}
			fs.writeFileSync(envPath, envContent, "utf8");
		} else {
			// 如果获取令牌失败，抛出错误
			throw new Error("获取ml token失败");
		}
	} catch (e) {
		// 捕获并记录异常，再次抛出错误以便外部处理
		console.error(e);
		throw new Error("获取ml token失败");
	}
};


