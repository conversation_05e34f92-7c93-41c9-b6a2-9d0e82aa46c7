"use client";
import clsx from 'clsx';
import { <PERSON> } from "@/navigation";;
import React from 'react';

function Index({ msg, className,isHovercolor=false, color = "#a1a1a1", link='/',cls }: { msg: string, className?: string,isHovercolor?: boolean, color?: string, link?: string , cls?: string }) {
  return (

                <Link href={link} className={clsx("border-ddd inline-flex items-center border-b-[1px] py-1 mb-[28px] hover:border-main group ", className,cls)}>
                <span className={clsx("text-ddd hover:text-main pr-2", className)}>{msg}</span>
                <svg
                  className="hdt-icon hdt-icon-2 group-hover:text-main"
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 64 64"
                  fill={isHovercolor?"currentColor":color}
                >
                  <path d="M6.89,64,0,57.11,47.26,9.85H4.92V0H64V59.08H54.15V16.74Z"></path>
                </svg>
              </Link>


  );
}

export default React.memo(Index);