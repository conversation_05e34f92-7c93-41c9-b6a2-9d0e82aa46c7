query ProductListByCategoryPaginated(
	$first: Int!
	$after: String
	$slug: String!
	$channel: String!
	$locale: LanguageCodeEnum!
) {
	category(slug: $slug) {
		...CategoryLocaleItem
		name
		description
		seoDescription
		seoTitle
		products(first: $first, after: $after, channel: $channel,sortBy: {direction: DESC, field: DATE}) {
			totalCount
			pageInfo {
				endCursor
				hasNextPage
			}
			edges {
				node {
					...ProductListItem
				}
			}
		}
	}
}
