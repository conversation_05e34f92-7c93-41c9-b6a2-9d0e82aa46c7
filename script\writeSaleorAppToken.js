import path from 'path'
import fs from 'fs'
import pkg from '@next/env';
const { loadEnvConfig } = pkg;
import { dirname } from 'path';
import { fileURLToPath } from "node:url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// const fetch = require("node-fetch");  // 导入 node-fetch 模块

// 加载当前项目的环境配置
loadEnvConfig(process.cwd());

/**
 * 异步获取Saleor App令牌并更新环境变量文件
 * 本函数首先从指定的URL获取前端令牌，然后根据获取的结果更新环境变量文件中的令牌值
 * 如果环境变量文件中不存在令牌，则添加新的令牌；如果已存在，则进行替换
 * @throws {Error} 当获取令牌失败时抛出错误
 */
export const getSaleorAppToken = async () => {
	try {
		// 构造请求URL，从环境变量中读取配置
		const baseUrl = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/user/get_front_token`;
		// 发起GET请求，获取前端令牌
		const res = await fetch(baseUrl, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				front: "1",
			},
		}).then((r) => r.json());
		// 检查响应码，确保请求成功
		if (res.code === 200) {
			// 构造环境变量文件路径
			const envPath = path.join(__dirname, "../.env");
			// 读取环境变量文件内容
			let envContent = fs.readFileSync(envPath, "utf8");
			// 提取获取到的令牌
			const token = res.detail.saleor_auth_token;
			// 定义环境变量键名
			const envTokenKey = "NEXT_PUBLIC_SALEOR_APP_TOKEN";
			// 构造正则表达式，用于匹配现有的键名
			const regex = new RegExp(`^${envTokenKey}=.*`, "m");
			// 如果已存在该键名，则替换旧的令牌
			if (envContent.match(regex)) {
				envContent = envContent.replace(regex, `${envTokenKey}=${token}`);
			} else {
				// 否则，添加新的键名和令牌
				envContent += `\n${envTokenKey}=${token}\n`;
			}
			// 更新环境变量文件
			fs.writeFileSync(envPath, envContent, "utf8");
		} else {
			// 如果请求失败，抛出错误
			throw new Error("获取saleor app token失败");
		}
	} catch (e) {
		// 捕获异常，打印错误信息，并抛出新的错误
		console.error(e);
		throw new Error("获取saleor app token失败");
	}
};


