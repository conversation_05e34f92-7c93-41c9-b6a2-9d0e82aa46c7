"use client"
import React, { useState, useEffect } from 'react';

const ScrollToTopButton = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [borderProgress, setBorderProgress] = useState(0);

  useEffect(() => {
    const toggleVisibility = () => {
      const scrollPosition = window.scrollY;
      const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
      if (scrollPosition > 300) {
        setIsVisible(true);
        const progress = Math.min((scrollPosition - 300) / (maxScroll - 300) * 400, 400);
        setBorderProgress(progress);
      } else {
        setIsVisible(false);
        setBorderProgress(0);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const getProgressStyle = () => {
    const topWidth = `${Math.min(borderProgress, 100)}%`;
    const rightHeight = borderProgress <= 100 ? '0%' : 
                       borderProgress >= 200 ? '100%' : 
                       `${borderProgress - 100}%`;
    const bottomWidth = borderProgress <= 200 ? '0%' : 
                       borderProgress >= 300 ? '100%' : 
                       `${borderProgress - 200}%`;
    const leftHeight = borderProgress <= 300 ? '0%' : 
                      borderProgress >= 400 ? '100%' : 
                      `${borderProgress - 300}%`;

    return {
      '--top-width': topWidth,
      '--right-height': rightHeight,
      '--bottom-width': bottomWidth,
      '--left-height': leftHeight,
    } as React.CSSProperties;
  };

  const borderStyle = "bg-black transition-all duration-300";
  const borderWidth = "1.5px"; // 统一边框宽度

  return (
    <div className='max-md:hidden'>
      {isVisible && (
        <button
          onClick={scrollToTop}
          className="!fixed !bottom-8 !right-8 w-12 h-12 bg-white hover:bg-gray-50 transition-all duration-300 group cursor-pointer"
          aria-label="Scroll to top"
          style={{
            ...getProgressStyle(),
            zIndex: '999',
          }}
        >
          {/* 顶部边框 */}
          <div 
            className={`absolute top-0 left-0 ${borderStyle}`}
            style={{ 
              width: 'var(--top-width)', 
              height: borderWidth 
            }} 
          />
          
          {/* 右侧边框 */}
          <div 
            className={`absolute top-0 right-0 ${borderStyle}`}
            style={{ 
              height: 'var(--right-height)', 
              width: borderWidth 
            }} 
          />
          
          {/* 底部边框 */}
          <div 
            className={`absolute bottom-0 right-0 ${borderStyle}`}
            style={{ 
              width: 'var(--bottom-width)', 
              height: borderWidth,
              transform: 'scaleX(-1)' 
            }} 
          />
          
          {/* 左侧边框 */}
          <div 
            className={`absolute bottom-0 left-0 ${borderStyle}`}
            style={{ 
              height: 'var(--left-height)', 
              width: borderWidth,
              transform: 'scaleY(-1)' 
            }} 
          />

          <i className="ri-arrow-up-s-line text-lg absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all group-hover:-translate-y-[60%]" />
        </button>
      )}
    </div>
  );
};

export default React.memo(ScrollToTopButton);
