mutation CheckoutShippingAddressUpdate(
  $checkoutId: ID!
  $firstName: String!
  $lastName: String!
  $country: CountryCode!
  $companyName: String
  $countryArea: String
  $streetAddress1: String!
  $city: String
  $phone: String!
  $postalCode: String!
) {
  checkoutShippingAddressUpdate(
    checkoutId: $checkoutId
    shippingAddress: {
      firstName: $firstName
      lastName: $lastName
      country: $country
      companyName: $companyName
      countryArea: $countryArea
      streetAddress1: $streetAddress1
      phone: $phone
      postalCode: $postalCode
      city: $city
    }
  ) {
    errors {
      code
      field
      message
    }
    checkout {
      id
      shippingAddress {  
        firstName
        lastName
        country {
          code
        }
        companyName
        countryArea
        streetAddress1
        city
        phone
        postalCode
      }
    }
  }
}