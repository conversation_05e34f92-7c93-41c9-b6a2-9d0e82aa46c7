"use client";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { useLocale, useTranslations } from "next-intl";
import { defaultLocale } from "@/config";
import {useRouter  } from "@/navigation";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";
function Index({ categories }: { categories }) {
	const t = useTranslations("nav");
  // console.log(categories,'categories');
  

	return (
		<>
			<motion.div
				initial="hidden"
				whileInView="visible"
				viewport={{ once: true, amount: 0.5 }}
				variants={containerVariants}
				className="Category box-border hidden w-full border-b-[1px] border-[#ebebeb] py-[36px] text-[18px] font-medium md:block"
			>
				<div className="max-md container flex  justify-center">
					<div className="flex flex-1 items-center  justify-center gap-x-5 ">
						<Cube />
						<motion.span variants={itemVariants} className=" ">
							{t("Free")}
						</motion.span>
					</div>
					<div className="flex flex-1 items-center  justify-center  gap-x-5">
						<Suc />
						<motion.span variants={itemVariants} className=" ">
							{t("returns")}
						</motion.span>
					</div>
					<div className="flex flex-1 items-center  justify-center  gap-x-5 max-lg:hidden">
						<Shield />
						<span className=" ">{t("Freewarranty")}</span>
					</div>
				</div>
				<div className="mt-[20px] hidden w-full max-lg:block">
					<div className="flex w-full items-center  justify-center  gap-x-5 ">
						<Shield />
						<motion.span variants={itemVariants} className=" ">
							{t("Freewarranty")}
						</motion.span>
					</div>
				</div>
			</motion.div>
			<Swiper
				className="!hidden h-[150px] max-md:!block"
				modules={[ Autoplay, Pagination]}
				loop={true}
				slidesPerView={1}
				spaceBetween={10}
				onSlideChange={() => console.log("slide change")}
				onSwiper={(swiper) => console.log(swiper)}
				autoplay={{
					delay: 5000,
					disableOnInteraction: true,
				}}
			>
				<SwiperSlide className="!flex h-full w-full !flex-col  !items-center !justify-center  gap-y-3">
					<Cube />
					<span className=" ">{t("Free")}</span>
				</SwiperSlide>

				<SwiperSlide className="!flex h-full w-full !flex-col  !items-center !justify-center  gap-y-3 ">
					<Suc />
					<span className=" ">{t("returns")}</span>
				</SwiperSlide>
				<SwiperSlide className="!flex h-full w-full !flex-col  !items-center !justify-center  gap-y-3 ">
					<Shield />
					<span className=" ">{t("Freewarranty")}</span>
				</SwiperSlide>
			</Swiper>

			<CategoryProduct categories={categories} />
		</>
	);
}

export default React.memo(Index);

function CategoryProduct({ categories }) {
	const locale = useLocale();

	const prevRef = useRef(null) as any;
	const nextRef = useRef(null) as any;

	const router = useRouter();
	const swiperBaseConfig = {
		slidesPerView: 3,
		breakpoints: {
		  320: {
			slidesPerView: 1,
		  },
		  768: {
			slidesPerView: 2,
		  },
		  1024: {
			slidesPerView: 3,
		  },
		},
	  };
    const sortedCategories = useMemo(() => {
      if (!categories?.edges) return [];
      return [...categories.edges].sort((a, b) => {
        if (!a.node.sortNumber && b.node.sortNumber) return 1;
        if (a.node.sortNumber && !b.node.sortNumber) return -1;
        if (!a.node.sortNumber && !b.node.sortNumber) return 0;
        return a.node.sortNumber - b.node.sortNumber;
      });
    }, [categories]);
	const t = useTranslations("nav");

	return (
		<div className="w-full py-[50px] max-lg:py-10">
			<div className="container mx-auto">
				<div className="mb-8 flex items-center gap-4">
					<button aria-label="left" className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300">
						<i ref={prevRef} className="ri-arrow-left-s-line text-sm"></i>
					</button>
					<button aria-label="right" className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300">
						<i ref={nextRef} className="ri-arrow-right-s-line text-sm"></i>
					</button>
					<h2 className="text-xl font-medium">{t("SHOP")}</h2>
				</div>

				<motion.div
					initial="hidden"
					whileInView="visible"
					viewport={{ once: true, amount: 0.5 }}
					variants={containerVariants}
					className="grid grid-cols-4 gap-6 max-md:grid-cols-1 "
				>
					<div className="col-span-3 max-md:col-span-3">
						<Swiper
							modules={[Autoplay, Navigation]}
							// loop={true}
							{...swiperBaseConfig}
							spaceBetween={30}
							autoplay={{
								delay: 5000,
								disableOnInteraction: true,
							}}
							pagination={{
								clickable: true,
								bulletActiveClass: "swiper-pagination-bullet-active !bg-white",
								bulletClass: "swiper-pagination-bullet home-banner-bullet",
							}}
							navigation={{
								prevEl: prevRef.current,
								nextEl: nextRef.current,
							}}
							onBeforeInit={(swiper: any) => {
								swiper.params.navigation.prevEl = prevRef.current;
								swiper.params.navigation.nextEl = nextRef.current;
							}}
						>
							{sortedCategories.length>0&&sortedCategories.map((item) => {
								let media = (item?.node?.media) ? JSON.parse(item?.node?.media) : [];
								const imageUrl = media[0]?.url || "/image/default-image.webp";
								const categoryName =
									locale === defaultLocale ? item.node.name : item.node.translation?.name || item.node.name;

								return (
									<SwiperSlide key={item.node.id} className="list group relative overflow-hidden rounded-2xl">
										<motion.div variants={itemVariants}>
											<img
												alt={`${categoryName}`}
												className="mx-auto h-[435px] w-full object-cover duration-1000 ease-in-out hover:scale-110"
												src={imageUrl}
											/>
											<div
												onClick={() => router.push(`/products/${item.node.slug}`)}
												className="absolute bottom-6 left-0 box-border flex cursor-pointer items-center gap-x-2 bg-white px-3 py-2 text-[18px] group-hover:bg-black"
											>
												<span className="text-xl font-medium text-black group-hover:text-white">
													{categoryName}
												</span>
												<span>
													<svg
														className="hdt-icon hdt-icon-2 scale-110 opacity-0 transition-opacity duration-200 group-hover:text-white group-hover:opacity-100"
														xmlns="http://www.w3.org/2000/svg"
														width="10"
														height="10"
														viewBox="0 0 64 64"
														fill="currentColor"
													>
														<path d="M6.89,64,0,57.11,47.26,9.85H4.92V0H64V59.08H54.15V16.74Z"></path>
													</svg>
												</span>
											</div>
										</motion.div>
									</SwiperSlide>
								);
							})}
						</Swiper>
					</div>

					<motion.div
						variants={itemVariants}
						className="relative col-span-1 box-border flex flex-col items-end justify-end overflow-hidden rounded-2xl
           border border-gray-200 bg-white p-10 max-md:hidden "
					>
						<div className=" w-full ">
							<h3 className="mb-4 text-[28px] font-medium">{t("Discover")}</h3>
							<button
								onClick={() => router.push(`/products`)}
                aria-label="Discover"
								className="group flex h-[50px] w-[50px] items-center justify-center rounded-full border border-gray-300 hover:bg-black"
							>
								<svg
									className="hdt-icon  hdt-icon-2 group-hover:text-white"
									xmlns="http://www.w3.org/2000/svg"
									width="10"
									height="10"
									viewBox="0 0 64 64"
									fill="currentColor"
								>
									<path d="M6.89,64,0,57.11,47.26,9.85H4.92V0H64V59.08H54.15V16.74Z"></path>
								</svg>
							</button>
						</div>
					</motion.div>
				</motion.div>
			</div>
		</div>
	);
}

function Cube() {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" width="29" height="30" viewBox="0 0 29 30">
			<path d="M14.2504 30L0 22.8814V7.01669L14.2504 0L28.5009 7.01273V22.8814L14.2504 30ZM2.64633 21.246L14.2504 27.0388L25.8546 21.242V8.66276L14.2504 2.95069L2.64633 8.66276V21.246ZM15.5736 28.5207H12.9273V15.0152L0.739716 9.02798L1.90671 6.64627L15.5736 13.3666V28.5207ZM15.5736 28.5207H12.9273V13.3665L26.5942 6.6515L27.7612 9.03321L15.5736 15.0152V28.5207ZM14.2504 15.6596L0.739716 9.02798L1.90671 6.64627L14.2504 12.717L26.5942 6.65292L27.7612 9.03457L14.2504 15.6596Z"></path>
		</svg>
	);
}
function Suc() {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" width="26" height="27" viewBox="0 0 26 27" fill="none">
			<path
				d="M7.67 10.5786L5.85 12.3986L11.7 18.2486L24.7 5.24858L22.88 3.42858L11.7 14.6086L7.67 10.5786ZM23.4 13.0486C23.4 18.7686 18.72 23.4486 13 23.4486C7.28 23.4486 2.6 18.7686 2.6 13.0486C2.6 7.32858 7.28 2.64858 13 2.64858C14.04 2.64858 14.95 2.77858 15.86 3.03858L17.9401 0.958584C16.38 0.438584 14.69 0.048584 13 0.048584C5.85 0.048584 0 5.89858 0 13.0486C0 20.1986 5.85 26.0486 13 26.0486C20.15 26.0486 26 20.1986 26 13.0486H23.4Z"
				fill="black"
			></path>
		</svg>
	);
}
function Shield() {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" width="25" height="32" viewBox="0 0 25 32" fill="none">
			<path
				d="M23.9821 6.51869L13.2678 1.16153C13.1191 1.08722 12.9551 1.04854 12.7889 1.04854C12.6226 1.04854 12.4587 1.08722 12.3099 1.16153L1.59563 6.51869C1.41752 6.6077 1.26774 6.74456 1.16308 6.91395C1.05842 7.08333 1.00303 7.27852 1.00313 7.47763V17.1634C1.0041 18.9766 1.44179 20.763 2.27916 22.3714C3.11653 23.9797 4.32895 25.3627 5.81385 26.4034L12.1749 30.8552C12.3549 30.981 12.5693 31.0485 12.7889 31.0485C13.0085 31.0485 13.2228 30.981 13.4028 30.8552L19.7639 26.4034C21.2488 25.3627 22.4612 23.9797 23.2986 22.3714C24.136 20.763 24.5737 18.9766 24.5746 17.1634V7.47763C24.5747 7.27852 24.5193 7.08333 24.4147 6.91395C24.31 6.74456 24.1602 6.6077 23.9821 6.51869ZM22.4318 17.1634C22.431 18.6321 22.0764 20.079 21.3981 21.3816C20.7198 22.6843 19.7377 23.8045 18.535 24.6473L12.7889 28.6695L7.04279 24.6473C5.84001 23.8045 4.85793 22.6843 4.17964 21.3816C3.50134 20.079 3.1468 18.6321 3.14599 17.1634V8.13977L12.7889 3.31833L22.4318 8.13977V17.1634Z"
				fill="black"
			></path>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M1.00313 17.1634V7.47763C1.00303 7.27852 1.05842 7.08333 1.16308 6.91395C1.26774 6.74456 1.41752 6.6077 1.59563 6.51869L12.3099 1.16153C12.4587 1.08722 12.6226 1.04854 12.7889 1.04854C12.9551 1.04854 13.1191 1.08722 13.2678 1.16153L23.9821 6.51869C24.1602 6.6077 24.31 6.74456 24.4147 6.91395C24.5193 7.08333 24.5747 7.27852 24.5746 7.47763V17.1634C24.5737 18.9766 24.136 20.763 23.2986 22.3714C22.4612 23.9797 21.2488 25.3627 19.7639 26.4034L13.4028 30.8552C13.2228 30.981 13.0085 31.0485 12.7889 31.0485C12.5693 31.0485 12.3549 30.981 12.1749 30.8552L5.81385 26.4034C4.32895 25.3627 3.11653 23.9797 2.27916 22.3714C1.44179 20.763 1.0041 18.9766 1.00313 17.1634ZM24.8746 7.47777V17.1634C24.8736 19.0248 24.4243 20.8588 23.5647 22.5099C22.7051 24.161 21.4604 25.5807 19.9361 26.6491L13.5748 31.101C13.3444 31.2621 13.07 31.3485 12.7889 31.3485C12.5078 31.3485 12.2334 31.2621 12.003 31.1011L5.64184 26.6492C4.11748 25.5809 2.87268 24.161 2.01306 22.5099C1.15344 20.8588 0.704126 19.025 0.703125 17.1635V7.47777C0.703125 7.47772 0.703125 7.47781 0.703125 7.47777C0.703032 7.22296 0.773929 6.97302 0.907866 6.75625C1.04181 6.53947 1.23351 6.36429 1.46146 6.25037C1.46148 6.25036 1.46144 6.25038 1.46146 6.25037L12.1758 0.893207C12.3661 0.798086 12.5761 0.748535 12.7889 0.748535C13.0017 0.748535 13.2116 0.798054 13.4019 0.893175L24.1162 6.25033C24.1162 6.25035 24.1162 6.25032 24.1162 6.25033C24.3442 6.36426 24.5359 6.53947 24.6699 6.75625C24.8038 6.97302 24.8747 7.22296 24.8746 7.47777C24.8746 7.47781 24.8746 7.47772 24.8746 7.47777ZM21.132 21.2431C21.7881 19.9831 22.131 18.5837 22.1318 17.1632V8.32518L12.7889 3.65374L3.44599 8.32518V17.1632C3.44677 18.5837 3.78969 19.9831 4.44573 21.2431C5.10176 22.503 6.05163 23.5864 7.21495 24.4016L12.7889 28.3033L18.3628 24.4016C19.5261 23.5864 20.476 22.503 21.132 21.2431ZM18.535 24.6473C19.7377 23.8045 20.7198 22.6843 21.3981 21.3816C22.0764 20.079 22.431 18.6321 22.4318 17.1634V8.13977L12.7889 3.31833L3.14599 8.13977V17.1634C3.1468 18.6321 3.50134 20.079 4.17964 21.3816C4.85793 22.6843 5.84001 23.8045 7.04279 24.6473L12.7889 28.6695L18.535 24.6473Z"
				fill="black"
			></path>
		</svg>
	);
}
