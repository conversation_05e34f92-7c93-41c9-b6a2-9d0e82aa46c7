"use client";
import { useEffect, useMemo, useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import { BodyText } from "@/components/BodyText";
import Skeleton from "react-loading-skeleton";
import { defaultLocale } from "@/config";
import { getColorImagePath } from "@/utils/categoryMapping";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useRouter } from "@/navigation";
import { executeGraphQL } from "@/lib/graphql";
import { ProductDetailsDocument } from "@/gql/graphql";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { handleGraphqlLocale } from "@/lib/utils/util";
import { Modal } from "antd";

// 产品变体分组配置 - 只需要管理 slug
const PRODUCT_VARIANT_GROUPS = {
  "nova-fiberglass-pickleball-paddle": [
    "nova-fiberglass-pickleball-paddle-style-meets-performance",
    "nova-fiberglass-pickleball-paddle-kiki",
    "nova-fiberglass-pickleball-paddle-meowzart-go-pop",
    "nova-fiberglass-pickleball-paddle-meowzart"
  ],
  "group2": [
    "quasar-raw-carbon-dynamic-neck-core-pickleball-paddle-dreamglow-pop",
    "quasar-raw-carbon-dynamic-neck-core-pickleball-paddle-cyberbeat-meowzart"
  ],
  "group3": [
    "mbti-composite-pickleball-paddle-judging",
    "mbti-fiberglass-pickleball-paddle-introverted",
    "mbti-composite-pickleball-paddle-perceiving",
    "mbti-fiberglass-pickleball-paddle-extraverted"
  ]
  // 可以添加更多分组
  // "other-product-series": [
  //   "product-slug-1",
  //   "product-slug-2"
  // ]
};

type AttributeSelectorProps = {
  variants: any[];
  onChange: (variantId: string) => void;
  changeValue: (params: any) => void;
  product?: any; // 添加产品信息，用于获取分类
  currentSlug?: string; // 添加当前产品的slug
  onNavigatingChange?: (isNavigating: boolean) => void; // 添加跳转状态回调
};

export default function AttributeSelector({ variants, onChange, changeValue, product, currentSlug, onNavigatingChange }: AttributeSelectorProps) {
  const t = useTranslations("nav");
  const locale = useLocale();
  const router = useRouter();
  const [selectedValues, setSelectedValues] = useState<Record<string, string>>({});
  const [groupProductsData, setGroupProductsData] = useState<Record<string, any>>({});
  const [isLoadingGroupData, setIsLoadingGroupData] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [isSizeChartOpen, setIsSizeChartOpen] = useState(false);

  // 查找当前产品所属的变体分组
  const currentVariantGroup = useMemo(() => {
    if (!currentSlug) return null;

    for (const [baseSlug, slugs] of Object.entries(PRODUCT_VARIANT_GROUPS)) {
      if (slugs.includes(currentSlug)) {
        return {
          baseSlug,
          slugs,
          currentSlug
        };
      }
    }
    return null;
  }, [currentSlug]);

  console.log("product====", product);
  console.log("currentVariantGroup====", currentVariantGroup);
  console.log("groupProductsData====", groupProductsData);

  // 预请求分组中其他产品的数据
  useEffect(() => {
    if (!currentVariantGroup) {
      setIsLoadingGroupData(false);
      return;
    }

    const fetchGroupProductsData = async () => {
      setIsLoadingGroupData(true);
      try {
        const channel = (await getChannelLanguageMap())[defaultLocale];

        const productDataPromises = currentVariantGroup.slugs.map(async (slug: string) => {
          if (slug === currentSlug) {
            // 当前产品使用已有的数据
            return { slug, data: product };
          }

          try {
            // 使用相同的 GraphQL 接口获取其他产品的数据
            const { product: productData } = await executeGraphQL(ProductDetailsDocument, {
              withAuth: false,
              variables: {
                locale: handleGraphqlLocale(locale || defaultLocale),
                channel: channel,
                slug: slug,
                filterKeywordKey: "",
              },
            });
            return { slug, data: productData };
          } catch (error) {
            console.error(`Failed to fetch product data for ${slug}:`, error);
            return { slug, data: null };
          }
        });

        const results = await Promise.all(productDataPromises);
        const productDataMap: Record<string, any> = {};
        results.forEach(({ slug, data }) => {
          if (data) {
            productDataMap[slug] = data;
          }
        });

        setGroupProductsData(productDataMap);
      } catch (error) {
        console.error('Failed to fetch channel data:', error);
      } finally {
        setIsLoadingGroupData(false);
      }
    };

    fetchGroupProductsData();
  }, [currentVariantGroup, currentSlug, product, locale]);

  const attributeGroups = useMemo(() => {
    if (!variants?.length) return [];

    const attributeMap = new Map<string, any>();

    // 首先处理当前产品的变体属性
    variants.forEach(variant => {
      variant.attributes?.forEach((attr: any) => {
        // 修复1：统一属性名大小写和空格处理
        const attrName = attr.attribute?.name?.trim();

        const excludedAttributes = ['cost', 'cost price', 'costprice']; // 需要排除的属性名列表
        if (!attrName || excludedAttributes.includes(attrName.trim())) return;

        // 修复2：兼容不同值类型
        const attrValue = attr.values?.[0]?.name || attr.values?.[0]?.value;

        if (!attrValue) return;

        // 修复3：处理特殊符号值（如13*14） 只删除首尾空格，保留内部空格
        const normalizedValue = String(attrValue).trim();

        if (!attributeMap.has(attrName)) {
          attributeMap.set(attrName, {
            attribute: attr.attribute,
            values: new Set(),
            variants: new Set(),
            isVariantGroup: false
          });
        }

        const group = attributeMap.get(attrName)!;
        group.values.add(normalizedValue);  // 存储标准化后的值
        group.variants.add(variant.id);
      });
    });

    // 如果当前产品属于变体分组，将分组中其他产品的属性值融合到现有属性组中
    if (currentVariantGroup && Object.keys(groupProductsData).length > 0) {
      console.log("开始融合变体分组属性...");
      currentVariantGroup.slugs.forEach((slug: string) => {
        const productData = groupProductsData[slug];
        console.log(`处理产品 ${slug}:`, productData);
        if (!productData?.variants) {
          console.log(`产品 ${slug} 没有变体数据`);
          return;
        }

        // 遍历该产品的所有变体属性
        productData.variants.forEach((variant: any) => {
          console.log(`处理变体:`, variant);
          variant.attributes?.forEach((attr: any) => {
            const attrName = attr.attribute?.name?.trim();
            const excludedAttributes = ['cost', 'cost price', 'costprice'];
            if (!attrName || excludedAttributes.includes(attrName.trim())) return;

            const attrValue = attr.values?.[0]?.name || attr.values?.[0]?.value;
            if (!attrValue) return;

            const normalizedValue = String(attrValue).trim();
            console.log(`找到属性: ${attrName} = ${normalizedValue}`);

            // 查找是否已经存在相同名称的属性组
            const existingGroup = attributeMap.get(attrName);
            if (existingGroup) {
              console.log(`属性 ${attrName} 已存在，添加新值: ${normalizedValue}`);
              // 如果属性组已存在，添加新的值并标记为变体分组
              existingGroup.values.add(normalizedValue);
              existingGroup.isVariantGroup = true;
              // 添加产品slug映射，用于后续跳转
              if (!existingGroup.productMapping) {
                existingGroup.productMapping = new Map();
              }
              existingGroup.productMapping.set(normalizedValue, slug);
            } else {
              console.log(`创建新属性组: ${attrName} = ${normalizedValue}`);
              // 如果属性组不存在，创建新的属性组（这种情况下是纯变体分组属性）
              const productMapping = new Map();
              productMapping.set(normalizedValue, slug);

              attributeMap.set(attrName, {
                attribute: {
                  name: attrName,
                  translation: { name: attrName }
                },
                values: new Set([normalizedValue]),
                variants: new Set(),
                isVariantGroup: true,
                productMapping
              });
            }
          });
        });
      });
      console.log("融合完成，最终 attributeMap:", attributeMap);
    }

    return Array.from(attributeMap).map(([name, data]) => {
      return {
        name: data.attribute.name,
        attribute: data.attribute,
        values: Array.from(data.values),
        variants: Array.from(data.variants),
        isVariantGroup: data.isVariantGroup,
        productMapping: data.productMapping
      };
    });
  }, [variants, currentVariantGroup, groupProductsData]);

  // 检查是否应该显示size chart
  const shouldShowSizeChart = useMemo(() => {
    if (!product?.category?.slug) return false;

    // 检查分类slug是否匹配
    const categorySlug = product.category.slug.toLowerCase();
    const validCategories = ['apparel', 'mens', 'womens'];
    const hasValidCategory = validCategories.includes(categorySlug);

    if (!hasValidCategory) return false;

    // 检查是否有size属性
    const hasSizeAttribute = attributeGroups.some(group =>
      group.attribute.name.toLowerCase().trim() === 'size'
    );

    return hasSizeAttribute;
  }, [product?.category?.slug, attributeGroups]);

  const availableValues = useMemo(() => {
    const result: Record<string, Array<{ value: string; available: boolean }>> = {};

    attributeGroups.forEach(group => {
      const normalizedGroupName = group.attribute.name.trim();
      // @ts-ignore
      result[group.name] = group.values.map(value => {
        // 构建包含当前选择的组合
        const combination = {
          ...Object.fromEntries(
            Object.entries(selectedValues).map(([k, v]) => [k.trim(), v])
          ),
          [normalizedGroupName]: value
        };

        console.log(`检查属性组合可用性: ${normalizedGroupName}=${value}`, combination);

        // 首先检查是否在当前产品变体中存在
        const existsInCurrentVariants = variants.some(variant =>
          variant.attributes.every(attr => {
            const attrName = attr.attribute.name.trim();
            if (['cost', 'cost price', 'costprice'].includes(attrName)) return true;
            const selectedValue = combination[attrName];
            const normalizedVariantValue = String(attr.values[0]?.name || attr.values[0]?.value).trim();
            return !selectedValue || selectedValue === normalizedVariantValue;
          })
        );

        if (existsInCurrentVariants) {
          return { value, available: true };
        }

        // 如果不在当前变体中，检查分组产品中是否存在匹配的组合
        if (currentVariantGroup && Object.keys(groupProductsData).length > 0) {
          const existsInGroupProducts = Object.entries(groupProductsData).some(([slug, productData]) => {
            if (slug === currentSlug || !productData?.variants) return false;

            return productData.variants.some((variant: any) =>
              variant.attributes.every((attr: any) => {
                const attrName = attr.attribute.name.trim();
                if (['cost', 'cost price', 'costprice'].includes(attrName)) return true;
                const selectedValue = combination[attrName];
                const normalizedVariantValue = String(attr.values[0]?.name || attr.values[0]?.value).trim();
                return !selectedValue || selectedValue === normalizedVariantValue;
              })
            );
          });

          if (existsInGroupProducts) {
            return { value, available: true };
          }
        }

        // 否则不可用
        return { value, available: false };
      });
    });

    return result;
  }, [selectedValues, attributeGroups, variants, currentVariantGroup, groupProductsData, currentSlug]);
  useEffect(() => {
    console.log(variants);
    console.log("selectedValues", selectedValues);

    const matchedVariant = variants.find(variant =>
      variant.attributes.every((attr: any) => {
        // 排除不需要匹配的属性
        const attrName = attr.attribute?.name?.trim();
        // 排除这三个属性不参与匹配
        if (['cost', 'cost price', 'costprice'].includes(attrName)) {
          return true; // 直接返回true，表示这个属性总是匹配
        }
        return selectedValues[attr.attribute.name] === attr.values?.[0]?.name
      })
    );
    // 始终触发回调，无匹配时传null
    onChange(matchedVariant?.id || null);
  }, [selectedValues, variants, onChange]);

  // 修复初始化逻辑
  useEffect(() => {
    if (variants?.length > 0 && Object.keys(selectedValues).length === 0) {
      const initialValues: Record<string, string> = {};
      variants[0].attributes?.forEach((attr: any) => {
        // 统一使用标准化属性名
        const attrName = attr.attribute?.name?.trim();
        const value = attr.values?.[0]?.name || attr.values?.[0]?.value;
        if (attrName && value) {
          initialValues[attrName] = String(value).trim();
        }
      });
      setSelectedValues(initialValues);
    }
  }, [variants]);
  // -----------------------------------属性都是空的情况start
  const [itemValue, setItemValue] = useState<any>();
  const [isVariantsLoading, setIsVariantsLoading] = useState(true);
  const [sortedVariants, setSortedVariants] = useState<any[]>([]);

  // 处理变体排序的函数
  useEffect(() => {
    if (variants?.length > 0) {
      setIsVariantsLoading(true);
      setSortedVariants([...variants]);
      setTimeout(() => {
        setIsVariantsLoading(false);
      }, 100);
    }
  }, [variants]);

  const handleOnchange = (value: string) => {
    console.log("value", value);

    setItemValue(value);
    changeValue(value);
  };

  useEffect(() => {
    if (sortedVariants.length > 0) {
      setItemValue(sortedVariants[0]);
      changeValue(sortedVariants[0]);
    }
  }, [sortedVariants]);
  // -----------------------------------属性都是空的情况end
  // 如果正在加载分组数据，显示loading状态
  if (isLoadingGroupData) {
    return (
      <div className="space-y-2">
        <div className="">
          <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
          <div className="flex flex-wrap gap-2">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-10 w-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
        <div className="">
          <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
          <div className="flex flex-wrap gap-2">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-10 w-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* 全屏跳转loading遮罩 */}
      {isNavigating && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="text-center" aria-busy="true" role="status">
            <svg
              aria-hidden="true"
              className="mr-2 inline h-12 w-12 animate-spin fill-white text-main"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}

      <div className="space-y-2">
        {/* 当所有变体的attributes都为空时显示（过滤排除属性后） */}
        {variants?.length > 0 && variants.every(v => {
          const filteredAttributes = v.attributes?.filter(attr => {
            const attrName = attr.attribute?.name?.trim().toLowerCase();
            return !['cost', 'cost price', 'costprice'].includes(attrName);
          });
          return filteredAttributes?.every(attr =>
            !attr.values?.length ||
            attr.values.every(val => !val.name && !val.value)
          );
        }) ? (
          <>
            {variants ? (
              <BodyText intent={"bold"} size="lg" className="!text-[16px] irs">{t('Variants')}:<span className="ml-2">{itemValue?.name}</span></BodyText>
            ) : (
              <Skeleton width={120} height={28} />
            )}
            <div className="mt-3 flex flex-wrap items-center gap-2.5">
              {isVariantsLoading ? (
                // 显示加载状态
                Array(variants.length).fill(0).map((_, index) => (
                  <div key={index} className="h-10 w-32 bg-gray-200"></div>
                ))
              ) : (
                // 显示排序后的变体
                sortedVariants.map((item: any, index: number) => (
                  <div
                    key={index}
                    className="cursor-pointer"
                    onClick={() => {
                      handleOnchange(item);
                    }}
                  >
                    <BodyText
                      size="xs"
                      intent="semibold"
                      className={`mt-1 ${itemValue == item
                        ? "border-black !bg-black text-white"
                        : "border-[#DFDFDF] !bg-white text-black"
                        } font-abeezee cursor-pointer border-[1px] bg-white px-4 py-2 !text-[16px] !font-normal`}
                    >
                      {item.name}
                    </BodyText>
                  </div>
                ))
              )}
            </div>
          </>
        ) : (
          attributeGroups.map(group => (
            <div key={group.name} className="attribute-group">
              <div className="flex items-center  mb-2">
                <h3 className={`uppercase text-[16px] text-black`}>
                  {group.attribute.translation?.name || group.attribute.name}
                  {/* 添加当前选中值展示 */}
                  {/* {selectedValues[group.attribute.name.trim()] && (
                  <span className="ml-2 irs">
                    {selectedValues[group.attribute.name.trim()]}
                  </span>
                )} */}
                </h3>
                {/* Size Chart 按钮 */}
                {shouldShowSizeChart && group.attribute.name.toLowerCase().trim() === 'size' && (
                  <div className="ml-4"> 
                    ——
                    <button
                      onClick={() => setIsSizeChartOpen(true)}
                      className="text-base  md:text-sm hover:underline transition-all duration-200 text-black hover:text-black"
                    >
                      {t('Size Chart')}
                    </button>
                  </div>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {availableValues[group.name].map(({ value, available }) => {
                  // 检查是否为颜色属性
                  const isColorAttribute = group.attribute.name.toLowerCase().trim() === 'color';

                  // 获取颜色对应的图片路径
                  const colorImagePath = isColorAttribute ? getColorImagePath(value, product?.category?.slug) : null;

                  // 计算按钮样式
                  const getButtonClasses = () => {
                    const baseClasses = 'border-[2px] transition-colors';
                    const sizeClasses = isColorAttribute ? 'w-[74px] h-[42px] py-1 px-1 overflow-hidden' : 'px-4 py-2 min-w-[74px] min-h-[42px]';

                    const isSelected = selectedValues[group.name] === value;

                    if (isColorAttribute) {
                      if (isSelected) return `${baseClasses} ${sizeClasses} border-black`;
                      if (available) return `${baseClasses} ${sizeClasses} border-gray-300 hover:border-gray-400`;
                      return `${baseClasses} ${sizeClasses} border-gray-200 cursor-not-allowed opacity-50`;
                    } else {
                      if (isSelected) return `${baseClasses} ${sizeClasses} bg-white text-black border-black`;
                      if (available) return `${baseClasses} ${sizeClasses} bg-white border-gray-300 hover:bg-gray-100`;
                      return `${baseClasses} ${sizeClasses} bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed`;
                    }
                  };

                  // 渲染按钮内容
                  const renderContent = () => {
                    if (!isColorAttribute) return value;

                    if (colorImagePath) {
                      return (
                        <SEOOptimizedImage
                          src={colorImagePath}
                          alt={`RC ${value} color`}
                          width={74}
                          height={42}
                          className="w-full h-full object-cover"
                          unoptimized
                          quality={100}
                          priority
                        />
                      );
                    }

                    return null; // 颜色属性但没有图片时，通过 style 显示背景色
                  };

                  return (
                    <button
                      key={value}
                      disabled={!available}
                      className={getButtonClasses()}
                      style={isColorAttribute && !colorImagePath ? { backgroundColor: value } : {}}
                      onClick={() => {
                        if (available) {
                          // 先更新选中的属性值
                          const normalizedName = group.attribute.name.trim();
                          const newSelectedValues = {
                            ...selectedValues,
                            [normalizedName]: selectedValues[normalizedName] === value ? undefined : value
                          };

                          setSelectedValues(newSelectedValues);

                          // 检查更新后的组合是否在当前产品的变体中存在
                          const hasCompleteSelection = attributeGroups.every(attrGroup => {
                            const attrName = attrGroup.attribute.name.trim();
                            return newSelectedValues[attrName] !== undefined;
                          });

                          // 只有当所有属性都已选择时，才检查是否需要跳转
                          if (hasCompleteSelection) {
                            const existsInCurrentVariants = variants.some(variant =>
                              variant.attributes.every((attr: any) => {
                                const attrName = attr.attribute.name.trim();
                                if (['cost', 'cost price', 'costprice'].includes(attrName)) return true;
                                const selectedValue = newSelectedValues[attrName];
                                const normalizedVariantValue = String(attr.values[0]?.name || attr.values[0]?.value).trim();
                                return !selectedValue || selectedValue === normalizedVariantValue;
                              })
                            );

                            // 如果完整组合不在当前变体中，寻找匹配的产品进行跳转
                            if (!existsInCurrentVariants && currentVariantGroup && Object.keys(groupProductsData).length > 0) {
                              // 在分组产品中寻找匹配的组合
                              for (const [slug, productData] of Object.entries(groupProductsData)) {
                                if (slug === currentSlug || !productData?.variants) continue;

                                const matchingVariant = productData.variants.find((variant: any) =>
                                  variant.attributes.every((attr: any) => {
                                    const attrName = attr.attribute.name.trim();
                                    if (['cost', 'cost price', 'costprice'].includes(attrName)) return true;
                                    const selectedValue = newSelectedValues[attrName];
                                    const normalizedVariantValue = String(attr.values[0]?.name || attr.values[0]?.value).trim();
                                    return !selectedValue || selectedValue === normalizedVariantValue;
                                  })
                                );

                                if (matchingVariant) {
                                  // 显示跳转loading
                                  setIsNavigating(true);
                                  onNavigatingChange?.(true);
                                  // 短暂延迟让用户看到loading状态
                                  setTimeout(() => {
                                    window.location.href = `/product/${slug}`;
                                  }, 100);
                                  break;
                                }
                              }
                            }
                          }
                        }
                      }}
                      title={isColorAttribute ? value : undefined}
                    >
                      {renderContent()}
                    </button>
                  );
                })}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Size Chart Modal */}
      <Modal
        open={isSizeChartOpen}
        onCancel={() => setIsSizeChartOpen(false)}
        footer={null}
        centered
        width="90%"
        style={{
          maxWidth: '800px'
        }}
        className="size-chart-modal"
        title={
          <div className="text-center mt-8">
            <h3 className="text-xl font-semibold">
              {product?.name ? `${product.name}` : t('Size Chart')}
            </h3>
          </div>
        }
        closable={true}
      >
        <div className="size-chart-content my-8">
          {/* 响应式表格容器 */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300 text-sm md:text-base"
              style={{ borderSpacing: 0 }}>
              <thead>
                <tr className="">
                  <th className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">{t('Size')}</th>
                  <th className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">{t('Length')}(")</th>
                  <th className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">{t('1/2 Chest')}(")</th>
                  <th className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">{t('Sleeve Length')}(")</th>
                  <th className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">{t('1/2 Sleeve Bottom')}(")</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">M</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">26"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">18 7/8"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">12 3/8"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">6 1/8"</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">L</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">26 3/4"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">19 5/8"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">13"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">6 1/4"</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">XL</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">27 1/2"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">20 1/2"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">13 5/8"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">6 1/2"</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">2XL</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">28 3/8"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">21 1/4"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">14 1/8"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">6 3/4"</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center font-medium text-black">3XL</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">29 1/8"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">22"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">14 3/4"</td>
                  <td className="border border-gray-300 px-3 md:px-4 py-3 text-center text-black">6 7/8"</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </Modal>
    </>
  );
}