import React, { Suspense, useEffect } from "react";
import { MyPageProps, SlugMyPageProps } from "@/lib/@types/base";
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";
import { executeGraphQL } from "@/lib/graphql";
import {
	ProductDetailsDocument,
	ProductCategoriesListDocument,
} from "@/gql/graphql";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { defaultLocale } from "@/config";
import { handleGraphqlLocale, productTranslationName } from "@/lib/utils/util";
import { Breadcrumb, Empty, Skeleton } from "antd";
import ProductDescription from "@/components/Product/ProductDescription";
import { NotFouned } from "@/components/404";
import ImageGallerySwiper from "@/components/Product/ImageGallerySwiper";
import Information from "@/components/Product/Information";
import { getProductDetailSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import WhoViewed from "@/components/Product/WhoViewed";
import Dproduct from "@/components/3Dproduct";
import { TestImageGallery } from "@/components/Product/ImageGallerySwiper";
import { type WithContext, type Product } from "schema-dts";
import ProductPaddleFeatureImg from "@/components/Product/product-paddle-feature-img";
import ProductBreadcrumb from "@/components/ProductsLayoutPage/ProductBreadcrumb";
import Partner from "@/components/ZC/Partner";
const getImagesForProduct = (product: any) => {
	if (!product?.media) return [];
	try {
		return JSON.parse(product.media);
	} catch (error) {
		console.error("Error parsing product media:", error);
		return [];
	}
};
export const generateMetadata = async (props: SlugMyPageProps) => {
	try {
		const seo = await getProductDetailSeo(props);
		// @ts-ignore
		return generateSeo(props, { ...seo, currentPath: `/product/${props.params.slug}`, ogType: "article" });
	} catch (error) {
		// 如果获取 SEO 数据失败，返回一个基础的 metadata
		return generateSeo(props, {
			title: props.params.slug,
			description: "",
			currentPath: `/product/${props.params.slug}`,
			ogType: "article",
			keywords: [],
		}) as any;
	}
};
export default async function ProductSlug({ params }: MyPageProps) {
	unstable_setRequestLocale(params.locale);
	const t = await getTranslations();
	const channel = (await getChannelLanguageMap())[defaultLocale];
	const { product } = await executeGraphQL(ProductDetailsDocument, {
		withAuth: false,
		variables: {
			locale: handleGraphqlLocale(params.locale || defaultLocale),
			channel: channel,
			slug: params.slug,
			filterKeywordKey: "",
		},
		revalidate: 60,
	});
	if (!product) {
		return <NotFouned link={"/products"} />;
	}

	// 获取分类层级信息
	let parentCategory = null;
	let categoryName = "";

	if (product.category) {
		try {
			const { categories } = await executeGraphQL(ProductCategoriesListDocument, {
				withAuth: false,
				variables: {
					channel,
					locale: handleGraphqlLocale(params.locale || defaultLocale),
					first: 100
				},
				revalidate: 60,
			});

			// 扁平化所有分类以便于查找父子关系
			const categoryParentMap = new Map();
			const flattenCategories = (categories: any[], parent = null) => {
				if (!categories) return;

				categories.forEach((category: any) => {
					if (category && category.node) {
						// 如果有父分类，记录映射关系
						if (parent) {
							categoryParentMap.set(category.node.slug, parent);
						}

						// 递归处理子分类
						if (category.node.children &&
							category.node.children.edges &&
							category.node.children.edges.length > 0) {
							flattenCategories(category.node.children.edges, category.node);
						}
					}
				});
			};

			flattenCategories(categories.edges);

			// 查找当前产品分类的父分类
			parentCategory = categoryParentMap.get(product.category.slug);

			// 获取分类名称
			categoryName = params.locale === defaultLocale ?
				(product.category.translation?.name || product.category.name) :
				(product.category.translation?.name || product.category.name);
		} catch (error) {
			console.error("Error fetching category hierarchy:", error);
		}
	}
	// 添加产品的结构化数据
	const images = getImagesForProduct(product);
	const description: any = product?.description ? JSON.parse(product?.description) : null;
	// 提取并清理描述文本，只使用第一个 block
	const getCleanDescription = (desc: any) => {
		if (!desc?.blocks || !desc.blocks[0]?.data?.text) return "";

		// 只处理第一个 block 的文本
		const text = desc.blocks[0].data.text
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">")
			.replace(/&quot;/g, '"')
			.replace(/&amp;/g, "&")
			.replace(/<[^>]+>/g, " ")
			.replace(/\\n/g, " ")
			.replace(/\\\\/g, "\\") // 处理多余的转义反斜杠
			.replace(/\\"/g, '"') // 处理转义的引号
			.replace(/,\\"/g, '"') // 处理特殊情况
			.replace(/\s+/g, " ")
			.trim();

		return text;
	};

	const productJsonLd: WithContext<Product> = {
		"@context": "https://schema.org",
		"@type": "Product",
		name: product.translation?.name || product.name || "",
		description: getCleanDescription(description) || "",
		image: Array.isArray(images) ? images.map((image: any) => image.url) : [],
		offers: {
			"@type": "Offer",
			availability: product.isAvailable ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
			priceCurrency: product.pricing?.priceRange?.start?.gross.currency,
			price: product.pricing?.priceRange?.start?.gross.amount,
			seller: {
				"@type": "Organization",
				name: process.env.NEXT_PUBLIC_COMPANY_NAME,
				url: process.env.NEXT_PUBLIC_SITE_URL,
				logo: `${process.env.NEXT_PUBLIC_SITE_URL}/image/logo-r.png`,
			},
		},
	};
	return (
		<>
			<script
				id={`product-structured-data-${params.slug}`}
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(productJsonLd),
				}}
			/>
			<div className="border-t border-b border-gray-200">
				<div className="container">
					<ProductBreadcrumb
						parentCategory={parentCategory}
						currentTitle={categoryName || product.category?.name || ""}
						currentCategory={product.category}
						locale={params.locale}
						isProductsPage={false}
						productName={productTranslationName(product.translation?.name) || productTranslationName(product.name)}
					/>
				</div>
			</div>
			<section className="max-lg:py-12 py-16">
				<div className="container grid grid-cols-8  gap-9  bg-white max-lg:grid-cols-1">
					<>
						{/* 图片 */}
						<div className="col-span-4 overflow-hidden max-lg:px-2 max-md:col-span-1 max-md:mt-10">
							<ImageGallerySwiper product={product} />
							{/* <TestImageGallery /> */}
						</div>

						{/* 详情 */}
						<div className="col-span-4 max-lg:px-2 max-md:col-span-1">
							<Information product={product} currentSlug={params.slug} />
						</div>
					</>
				</div>
				{/* 3D产品 */}
				{/* <div className="box-border px-2">
					<Dproduct product={product} />
				</div> */}
				<h2 className={`text-center text-4xl mt-32 mb-16 ${params.locale === defaultLocale ? "ib" : "font-semibold"}`}>{t("base.product-details")}</h2>
				<ProductPaddleFeatureImg productKey={params.slug} />
				<div className="container my-16 gap-8 max-md:grid-cols-1">
					<div className="mb-5 max-md:col-span-1">
						<ProductDescription product={product} locale={params.locale} />
					</div>
				</div>
				<div className="container">
					<WhoViewed locale={params.locale} channel={channel} slug={product.category.slug} />
				</div>
			</section>
			<Partner />
		</>
	);
}
