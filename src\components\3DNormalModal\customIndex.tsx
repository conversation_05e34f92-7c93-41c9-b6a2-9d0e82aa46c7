"use client";
import React, { useEffect, useState, Suspense, useRef, useMemo } from "react";
import { Canvas, use<PERSON>rame, useLoader, useThree } from "@react-three/fiber";
// @ts-ignore
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
// @ts-ignore
import { DRACOLoader } from "three-stdlib"; // 从 three-stdlib 导入 DRACOLoader
// @ts-ignore
import { OBJLoader } from "three/examples/jsm/loaders/OBJLoader";
// @ts-ignore
import { FBXLoader } from "three/examples/jsm/loaders/FBXLoader";
// @ts-ignore
import { STLLoader } from "three/examples/jsm/loaders/STLLoader.js";

// @ts-ignore
import { Rhino3dmLoader } from "three/examples/jsm/loaders/3DMLoader.js";
import {
	Environment,
	OrbitControls,
	Html,
	Bounds,
	Loader,
	Effects,
	Grid,
	useTexture,
	ContactShadows,
} from "@react-three/drei";
import * as THREE from "three";
import { LoadStep } from "@/lib/utils";
// @ts-ignore
import { OutlineEffect } from "three/examples/jsm/effects/OutlineEffect";

// 动态选择合适的加载器
export const chooseLoader = (url: string) => {
	const ext = url.split(".").pop()?.toLowerCase();
	switch (ext) {
		case "gltf":
		case "glb":
			return GLTFLoader;
		case "3dm":
			return Rhino3dmLoader;
		case "obj":
			return OBJLoader;
		case "fbx":
			return FBXLoader;
		case "stl":
			return STLLoader;
		case "stp":
		case "step":
			return "stp"; // 保留 stp 文件的处理
		default:
			throw new Error(`Unsupported file format: ${url}`);
	}
};

// 缩放视图以适应3D对象并设置中心点
const zoomExtents = (
	object: THREE.Object3D,
	camera: THREE.Camera,
	controls: THREE.EventDispatcher | null,
	zoomed: boolean,
	onBottomYChange?: (y: number) => void,
) => {
	if (zoomed) return;
	const box = new THREE.Box3().setFromObject(object);
	const size = box.getSize(new THREE.Vector3());
	const center = box.getCenter(new THREE.Vector3());

	// 只计算模型底部的Y轴位置，不移动模型
	const bottomY = box.min.y;

	// 通知父组件模型底部的Y轴位置
	if (onBottomYChange) {
		onBottomYChange(bottomY);
	}

	// 计算合适的相机距离
	const maxDim = Math.max(size.x, size.y, size.z);
	const fov = camera instanceof THREE.PerspectiveCamera ? camera.fov : 50;
	const cameraZ = Math.abs(maxDim / Math.sin((fov * Math.PI) / 360)) * 0.4;

	camera.position.set(center.x + cameraZ * 0.4, center.y + cameraZ * 0.4, center.z + cameraZ);
	camera.lookAt(center);

	if (controls) {
		// @ts-ignore
		controls.target.copy(center);
		// @ts-ignore
		controls.update();
	}
};
export type PreviewObject = {
	rename?: string;
	meshName: string;
	materialBaseImg?: string;
	materialNormalImg?: string;
	materialRoughnessImg?: string;
	materialMetalnessImg?: string;
	materialAoImg?: string;
	patternImage?: string;
	color?: string;
};
// Model 组件：处理 3D 文件的下载、解析和渲染
const Model: React.FC<{
	modalUrl: string;
	fileName: string;
	arrayBuffer?: ArrayBuffer;
	metalness?: number;
	roughness?: number;
	color?: string;
	isHovered: boolean;
	IsRotate: boolean;
	onProgress?: (progress: number) => void; // 添加进度回调函数

	//预览对象
	preViewObj: PreviewObject[];
	onBottomYChange?: (y: number) => void; // 添加新的回调函数
}> = ({
	modalUrl,
	fileName,
	arrayBuffer,
	metalness = 0.4,
	roughness = 0.3,
	color = "#cccccc",
	isHovered,
	IsRotate,
	onProgress,
	preViewObj,
	onBottomYChange,
}) => {
	const [object, setObject] = useState<THREE.Object3D | null>(null);
	const [zoomed, setZoomed] = useState(false);
	const { camera, controls } = useThree();
	const groupRef = useRef<THREE.Group>(null);
	const isLoadedRef = useRef(false);
	const baseScaleRef = useRef<number>(1);
	const initialMaterials = useRef<Map<string, THREE.Material>>(new Map());

	// 添加设置阴影的函数
	const setupShadows = (obj: THREE.Object3D) => {
		obj.traverse((child) => {
			if (child instanceof THREE.Mesh) {
				child.castShadow = true;
				child.receiveShadow = true;
				if (child.material) {
					child.material.shadowSide = THREE.FrontSide;
				}
			}
		});
	};

	const loadModel = async () => {
		try {
			let buffer = arrayBuffer;
			if (!buffer) {
				// 添加加载进度监控
				const response = await fetch(modalUrl);

				// 如果服务器支持 content-length 头，可以获取总大小
				const contentLength = Number(response.headers.get("content-length") || 0);
				const reader = response.body?.getReader();

				if (reader && contentLength && onProgress) {
					buffer = await new Promise(async (resolve) => {
						let receivedLength = 0;
						const chunks: Uint8Array[] = [];

						while (true) {
							const { done, value } = await reader.read();

							if (done) {
								break;
							}

							chunks.push(value);
							receivedLength += value.length;

							// 更新加载进度
							onProgress(Math.min(receivedLength / contentLength, 0.8)); // 最多到80%，剩余20%留给解析模型
						}

						// 合并所有接收到的块
						const chunksAll = new Uint8Array(receivedLength);
						let position = 0;
						for (const chunk of chunks) {
							chunksAll.set(chunk, position);
							position += chunk.length;
						}

						resolve(chunksAll.buffer);
					});
				} else {
					// 回退到普通方式
					buffer = await response.arrayBuffer();
				}
			}

			const loaderType = chooseLoader(fileName);
			let loadedObject;

			if (loaderType === GLTFLoader) {
				// 创建并设置 DracoLoader
				const dracoLoader = new DRACOLoader();
				dracoLoader.setDecoderPath("/draco/"); // 设置解码器路径（可以使用CDN或本地路径）

				const loader = new GLTFLoader();
				loader.setDRACOLoader(dracoLoader); // 将 Draco 解码器传递给 GLTFLoader

				// 添加onProgress回调
				loadedObject = await new Promise((resolve, reject) => {
					loader.parse(
						buffer,
						"",
						(object: any) => {
							if (onProgress) onProgress(1); // 加载完成
							resolve(object.scene || object);
						},
						// 解析进度
						(xhr: any) => {
							if (onProgress && xhr.lengthComputable) {
								// 从80%到100%的进度
								const progress = 0.8 + (xhr.loaded / xhr.total) * 0.2;
								onProgress(progress);
							}
						},
						reject,
					);
				});
			} else if (loaderType === "stp") {
				// 处理 stp 格式的加载
				loadedObject = await LoadStep(modalUrl, buffer);
			} else {
				const loader = new loaderType();
				if (loader instanceof Rhino3dmLoader) {
					loader.setLibraryPath("/rhino3dm/");
					loadedObject = await new Promise((resolve, reject) => {
						try {
							// 使用parse方法处理buffer数据
							const onLoad = (object: THREE.Object3D) => {
								resolve(object);
							};
							loader.parse(buffer as ArrayBuffer, onLoad);
						} catch (error) {
							console.error("Error during 3dm parsing:", error);
							reject(error);
						}
					});
				} else if (loader instanceof STLLoader) {
					const material = loader.parse(buffer as ArrayBuffer);
					loadedObject = new THREE.Mesh(
						material,
						new THREE.MeshStandardMaterial({
							color,
							metalness,
							roughness,
						}),
					);
				} else {
					loadedObject = await new Promise((resolve, reject) => {
						loader.parse(buffer, "", (object: any) => resolve(object.scene || object), reject);
					});
				}
			}

			// 创建一个组来包含模型
			const group = new THREE.Group();
			group.add(loadedObject);
			setupShadows(group); // 为整个组设置阴影

			// 计算中心点并调整位置
			const box = new THREE.Box3().setFromObject(loadedObject);
			const center = box.getCenter(new THREE.Vector3());
			loadedObject.position.sub(center);

			setObject(group);
			isLoadedRef.current = true;
			zoomExtents(group, camera, controls, zoomed, onBottomYChange);
			setZoomed(true);
		} catch (error) {
			console.error("Error loading model:", error);
		}
	};

	useEffect(() => {
		loadModel();
	}, [modalUrl, arrayBuffer]);

	// 预览时，根据预览对象更新材质
	useEffect(() => {
		if (!object || !preViewObj) return;

		const textureLoader = new THREE.TextureLoader();

		// 创建一个Map来存储已经更新过的mesh
		const updatedMeshes = new Set<string>();

		// 首次加载时保存初始材质
		if (initialMaterials.current.size === 0) {
			object.traverse((child: any) => {
				if (child instanceof THREE.Mesh) {
					initialMaterials.current.set(child.name, child.material.clone());
				}
			});
		}

		object.traverse((child: any) => {
			if (!(child instanceof THREE.Mesh)) return;

			const previewItem = preViewObj.find((item) => item.meshName === child.name);
			if (!previewItem) return;

			// 如果这个mesh已经更新过，跳过
			if (updatedMeshes.has(child.name)) return;

			const {
				materialBaseImg,
				materialNormalImg,
				materialRoughnessImg,
				materialMetalnessImg,
				materialAoImg,
				patternImage,
				color,
			} = previewItem;
			const isAllEmpty =
				!materialBaseImg &&
				!patternImage &&
				!color &&
				!materialNormalImg &&
				!materialRoughnessImg &&
				!materialMetalnessImg &&
				!materialAoImg;

			if (isAllEmpty) {
				const initialMaterial = initialMaterials.current.get(child.name);
				if (initialMaterial) {
					child.material = initialMaterial.clone();
					child.material.needsUpdate = true;
				}
				updatedMeshes.add(child.name);
				return;
			}

			// 直接新建一个物理渲染材质
			const newMaterial = new THREE.MeshStandardMaterial({
				color: color ? new THREE.Color(color) : 0xffffff,
				metalness: child.material.metalness,
				roughness: child.material.roughness,
				transparent: child.material.transparent,
				blending: child.material.blending,
			});

			// 创建一个Promise数组来跟踪所有纹理加载
			const texturePromises: Promise<void>[] = [];

			// 加载基础贴图
			if (materialBaseImg) {
				texturePromises.push(
					new Promise<void>((resolve) => {
						textureLoader.load(materialBaseImg, (texture) => {
							// 自动适配图片比例调整UV
							if (!child.geometry.attributes.uv) {
								child.geometry.computeBoundingBox();
								const bbox = child.geometry.boundingBox;
								const position = child.geometry.attributes.position;
								const uvArray = [];
								const imgW = texture.image.width;
								const imgH = texture.image.height;
								const imgAspect = imgW / imgH;
								const meshW = bbox.max.x - bbox.min.x;
								const meshH = bbox.max.z - bbox.min.z;
								const meshAspect = meshW / meshH;
								let scaleU = 1,
									scaleV = 1;

								if (imgAspect > meshAspect) {
									// 图片更宽，U方向要压缩
									scaleU = meshAspect / imgAspect;
								} else {
									// 图片更高，V方向要压缩
									scaleV = imgAspect / meshAspect;
								}
								for (let i = 0; i < position.count; i++) {
									uvArray.push(
										((position.getX(i) - bbox.min.x) / meshW) * scaleU,
										((position.getZ(i) - bbox.min.z) / meshH) * scaleV,
									);
								}
								const uvAttribute = new THREE.Float32BufferAttribute(uvArray, 2);
								child.geometry.setAttribute("uv", uvAttribute);
								child.geometry.attributes.uv.needsUpdate = true;
							}
							texture.wrapS = THREE.RepeatWrapping;
							texture.wrapT = THREE.RepeatWrapping;
							texture.colorSpace = THREE.SRGBColorSpace;
							texture.repeat.set(1, 1);
							newMaterial.map = texture;
							newMaterial.needsUpdate = true;
							resolve();
						});
					}),
				);
			}

			// 加载法线贴图
			if (materialNormalImg) {
				texturePromises.push(
					new Promise<void>((resolve) => {
						textureLoader.load(materialNormalImg, (texture) => {
							texture.wrapS = THREE.RepeatWrapping;
							texture.wrapT = THREE.RepeatWrapping;
							texture.repeat.set(10, 10);
							newMaterial.normalMap = texture;
							newMaterial.normalScale.set(1, 1);
							newMaterial.needsUpdate = true;
							resolve();
						});
					}),
				);
			}

			// 加载粗糙度贴图
			if (materialRoughnessImg) {
				texturePromises.push(
					new Promise<void>((resolve) => {
						textureLoader.load(materialRoughnessImg, (texture) => {
							// texture.wrapS = THREE.RepeatWrapping;
							// texture.wrapT = THREE.RepeatWrapping;
							// texture.repeat.set(10, 10);
							newMaterial.roughnessMap = texture;
							newMaterial.needsUpdate = true;
							resolve();
						});
					}),
				);
			}

			// 加载金属度贴图
			if (materialMetalnessImg) {
				texturePromises.push(
					new Promise<void>((resolve) => {
						textureLoader.load(materialMetalnessImg, (texture) => {
							// texture.wrapS = THREE.RepeatWrapping;
							// texture.wrapT = THREE.RepeatWrapping;
							// texture.repeat.set(10, 10);
							newMaterial.metalnessMap = texture;
							newMaterial.needsUpdate = true;
							resolve();
						});
					}),
				);
			}

			// 加载环境光遮蔽贴图
			if (materialAoImg) {
				texturePromises.push(
					new Promise<void>((resolve) => {
						textureLoader.load(materialAoImg, (texture) => {
							texture.wrapS = THREE.RepeatWrapping;
							texture.wrapT = THREE.RepeatWrapping;
							texture.repeat.set(10, 10);
							newMaterial.aoMap = texture;
							newMaterial.aoMapIntensity = 1.0;
							newMaterial.needsUpdate = true;
							resolve();
						});
					}),
				);
			}

			// 加载图案贴图
			if (patternImage) {
				texturePromises.push(
					new Promise<void>((resolve) => {
						textureLoader.load(patternImage, (texture) => {
							texture.wrapS = THREE.RepeatWrapping;
							texture.wrapT = THREE.RepeatWrapping;
							texture.repeat.set(10, 10);
							texture.colorSpace = THREE.SRGBColorSpace;
							newMaterial.map = texture;
							newMaterial.transparent = true;
							newMaterial.blending = THREE.NormalBlending;
							newMaterial.needsUpdate = true;
							resolve();
						});
					}),
				);
			}

			// 等待所有纹理加载完成后再设置材质
			Promise.all(texturePromises).then(() => {
				// 如果没有设置新的颜色，使用原始材质的颜色
				// if (!color) {
				// 	const initialMaterial = initialMaterials.current.get(child.name);
				// 	if (initialMaterial && "color" in initialMaterial) {
				// 		newMaterial.color.copy((initialMaterial as THREE.MeshStandardMaterial).color);
				// 	}
				// }
				child.material = newMaterial;
				updatedMeshes.add(child.name);
			});
		});
	}, [object, preViewObj]);
	// 修改 useFrame 逻辑
	useFrame((state, delta) => {
		if (object && isLoadedRef.current) {
			if (IsRotate) {
				object.rotation.y += delta * 0.4;
			}
		}
	});
	return object ? (
		<group ref={groupRef}>
			<primitive object={object} dispose={null} />
		</group>
	) : null;
};

// 主要组件：加载和渲染 3D 文件
const LoadNormalModal: React.FC<{
	modalUrl: string;
	className?: string;
	fileName: string;
	arrayBuffer?: ArrayBuffer;
	metalness?: number;
	roughness?: number;
	color?: string;
	IsRotate: boolean;
	backgroundColor: string;
	preViewObj?: PreviewObject[];
	preloaded?: boolean;
}> = ({
	modalUrl,
	className,
	fileName,
	arrayBuffer,
	metalness,
	roughness,
	color,
	IsRotate = false,
	backgroundColor = "#dddddd",
	preViewObj = [],
	preloaded = false,
}) => {
	const [isHovered, setIsHovered] = useState(false);
	const [isModelLoaded, setIsModelLoaded] = useState(false);
	const [loadingProgress, setLoadingProgress] = useState(0);
	const [bottomY, setBottomY] = useState(0); // 添加新的state

	// console.log(backgroundColor,'backgroundColor');

	// 进度更新处理函数
	const handleProgress = (progress: number) => {
		setLoadingProgress(progress);
		if (progress >= 1) {
			setIsModelLoaded(true);
		}
	};
	return (
		<Canvas
			shadows // 启用阴影
			// camera={{
			// 	position: [0, 2, 4],
			// 	fov: 50,
			// 	near: 0.1,
			// 	far: 1000
			// }}
			className={`${className || "relative"}`}
			onCreated={({ gl }) => {
				if (typeof window !== "undefined") {
					gl.setPixelRatio(Math.min(window.devicePixelRatio, 2.0));
					gl.shadowMap.enabled = true;
					gl.shadowMap.type = THREE.PCFSoftShadowMap;
					gl.toneMapping = THREE.ACESFilmicToneMapping;
					gl.toneMappingExposure = 1.4;
				}
			}}
			onWheel={(e) => e.stopPropagation()}
			onMouseEnter={() => isModelLoaded && setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			{/* 主环境光 */}
			<ambientLight intensity={0.1} />

			{/* 主平行光源 */}
			<directionalLight
				position={[10, 20, 10]}
				intensity={1}
				castShadow
				shadow-mapSize-width={4096}
				shadow-mapSize-height={4096}
				shadow-camera-left={-20}
				shadow-camera-right={20}
				shadow-camera-top={20}
				shadow-camera-bottom={-20}
				shadow-camera-near={0.1}
				shadow-camera-far={100}
				shadow-bias={-0.0001}
			/>

			{/* 补充光源 */}
			<directionalLight position={[-5, 5, -5]} intensity={0.3} />

			<ContactShadows resolution={512} position={[0, bottomY, 0]} opacity={1} scale={10} blur={1} far={0.8} />
			<color attach="background" args={[backgroundColor]} />
			<Environment preset={null} background={false} blur={1} path="/3DModals/" files={["venice_sunset_1k.hdr"]} />

			{!isModelLoaded && (
				<Html center>
					<div className="flex items-center justify-center">
						<div className="relative h-12 w-12">
							<div className="absolute inset-0 animate-spin rounded-full border-4 border-gray-200 border-t-gray-900"></div>
							<div className="absolute inset-0 flex items-center justify-center text-sm font-medium text-gray-900">
								{Math.round(loadingProgress * 100)}%
							</div>
						</div>
					</div>
				</Html>
			)}

			<Suspense fallback={null}>
				<Model
					modalUrl={modalUrl}
					fileName={fileName}
					arrayBuffer={arrayBuffer}
					metalness={metalness}
					roughness={roughness}
					color={color}
					isHovered={isHovered}
					IsRotate={IsRotate}
					onProgress={handleProgress}
					preViewObj={preViewObj}
					onBottomYChange={setBottomY}
				/>
			</Suspense>
			<OrbitControls
				makeDefault
				enableZoom={true}
				minDistance={1}
				maxDistance={50}
				enableDamping={true}
				dampingFactor={0.05}
				rotateSpeed={0.6}
				target={new THREE.Vector3(0, 0, 0)}
				enablePan={false}
			/>
		</Canvas>
	);
};

export default LoadNormalModal;
