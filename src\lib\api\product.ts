// 获取特色产品列表
import { defaultLocale } from "@/config";
import {
	ProductListByCategoryDocument,
	ProductListByCategoryPaginatedDocument,
	ProductListByIdsDocument,
	ProductListPaginatedDocument,
	SearchProductsDocument,
	ProductListByCollectionDocument,
} from "@/gql/graphql";
import { executeGraphQL } from "@/lib/utils/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";

// 获取特色产品
export const getFeaturedProducts = async (locale: string, channel: string) => {
	const data = await executeGraphQL(ProductListByCategoryDocument, {
		variables: {
			slug: "featured-products",
			channel: channel,
			locale: handleGraphqlLocale(locale),
		},
		revalidate: 60,
	});
};
// 获取收藏产品
export const getCollectionProducts = async ({ slug, locale, channel }) => {
	return await executeGraphQL(ProductListByCollectionDocument, {
		variables: {
			slug,
			channel: channel,
			locale: handleGraphqlLocale(locale),
		},
		revalidate: 60,
	});
}


export const fetchProductData = async ({ locale, channel = "default-channel", after = "" }) => {
	console.log(locale, channel, 'locale, channel');

	return await executeGraphQL(ProductListPaginatedDocument, {
		withAuth: false,
		variables: {
			first: 6,
			after,
			locale: handleGraphqlLocale(locale || defaultLocale),
			channel: channel,
		},
		revalidate: 300,
	});
};
// 2
export const fetchProductSearchData = async ({
	locale,
	channel = "default-channel", // 确保提供默认的 channel
	searchQuery,
	after = "",
	first = 9,  // 默认每页9条
	sortBy = "NAME" as any,  // 默认按名称排序
	sortDirection = "ASC" as any  // 默认升序
}) => {
	return await executeGraphQL(SearchProductsDocument, {
		withAuth: false,
		variables: {
			search: searchQuery.trim(),  // 参数名从 searchQuery 改为 search
			first,
			after,
			channel,
			locale: handleGraphqlLocale(locale || defaultLocale),
			sortBy,
			sortDirection
		},
		revalidate: 60,
	});
};
export const fetchProductByCategoryData = async ({ slug, locale, channel, after = "" }) => {
	return await executeGraphQL(ProductListByCategoryPaginatedDocument, {
		withAuth: false,
		variables: {
			slug,
			first: 9,
			after,
			locale: handleGraphqlLocale(locale || defaultLocale),
			channel: channel,
		},
		revalidate: 60,
	});
};

// 搜索产品  返回产品id
export const searchProducts = async ({
	searchValue,
	page,
	limit,
}: {
	page: string;
	limit: string;
	searchValue: string;
}) => {
	const baseUrl = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/saleor/product/${searchValue}?page=${page}&limit=${limit}`;
	const app_token = process.env.NEXT_PUBLIC_SALEOR_APP_TOKEN;

	const res = await fetch(baseUrl, {
		method: "GET",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${app_token}`,
		},
		next: { revalidate: 600 },
	}).then((r) => r.json());
	return res;
};

// 根据id查产品
export const fetchSearchProductsData = async ({ ids, locale, channel }) => {
	return await executeGraphQL(ProductListByIdsDocument, {
		withAuth: false,
		variables: {
			first: 9,
			locale: handleGraphqlLocale(locale || defaultLocale),
			ids,
			channel: channel,
		},
		revalidate: 60,
	});
};

