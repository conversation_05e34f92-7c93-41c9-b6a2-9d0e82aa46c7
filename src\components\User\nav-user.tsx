"use client";
import React, { useEffect, useState } from "react";

import LoginForm from "@/components/Form/User/login-form";
import SignUpForm from "@/components/Form/User/sign-up-form";
import ResetPasswordForm from "@/components/Form/User/reset-password-form";

import { Link } from "@/navigation";
import LoginModal from "./login-modal";
import { useTranslations } from "next-intl";
import { useUserStore } from "@/store/user.store";
import { deleteCookie, setCookie } from "cookies-next";
import { useRouter } from "@/navigation";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
import { message } from "antd";
import { LogoutOutlined, UserSwitchOutlined, UserAddOutlined } from "@ant-design/icons";

type Props = {
	children: React.ReactNode;
};

export type SignSwitchProps = {
	activeId: number;
	setActiveId: (id: number) => void;
	setOpenModal: (open: boolean) => void;
};

export function SignSwitch({ activeId = 1, setActiveId, setOpenModal }: SignSwitchProps) {
	const t = useTranslations();
	const [isLoading, setIsLoading] = useState(false);
	const list = [
		{
			id: 1,
			label: "common.Sign_in",
			description: "message.e6d7f9862ff4ff40a9eaf8523c08e6c4624c",
			component: <LoginForm setLoginModalOn={setOpenModal} />,
		},
		{
			id: 2,
			label: "common.Sign_Up",
			description: "message.b0e9e6e07105254449b889ccac3a508d8c88",
			component: <SignUpForm setActive={setActiveId} />,
		},
		{
			id: 3,
			label: "common.Reset_password",
			description: "message.e6d7f9862ff4ff40a9eaf8523c08e6c4624c",
			component: <ResetPasswordForm setLoginModalOn={setOpenModal} />,
		},
	];
	const googleLoginUrl = process.env.NEXT_PUBLIC_GOOGLE_LOGIN_URL;
	const handleGoogleLogin = async () => {
		try {
			setIsLoading(true);
			setCookie("googleLoginRedirectPath", window.location.href);

			const res = await fetch(googleLoginUrl);
			const data = (await res.json()) as any;
			console.log(data, "data");

			window.location.href = data.detail.approval_url;
		} catch (error) {
			console.error("Google login error:", error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="mx-auto w-[700px] overflow-hidden bg-white max-md:max-w-[80vw]">
			{activeId === 1 && (
				<div className="p-10">
					<div className="mb-8 flex items-center justify-between">
						<h3 className="text-[28px] font-light tracking-[-0.5px] text-gray-900 max-md:text-2xl">
							{t("common.Log_in")}
						</h3>
					</div>

					<div>{list[activeId - 1].component}</div>

					<div className="mt-4 flex items-center justify-between">
						<span className="cursor-pointer text-[13px] text-[#4285F4]" onClick={() => setActiveId(3)}>
							{t("common.New_customer")}?
						</span>
						<button
							onClick={() => setActiveId(2)}
							className="inline-flex items-center text-[13px] font-medium text-[#4285F4] transition-all duration-200 hover:translate-x-0.5 hover:text-[#357ABD]"
						>
							{t("common.Create_your_account")}{" "}
							<span aria-hidden="true" className="ml-1.5">
								&rarr;
							</span>
						</button>
					</div>

					{/* OR Divider */}
					<div className="relative my-4">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t-2 border-gray-200"></div>
						</div>
						<div className="relative flex justify-center">
							<span className="bg-white px-6 text-[13px] font-medium text-gray-500">OR</span>
						</div>
					</div>

					<button
						onClick={handleGoogleLogin}
						disabled={isLoading}
						className="group relative flex w-full items-center justify-center rounded-[4px] border border-[#4285F4] bg-[#4285F4] px-5 py-2.5 text-[14px] font-medium text-white transition-all duration-200 hover:border-[#357ABD] hover:bg-[#357ABD] focus:outline-none disabled:cursor-not-allowed disabled:opacity-70"
					>
						<div className="flex items-center">
							<div className="mr-3 flex items-center justify-center rounded-full bg-white p-[6px]">
								{isLoading ? (
									<div className="h-[18px] w-[18px] animate-spin rounded-full border-2 border-[#4285F4] border-t-transparent"></div>
								) : (
									<GoogleSVG />
								)}
							</div>
							{t("common.Login_with_Google")}
						</div>
					</button>
				</div>
			)}
			{activeId === 2 && (
				<div className="p-10">
					<div className="mb-8 flex items-center justify-between max-md:mb-0">
						<h3 className="text-[28px] font-light tracking-[-0.5px] text-gray-900 max-md:text-2xl">
							{t("common.Create_your_account")}
						</h3>
					</div>
					<div>{list[activeId - 1].component}</div>

					<div className="mt-4 flex items-center justify-between">
						<span className="text-[13px] text-gray-500 ">{t("common.Already_have_account")}?</span>
						<button
							onClick={() => setActiveId(1)}
							className="inline-flex items-center text-[13px] font-medium text-[#4285F4] transition-all duration-200 hover:translate-x-0.5 hover:text-[#357ABD]"
						>
							{t("common.Sign_in")}{" "}
							<span aria-hidden="true" className="ml-1.5">
								&rarr;
							</span>
						</button>
					</div>

					{/* OR Divider */}
					<div className="relative my-4">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t-2 border-gray-200"></div>
						</div>
						<div className="relative flex justify-center">
							<span className="bg-white px-6 text-[13px] font-medium text-gray-500">OR</span>
						</div>
					</div>

					{/* Google Login Button */}
					<button
						onClick={handleGoogleLogin}
						disabled={isLoading}
						className="group relative flex w-full items-center justify-center rounded-[4px] border border-[#4285F4] bg-[#4285F4] px-5 py-2.5 text-[14px] font-medium text-white transition-all duration-200 hover:border-[#357ABD] hover:bg-[#357ABD] focus:outline-none disabled:cursor-not-allowed disabled:opacity-70"
					>
						<div className="flex items-center">
							<div className="mr-3 flex items-center justify-center rounded-full bg-white p-[6px]">
								{isLoading ? (
									<div className="h-[18px] w-[18px] animate-spin rounded-full border-2 border-[#4285F4] border-t-transparent"></div>
								) : (
									<GoogleSVG />
								)}
							</div>
							{t("common.Login_with_Google")}
						</div>
					</button>
				</div>
			)}
			{activeId === 3 && (
				<div className="p-10">
					<div className="mb-8 flex items-center justify-between">
						<h3 className="text-[28px] font-light tracking-[-0.5px] text-gray-900 max-md:text-2xl">
							{t("common.Reset_password")}
						</h3>
					</div>

					<div>{list[activeId - 1].component}</div>

					<div className="mt-4 flex items-center justify-between">
						<span className="cursor-pointer text-[13px] text-[#4285F4] " onClick={() => setActiveId(3)}>
							{t("common.New_customer")}?
						</span>
						<button
							onClick={() => setActiveId(2)}
							className="inline-flex items-center text-[13px] font-medium text-[#4285F4] transition-all duration-200 hover:translate-x-0.5 hover:text-[#357ABD]"
						>
							{t("common.Create_your_account")}{" "}
							<span aria-hidden="true" className="ml-1.5">
								&rarr;
							</span>
						</button>
					</div>

					{/* OR Divider */}
					<div className="relative my-4">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t-2 border-gray-200"></div>
						</div>
						<div className="relative flex justify-center">
							<span className="bg-white px-6 text-[13px] font-medium text-gray-500">OR</span>
						</div>
					</div>

					<button
						onClick={handleGoogleLogin}
						disabled={isLoading}
						className="group relative flex w-full items-center justify-center rounded-[4px] border border-[#4285F4] bg-[#4285F4] px-5 py-2.5 text-[14px] font-medium text-white transition-all duration-200 hover:border-[#357ABD] hover:bg-[#357ABD] focus:outline-none disabled:cursor-not-allowed disabled:opacity-70"
					>
						<div className="flex items-center">
							<div className="mr-3 flex items-center justify-center rounded-full bg-white p-[6px]">
								{isLoading ? (
									<div className="h-[18px] w-[18px] animate-spin rounded-full border-2 border-[#4285F4] border-t-transparent"></div>
								) : (
									<GoogleSVG />
								)}
							</div>
							{t("common.Login_with_Google")}
						</div>
					</button>
				</div>
			)}
		</div>
	);
}

type NavUserList = {
	id: number;
	label: string;
	show: boolean;
	callback?: (item: NavUserList) => void;
	link?: string;
	icon?: JSX.Element;
};

export default function NavUser({ children }: Props) {
	const [openModal, setOpenModal] = useState(false);
	const [activeId, setActiveId] = useState(1);
	const [list, setList] = useState<NavUserList[]>([]);
	const { userInfo, getUserInfo } = useUserStore();
	const t = useTranslations();
	const router = useRouter();
	const { useSignOut } = useUserAuth();

	useEffect(() => {
		setList([
			{
				id: 1,
				label: "common.login",
				show: !userInfo,
				callback: (option: NavUserList) => {
					setOpenModal(true);
					setActiveId(option.id);
				},
				icon: <UserSwitchOutlined />,
			},
			{
				id: 2,
				label: "common.register",
				show: !userInfo,
				callback: (option: NavUserList) => {
					setOpenModal(true);
					setActiveId(option.id);
				},
				icon: <UserAddOutlined />,
			},
			{
				id: 3,
				label: "common.center",
				show: !!userInfo,
				link: "/center",
				icon: <UserSwitchOutlined />,
			},
			{
				id: 4,
				label: "common.logout",
				show: !!userInfo,
				callback: () => {
					deleteCookie("__user__login__info");
					deleteCookie("user-store");
					useSignOut();
					message.success(t("message.logout successful"));
				},
				icon: <LogoutOutlined />,
			},
		]);
	}, [userInfo]);

	useEffect(() => {
		getUserInfo();
	}, []);

	return (
		<div className="group relative">
			{children}
			<div
				className="absolute left-[-103px] top-full z-50 hidden min-w-[180px] flex-col rounded-md  bg-white  text-black  transition-all duration-200 before:absolute
      before:left-[50%] before:top-[-6px] before:h-3 before:w-3 before:translate-x-[-50%]
      before:rotate-45 before:border-l before:border-t before:border-gray-100 before:bg-white before:content-[''] group-hover:flex"
			>
				<div className="w-56 overflow-hidden rounded-md border border-gray-100 bg-white">
					{/* User menu items */}
					<div className="py-1">
						{list.map(
							(item) =>
								item.show && (
									<div
										key={item.id}
										id={item.id === 1 ? "web-login" : ""}
										className="transition-colors duration-150"
										onClick={() => item.callback && item.callback(item)}
									>
										{item.link ? (
											<Link
												href={item.link}
												className="flex w-full items-center gap-2.5 px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50"
											>
												<span className="text-gray-500">{item.icon}</span>
												<span>{t(item.label)}</span>
											</Link>
										) : (
											<button className="flex w-full items-center gap-2.5 px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50">
												<span className="text-gray-500">{item.icon}</span>
												<span>{t(item.label)}</span>
											</button>
										)}
									</div>
								),
						)}
					</div>
				</div>
			</div>
			<LoginModal
				openModal={openModal}
				setOpenModal={setOpenModal}
				activeId={activeId}
				setActiveId={setActiveId}
			/>
		</div>
	);
}

const GoogleSVG = () => {
	return (
		<svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
			<path
				fill="#EA4335"
				d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
			/>
			<path
				fill="#4285F4"
				d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
			/>
			<path
				fill="#FBBC05"
				d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
			/>
			<path
				fill="#34A853"
				d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
			/>
		</svg>
	);
};
