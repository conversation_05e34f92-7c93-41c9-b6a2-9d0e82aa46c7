"use client";
import React, { useEffect, useState, Suspense, useRef } from "react";
import { Can<PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
// @ts-ignore
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
// @ts-ignore
import { DRACOLoader } from "three-stdlib";  // 从 three-stdlib 导入 DRACOLoader
// @ts-ignore
import { OBJLoader } from "three/examples/jsm/loaders/OBJLoader";
// @ts-ignore
import { FBXLoader } from "three/examples/jsm/loaders/FBXLoader";
// @ts-ignore
import { STLLoader } from "three/examples/jsm/loaders/STLLoader.js";

// @ts-ignore
import { Rhino3dmLoader } from "three/examples/jsm/loaders/3DMLoader.js";
import { Environment, OrbitControls, Html, Bounds, Loader, Effects } from "@react-three/drei";
import * as THREE from "three";
import { LoadStep } from "@/lib/utils";
// @ts-ignore
import { OutlineEffect } from 'three/examples/jsm/effects/OutlineEffect';

// 动态选择合适的加载器
export const chooseLoader = (url: string) => {
	const ext = url.split('.').pop()?.toLowerCase();
	switch (ext) {
		case "gltf":
		case "glb":
			return GLTFLoader;
		case "3dm":
			return Rhino3dmLoader;
		case "obj":
			return OBJLoader;
		case "fbx":
			return FBXLoader;
		case "stl":
			return STLLoader;
		case "stp":
		case "step":
			return "stp";  // 保留 stp 文件的处理
		default:
			throw new Error(`Unsupported file format: ${url}`);
	}
};

// 缩放视图以适应3D对象并设置中心点
const zoomExtents = (object: THREE.Object3D, camera: THREE.Camera, controls: THREE.EventDispatcher | null, zoomed: boolean) => {
	if (zoomed) return;  // 如果已经缩放过，则跳过
	const box = new THREE.Box3().setFromObject(object);
	const size = box.getSize(new THREE.Vector3());
	const center = box.getCenter(new THREE.Vector3());
	const maxDim = Math.max(size.x, size.y, size.z);
	const cameraZ =  1 / maxDim;
	object.scale.set(cameraZ, cameraZ, cameraZ);
	camera.position.set(center.x, center.y, center.y);
	camera.lookAt(center);

	if (controls) {
		// @ts-ignore
		controls?.target.copy(center); // 设置旋转中心为物体的中心
		// @ts-ignore
		controls?.update();
	}
};

// Model 组件：处理 3D 文件的下载、解析和渲染
const Model: React.FC<{
	modalUrl: string;
	fileName: string;
	arrayBuffer?: ArrayBuffer;
	metalness?: number;
	roughness?: number;
	color?: string;
	isHovered: boolean;
}> = ({ modalUrl, fileName, arrayBuffer, metalness = 0.4, roughness = 0.3, color = "#cccccc", isHovered }) => {
	const [object, setObject] = useState<THREE.Object3D | null>(null);
	const [zoomed, setZoomed] = useState(false);
	const { camera, controls } = useThree();
	const groupRef = useRef<THREE.Group>(null);
	const isLoadedRef = useRef(false);

	const loadModel = async () => {
		try {
			let buffer = arrayBuffer;
			if (!buffer) {
				const response = await fetch(modalUrl);
				buffer = await response.arrayBuffer();
			}

			const loaderType = chooseLoader(fileName);
			let loadedObject;

			if (loaderType === GLTFLoader) {
				// 创建并设置 DracoLoader
				const dracoLoader = new DRACOLoader();
				dracoLoader.setDecoderPath('/draco/');  // 设置解码器路径（可以使用CDN或本地路径）

				const loader = new GLTFLoader();
				loader.setDRACOLoader(dracoLoader); // 将 Draco 解码器传递给 GLTFLoader

				loadedObject = await new Promise((resolve, reject) => {
					loader.parse(buffer, "", (object: any) => resolve(object.scene || object), reject);
				});
			} else if (loaderType === "stp") {
				// 处理 stp 格式的加载
				loadedObject = await LoadStep(modalUrl, buffer);
			} else {
				const loader = new loaderType();
				if (loader instanceof Rhino3dmLoader) {
					loader.setLibraryPath("/rhino3dm/");
					loadedObject = await new Promise((resolve, reject) => {
						try {
							// 使用parse方法处理buffer数据
							const onLoad = (object: THREE.Object3D) => {
								resolve(object);
							};
							loader.parse(buffer as ArrayBuffer, onLoad);
						} catch (error) {
							console.error('Error during 3dm parsing:', error);
							reject(error);
						}
					});
				} else if (loader instanceof STLLoader) {
					const material = loader.parse(buffer as ArrayBuffer);
					loadedObject = new THREE.Mesh(material, new THREE.MeshStandardMaterial({
						color,
						metalness,
						roughness,
					}));
				} else {
					loadedObject = await new Promise((resolve, reject) => {
						loader.parse(buffer, "", (object: any) => resolve(object.scene || object), reject);
					});
				}
			}

			// 创建一个组来包含模型
			const group = new THREE.Group();
			group.add(loadedObject);

			// 计算中心点并调整位置
			const box = new THREE.Box3().setFromObject(loadedObject);
			const center = box.getCenter(new THREE.Vector3());
			loadedObject.position.sub(center);

			setObject(group);
			isLoadedRef.current = true;
			zoomExtents(group, camera, controls, zoomed);
			setZoomed(true);
		} catch (error) {
			console.error("Error loading model:", error);
		}
	};

	useEffect(() => {
		loadModel();
	}, [modalUrl, arrayBuffer]);

	return object ? (
		<group ref={groupRef}>
			<primitive object={object} dispose={null} />
		</group>
	) : null;
};

// 主要组件：加载和渲染 3D 文件
const LoadNormalModal: React.FC<{
	modalUrl: string;
	className?: string;
	fileName: string;
	arrayBuffer?: ArrayBuffer;
	metalness?: number;
	roughness?: number;
	color?: string;
	backgroundColor?: string;
}> = ({ modalUrl, className, fileName, arrayBuffer, metalness, roughness, color, backgroundColor }) => {
	console.log(backgroundColor, 'backgroundColor');
	
	const [isHovered, setIsHovered] = useState(false);
	const [isModelLoaded, setIsModelLoaded] = useState(false);

	return (
		<Canvas
			shadows
			className={`${className || ""}`}
			onCreated={({ gl }) => {
				if (typeof window !== "undefined") {
					gl.setPixelRatio(Math.min(window.devicePixelRatio, 2.0));
					gl.shadowMap.enabled = true;
					gl.shadowMap.type = THREE.PCFSoftShadowMap;
					gl.toneMapping = THREE.ACESFilmicToneMapping;
					gl.toneMappingExposure = 1.0; // 增加曝光度以显示更多细节
				}
			}}
			onWheel={(e) => e.stopPropagation()}
			onMouseEnter={() => isModelLoaded && setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			{/* 主环境光 */}
			<ambientLight intensity={0.7} />

			{/* 主环境光 */}
			<ambientLight intensity={0.4} />

			{/* 主平行光源 - 正面 */}
			<directionalLight
				position={[0, 2, 5]}
				intensity={0.8}
				castShadow
				shadow-mapSize-width={2048}
				shadow-mapSize-height={2048}
			/>

			{/* 补充平行光源 - 背面 */}
			<directionalLight
				position={[0, 2, -5]}
				intensity={0.6}
			/>

			{/* 顶部光源 */}
			<directionalLight
				position={[0, 5, 0]}
				intensity={0.5}
			/>

			{/* 左侧光源 */}
			<directionalLight
				position={[-5, 2, 0]}
				intensity={0.4}
			/>

			{/* 右侧光源 */}
			<directionalLight
				position={[5, 2, 0]}
				intensity={0.4}
			/>

			{/* 底部填充光 */}
			<directionalLight
				position={[0, -3, 0]}
				intensity={0.2}
			/>


			{/* 环境贴图 */}
			<Environment
					preset={null}
					background={false}
					path="/3DModals/"
					files={["venice_sunset_1k.hdr"]}
				/>

			<Suspense fallback={<Html center> <Loader /></Html>}>
			
				<Bounds fit clip observe={false} margin={0.25} maxDuration={1}>
					<Model
						modalUrl={modalUrl}
						fileName={fileName}
						arrayBuffer={arrayBuffer}
						metalness={metalness}
						roughness={roughness}
						color={color}
						isHovered={isHovered}
					/>
				</Bounds>
			</Suspense>
			<color attach="background" args={[backgroundColor || '#ffffff']} />
			<OrbitControls
				makeDefault
				maxPolarAngle={Math.PI}
				enableZoom={true}
				minDistance={0.8}
				maxDistance={1.2}
				enableDamping={true}
				dampingFactor={0.05}
				rotateSpeed={0.6} // 降低旋转速度以便更好地观察细节
			/>
		</Canvas>
	);
};

export default LoadNormalModal;
