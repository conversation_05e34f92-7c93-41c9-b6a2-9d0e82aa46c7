"use client";
import React from "react";
import { Button } from "@/components/Button";
import { SubmitHandler, useForm } from "react-hook-form";
import { RingLoader } from "react-spinners";
import { getCookie, setCookie } from "cookies-next";
import axios from "axios";
import { useTranslations } from "next-intl";
import { message } from 'antd';
import { UserLoginInfo, useUserStore } from "@/store/user.store";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
import {useRouter  } from "@/navigation";

type FormValues = {
  email?: string;
  password?: string;
  remember?: boolean;
  onSubmit: (data?: React.BaseSyntheticEvent<object, any, any> | undefined) => Promise<void>;
  setLoginModalOn?: any;
};

interface LoginFormProps {
  setLoginModalOn?: any;
}

const LoginForm = ({ setLoginModalOn }: LoginFormProps) => {
  const t = useTranslations();
  const { setUserLoginInfo } = useUserStore();
  let get_form_info: any = getCookie("created__user__info");
  if (get_form_info) {
    get_form_info = JSON.parse(get_form_info);
  }
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<FormValues>();

  const [loading, setLoading] = React.useState(false);
  const { useLogin } = useUserAuth();
	const router = useRouter();
  const onSubmit: SubmitHandler<FormValues> = async (data:any) => {
    setLoading(true);

      try {
        setLoading(true);
        await useLogin(data);
        setLoginModalOn(false);
        // router.replace("/");
      } catch (e: any) {
        message.error(e?.message || "error");
      } finally {
        setLoading(false);
      }
  };

  return (
    <div>
      <div className="mt-7 user">
        <form action="" onSubmit={handleSubmit(onSubmit)}>
          <input
            type="text"
            id="email"
            className={`px-5 py-3 rounded-sm outline-none border bg-white text-xl  placeholder:text-lg w-full !text-black ${errors.email ? "border-red-500" : "border-themeSecondary300"
            }`}
            defaultValue={get_form_info?.email}
            placeholder={t("form.109a2790cc6b48461908f883b7b41549f9b3")}
            {...register("email", { required: true })}
          />
          <input
            type="password"
            id="password"
            className={`px-5 py-3 rounded-sm outline-none border bg-white text-xl  placeholder:text-lg w-full mt-6 !text-black ${errors.password ? "border-red-500" : "border-themeSecondary300"
            }`}
            placeholder={t("common.Password")}
            {...register("password", { required: true })}
          />

          <Button
            className={`flex gap-4 items-center justify-center w-full mt-6 !rounded-sm !bg-black ${loading ? "bg-themeSecondary800" : ""}`}
          >
            {loading ? <RingLoader color="#fff" size={30} /> : ""}
            {loading ? t("message.7f9e518a7a1d1e4bc3e8990bed1d9be4d404") + "..." : t("common.Sign_in")}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;
