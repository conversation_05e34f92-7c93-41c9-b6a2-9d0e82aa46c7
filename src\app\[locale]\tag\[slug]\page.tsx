
// app/blog/page.tsx
import React from 'react';
import { getBlogCls, getBlogList, getBlogTags } from "@/lib/api/blog";
import Breadcrumb from '@/components/Breadcrumb/Breadcrumb';
import RecentPosts from '@/components/Blogs/RecentPosts';
import Categories from '@/components/Blogs/Categories';
import TagsCloud from '@/components/Blogs/Tags';
import { type MyPageProps } from '@/lib/@types/base';
import BlogSearch from "@/components/Blogs/BlogSeach";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import Blogs from "@/components/Blogs/Blogs";
import type { Blog } from "@/lib/@types/api/blog";
import BlogList from "@/components/Blogs/BlogList";
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";
import BlogsServer from '@/components/Blogs/BlogsServer';
import { HomeTaile } from '@/components/Contact/ConcatPage';
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["blog"]
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};
const fetchBlogList = async (Params: Blog.GetBlogListParams): Promise<{ blogList: Blog.BlogListItem[], blogCount: number }> => {

	try {
		const res = await getBlogList(Params);

		return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
	} catch (error) {

		console.error("Error fetching blog list:", error);
		return { blogList: [], blogCount: 0 }; // 如果发生错误，返回一个空数组
	}
}

// 使用async函数获取数据并渲染
export default async function Blog({ params, searchParams }: MyPageProps) {
	const currentPage = parseInt(searchParams?.page as string) || 1;
	const searchTerm = searchParams?.search as string || "";
	const limit = 6;

	unstable_setRequestLocale(params.locale);
	const t = await getTranslations();

	try {
		// 并行获取所有数据
		const [clsList, tagList, blogData] = await Promise.all([
			getBlogCls({ lang_code: { lang_code: params.locale } }),
			getBlogTags({ lang_code: { lang_code: params.locale } }),
			getBlogList({
				lang_code: { lang_code: params.locale },
				page: currentPage,
				limit,
				tag_slug_in: [{ slug: params.slug }],
				blog_title: searchTerm
			})
		]);

		return (
			<>
				<HomeTaile msg={t('blog.Tag')} />
				<section className="blog list py-10 md:py-20">
					<div className="container">
						<div className="flex justify-between gap-y-12 max-xl:flex-col">
							<div className="right xl:w-1/4 xl:pr-[52px]">
								<BlogSearch></BlogSearch>
								<Categories clsList={clsList} />
								<TagsCloud tagList={tagList} />
								{
									<RecentPosts blogDetail={blogData.detail} />
								}
							</div>
							<BlogsServer
								blogList={blogData.detail.blog_list}
								totalCount={blogData.detail.blog_filter_count}
								currentPage={currentPage}
								limit={limit}
								searchTerm={searchTerm}
								locale={params.locale}
								basePath={`/tag/${params.slug}`}
							/>
						</div>
					</div>
				</section>
			</>
		);
	} catch (error) {
		console.log('Error fetching blog data:', error);
		return (
			<>
				<HomeTaile msg={t('blog.Tag')} />
				<section className="blog list py-10 md:py-20">
					<div className="container">
						<div className="text-center py-12">
							<p className="text-gray-500 text-lg">Loading tag data failed, please try again later</p>
						</div>
					</div>
				</section>
			</>
		);
	}
}



