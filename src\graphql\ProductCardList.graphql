query ProductCardList(
	$first: Int
	$channel: String!
	$locale: LanguageCodeEnum!
	$filter: ProductFilterInput
) {
	products(first: $first, channel: $channel, filter: $filter, sortBy: { field: DATE, direction: DESC }) {
		edges {
			node {
				...ProductCardItem
			}
		}
	}
}
fragment ProductCardItem on Product {
	name
	translation(languageCode: $locale) {
		name
	}
	metadata {
		key
		value
	}
	id
	slug
	rating
	pricing {
		priceRange {
			start {
				gross {
					amount
					currency
				}
			}
			stop {
				gross {
					amount
					currency
				}
			}
		}
	}
	category {
		name
		translation(languageCode: $locale) {
			name
		}
	}
	variants {
		...ProductVariantItem
	}
}
