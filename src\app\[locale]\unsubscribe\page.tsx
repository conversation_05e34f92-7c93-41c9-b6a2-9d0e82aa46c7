
import React from 'react';
import { Link } from '@/navigation';
import { getChannelLanguageMap } from '@/lib/api/channel';
import { defaultLocale } from '@/config';
import { type MyPageProps } from '@/lib/@types/base';
import { executeGraphQL } from '@/lib/graphql';
import {  ProductListPaginatedDocument } from '@/gql/graphql';
import { handleGraphqlLocale } from '@/lib/utils/util';
import SimilarProductsCard from '@/components/Product/SimilarProductsCard';
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";
import { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo.ts";
import { generateSeo } from "@/lib/utils/seo.ts";

export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {

  props.params.page=["unsubscribe"]
  // 获取基础页面的SEO数据
  const seo = await getBasePageSeo(props);
  // 生成最终的SEO信息，并返回
  return generateSeo(props, {
    ...seo,
    ogType: "website",
  });
};

const UnsubscribeSuccessPage =async ({ params }: MyPageProps) => {
  unstable_setRequestLocale(params.locale);
  const t=await getTranslations();
  const locale = params.locale;
  const channel = (await getChannelLanguageMap())[defaultLocale];
  const productData = await executeGraphQL(ProductListPaginatedDocument, {
    withAuth: false,
    variables: {
        first: 8,
        after:'',
        locale: handleGraphqlLocale(locale || defaultLocale),
        channel: channel,
    },
    revalidate: 60,
});

  return (
    <div className="flex flex-col items-center justify-center mt-20 max-md:mt-10  text-gray-800">
      <h2 className="text-4xl font-bold mb-5 text-green-300">{t("unsubscribe.successTitle")}</h2>
      <p className="text-lg mb-8">{t("unsubscribe.successMessage")}</p>
      <Link href="/products">
        <span className="text-blue-500 hover:underline mb-10">{t("unsubscribe.viewProducts")}</span>
      </Link>
      <div className="w-full container mt-4 mb-36 max-md:mb-20">
        <SimilarProductsCard data={productData?.products?.edges} />
      </div>
    </div>
  );
};

export default UnsubscribeSuccessPage;
