import { useTranslations } from "next-intl";
import React from "react";

function Index({ cOrderId, email }: { cOrderId: number | string; email: string }) {
	const t = useTranslations();
	return (
		<div className="my-10 grid grid-cols-1 gap-y-3 rounded-md bg-white p-4 font-bold">
			{cOrderId && (
				<p>
					{t("checkout.67d229c330a0b9415c8810d8b5918bd976a7")}:{" "}
					<span className="font-medium">#{cOrderId}</span>
				</p>
			)}
			<p>
				{t("checkout.10bf4b1d8021244ebb08bbb544aa6731c2f4")}: <span className="font-medium">{email}</span>
			</p>
		</div>
	);
}

export default React.memo(Index);
