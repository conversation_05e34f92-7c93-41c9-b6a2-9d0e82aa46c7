"use client";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import Marquee from "react-fast-marquee";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";
import { useRouter } from "@/navigation";
import Blog from "../Resource/Blog";
import News from "./News";
function Index({ blogList }: { blogList: Blog.BlogListItem[] }) {
	let zzarr = [
		{ img: "/image/img/zz/01.png" },
		{ img: "/image/img/zz/02.png" },
		{ img: "/image/img/zz/03.png" },
		{ img: "/image/img/zz/04.png" },
		{ img: "/image/img/zz/05.png" },
		{ img: "/image/img/zz/06.png" },
		{ img: "/image/img/zz/01.png" },
		{ img: "/image/img/zz/02.png" },
		{ img: "/image/img/zz/03.png" },
		{ img: "/image/img/zz/04.png" },
		{ img: "/image/img/zz/05.png" },
		{ img: "/image/img/zz/06.png" },
	];

	const t = useTranslations('nav');


	return (
		<div>
			<Pickleball />
			<div className="box-border w-full bg-[#243256] py-[30px] text-white">
				<Marquee speed={80} gradient={false} pauseOnHover={true}>
					<div className="flex ">
						{new Array(10).fill(1).map((itm, index) => (
							<div key={index} className="mx-9 flex cursor-default items-center gap-10 text-[24px]">
								<GetSvg /> <span>{t('Free express')}</span>
							</div>
						))}
					</div>
				</Marquee>
			</div>
			<ProductShowcase />

			<Appraise />
			{/* 博客 */}
			<News blogList={blogList} />

			<div className="box-border w-full py-[36px]">
				<Marquee speed={80} gradient={false} pauseOnHover={true}>
					<div style={{ display: "flex", gap: "10px" }}>
						{zzarr.map((item, index) => (
							<img key={index} src={item.img} alt={process.env.NEXT_PUBLIC_COMPANY_NAME} className="h-[90px]" />
						))}
					</div>
				</Marquee>
			</div>
		</div>
	);
}

export default React.memo(Index);

function GetSvg() {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="#fff">
			<path
				d="M19.6528 10.3641C17.4705 10.3569 15.1796 9.91078 13.4555 8.48806C12.0063 7.29202 11.1454 5.59441 10.7282 3.78587C10.4678 2.65734 10.3738 1.50229 10.381 0.347239C10.381 0.106101 10.1881 0 10 0C9.81191 0 9.619 0.106101 9.619 0.347239C9.62865 2.31734 9.35134 4.35496 8.4326 6.12491C7.55727 7.81046 6.11526 9.08609 4.32843 9.73475C3.05281 10.1977 1.69761 10.3617 0.347239 10.3665C0.106101 10.3665 0 10.5619 0 10.7499C0 10.938 0.106101 11.1333 0.347239 11.1333C2.52954 11.1406 4.82035 11.5867 6.54449 13.0094C7.99373 14.2054 8.85459 15.9031 9.27176 17.7116C9.53219 18.8401 9.62382 19.9952 9.619 21.1502C9.619 21.3914 9.81191 21.4975 10 21.4975C10.1881 21.4975 10.381 21.3914 10.381 21.1502C10.3714 19.1801 10.6487 17.1425 11.5674 15.3726C12.4427 13.687 13.8847 12.4114 15.6716 11.7627C16.9472 11.2997 18.3024 11.1358 19.6528 11.1309C19.8939 11.1309 20 10.9356 20 10.7475C20 10.5594 19.8939 10.3641 19.6528 10.3641ZM13.019 12.4331C11.4492 13.6942 10.4919 15.5462 10.0386 17.4825C10.0241 17.5404 10.0145 17.6007 10 17.6585C9.79503 16.7278 9.48638 15.8235 9.04268 14.9771C8.08295 13.1444 6.49144 11.7892 4.5551 11.0779C4.18857 10.9428 3.81239 10.8343 3.4314 10.7451C4.71184 10.4389 5.92718 9.90596 6.97854 9.06197C8.54835 7.80082 9.50567 5.94888 9.95901 4.01254C9.97348 3.95467 9.98312 3.89438 9.99759 3.83651C10.2026 4.7673 10.5112 5.67157 10.9549 6.51796C11.9146 8.35061 13.5061 9.70581 15.4425 10.4172C15.809 10.5522 16.1852 10.6607 16.5662 10.7499C15.2857 11.0586 14.0704 11.5891 13.019 12.4331Z"
				fill="white"
			></path>
		</svg>
	);
}

function Appraise() {
	const t = useTranslations("nav");

	let arr = [
		{
			rating: 5,
			title: t('Great'),
			review: t('Reasonably'),
			reviewer: {
				name: t("Robert Smith"),
				location: t("Customer from USA"),
			},
			product: {
				image: "/image/img/product-3.webp",
				name: t('Franklin'),
				originalPrice: "USD139.99",
				discountedPrice: "USD100.00",
			},
		},
		{
			rating: 5,
			title: t('Lightweight'),
			review: t('The paddle'),
			reviewer: {
				name: t('Hellen Ase'),
				location: t('Customer'),
			},
			product: {
				image: "/image/img/product-4.webp",
				name: t('Selkirk'),
				originalPrice: "USD274.99",
				discountedPrice: "USD249.99",
			},
		},
		{
			rating: 5,
			title: t("Built to Last"),
			review: t('Durable'),
			reviewer: {
				name: t('Peter Rope'),
				location: t("Customer from USA"),
			},
			product: {
				image: "/image/img/product-5.webp",
				name: t('Selkirk Vanguard'),
				originalPrice: "USD274.99",
				discountedPrice: "USD249.99",
			},
		},
		{
			rating: 5,
			title: t('Great'),
			review: t('Reasonably'),
			reviewer: {
				name: t("Robert Smith"),
				location: t("Customer from USA"),
			},
			product: {
				image: "/image/img/product-3.webp",
				name: t('Franklin'),
				originalPrice: "USD139.99",
				discountedPrice: "USD100.00",
			},
		},
	];
	const swiperBaseConfig = {
		slidesPerView: 3,
		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			768: {
				slidesPerView: 2,
			},
			1024: {
				slidesPerView: 3,
			},
		},
	};
	return (
		<div className="bg-[#f6f6f6]">
			<div className="container mx-auto  box-border px-10 py-20 max-md:px-2 max-md:py-8 ">
				<h2 className="mb-16 text-center text-4xl">Customer Reviews</h2>

				<Swiper
					className="home-banner"
					modules={[Autoplay]}
					loop={true}
					{...swiperBaseConfig}
					spaceBetween={50}
					onSlideChange={() => console.log("slide change")}
					onSwiper={(swiper) => console.log(swiper)}
					autoplay={{
						delay: 5000,
						disableOnInteraction: true,
					}}
					pagination={{
						clickable: true,
						bulletActiveClass: "swiper-pagination-bullet-active !bg-white",
						bulletClass: "swiper-pagination-bullet home-banner-bullet",
					}}
				>
					{arr.map((item, index) => (
						<SwiperSlide key={index}>
							<div
								key={index}
								className="list box-border flex-1 rounded-lg bg-white p-10 px-[36px] shadow-sm"
							>
								<div className="star-rating mb-4 flex gap-x-2">
									<i className="ri-star-fill text-lg text-[#ff7b54]"></i>
									<i className="ri-star-fill text-lg text-[#ff7b54]"></i>
									<i className="ri-star-fill text-lg text-[#ff7b54]"></i>
									<i className="ri-star-fill text-lg text-[#ff7b54]"></i>
									<i className="ri-star-fill text-lg text-[#ff7b54]"></i>
								</div>
								<h2 className="mb-4 text-xl font-medium text-black">{item.title}</h2>
								<p className="mb-6 line-clamp-2  text-[18px]  text-black">{item.review}</p>
								<div className="mb-8 border-b-[1px] border-[#ebebeb] pb-8">
									<p className="font-medium text-black">{item.reviewer.name}</p>
									<p className="text-[#545454]">{item.reviewer.location}</p>
								</div>
								<div className="box-border flex items-center justify-between px-2">
									<div className="flex items-center gap-4">
										<img
											alt={item.product.name}
											className="h-12 w-12 object-contain"
											src={item.product.image}
										/>
										<div>
											<p className="mb-3 line-clamp-1 text-sm">{item.product.name}</p>
											<div className="flex items-center gap-2">
												<span className="text-gray-400 line-through">{item.product.originalPrice}</span>
												<span className="text-red-500">{item.product.discountedPrice}</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</SwiperSlide>
					))}
				</Swiper>
			</div>
		</div>
	);
}

function Pickleball() {
	const t = useTranslations("nav");
	let router = useRouter()

	return (
		<div className="w-full py-[30px] max-md:py-8">
			<div className="containernone grid grid-cols-2    overflow-hidden rounded-lg max-lg:grid-cols-1 max-lg:rounded-none">
				<div className="flex w-full  flex-col justify-center bg-[#e8f5ed] p-16 max-md:p-7">
					<h2 className="mb-6 text-5xl font-medium max-md:text-2xl">
						{t("Pickleball")}
						<br />
						{t("Superstore")}
					</h2>
					<p className="mb-8 max-w-md text-lg leading-relaxed text-gray-700">{t("paddles")}</p>
					<button onClick={() => router.push(`/products`)} className="w-fit rounded-full border-2 border-black bg-transparent px-8 py-3 text-black transition duration-300 hover:bg-black hover:border-[#e8f5ed] hover:text-white myhover">
						{t("Shop Collection")}
					</button>
				</div>

				<div className="w-full">
					<img
						alt={t("paddles")}
						className="h-full w-full object-cover"
						height="600"
						src="/image/img/image_246.webp"
						width="800"
					/>
				</div>
			</div>
		</div>
	);
}

interface ProductCard {
	title: string;
	description: string;
	image: string;
	bgColor: string;
}
function ProductShowcase() {
	const t = useTranslations("nav");

	const products: ProductCard[] = [
		{
			title: t("SLK Series"),
			description: t("The lineup"),
			image: "/image/img/banner1.webp",
			bgColor: "bg-gradient-to-br from-purple-900 to-purple-700",
		},
		{
			title: t("Motion Pro"),
			description: t("Expertly"),
			image: "/image/img/banner2.webp",
			bgColor: "bg-gradient-to-br from-gray-800 to-green-900",
		},
	];


	let router = useRouter()

	const swiperBaseConfig = {
		slidesPerView: 2,
		spaceBetween: 50,
		breakpoints: {
			320: {
				slidesPerView: 1.5,
				spaceBetween: 10,
			},
			768: {
				slidesPerView: 2,
				spaceBetween: 50,
			},
			1024: {
				slidesPerView: 2,
				spaceBetween: 50,
			},
		},
	};
	return (
		<motion.div
			initial="hidden"
			whileInView="visible"
			viewport={{ once: true, amount: 0.5 }}
			variants={containerVariants} className="box-border w-full px-6 py-[65px] max-md:py-[18px]">
			<div className=" w-full ">
				<Swiper
					className="home-banner"
					modules={[Autoplay]}
					loop={true}
					{...swiperBaseConfig}
					onSlideChange={() => console.log("slide change")}
					onSwiper={(swiper) => console.log(swiper)}
					autoplay={{
						delay: 5000,
						disableOnInteraction: true,
					}}
					pagination={{
						clickable: true,
						bulletActiveClass: "swiper-pagination-bullet-active !bg-white",
						bulletClass: "swiper-pagination-bullet home-banner-bullet",
					}}
				>
					{products.map((product, index) => (
						<SwiperSlide key={index} className={` group relative  w-full overflow-hidden rounded-lg`}>
							<motion.div
								variants={itemVariants} className="">
								<div className="w-full  overflow-hidden">
									<img
										src={product.image}
										alt={product.description}
										className=" mx-auto w-full  rounded-lg object-cover duration-1000  ease-in-out  group-hover:scale-105  max-md:h-[300px] max-md:w-[258px]"
									/>
								</div>

								<div className="absolute bottom-0 left-0 box-border flex h-full w-full transform flex-col justify-end p-16 max-lg:p-6">
									<div>
										<h2 className="text-[60px] text-white max-lg:text-lg">{product.title}</h2>
										<p className="mb-6 text-[14px] text-gray-200">{product.description}</p>
										<button onClick={() => router.push(`/products`)} className="rounded-full bg-white px-6 py-2 text-[14px] text-black transition-colors hover:bg-black hover:text-white myhover">
											{t("Shop now")}
										</button>
									</div>
								</div>
							</motion.div>

						</SwiperSlide>
					))}
				</Swiper>
			</div>
		</motion.div>
	);
}
