import { type BaseMicroservicesResponse } from "@/lib/@types/api/base";
import { type MediaType } from "@/lib/@types/api/media";
import { message } from "antd";

const baseUrl = process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL;
// const baseUrl = "http://43.132.233.53:28000";

// 获取电子书列表
export const getEbookList = async (data: any) => {
    try {
        const res: any = await fetch(`${baseUrl}/medias/get_ebook_list`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "lang": "en",
                "Content-Type": "application/json"
            },
        })

        return res.status === 200 ? res.json() : [];
    } catch (error) {
        message.error("Fail to fetch data");
        return [];
    }
};

// 获取电子书详情
export const getEbookDetail = async (data: any) => {
    return new Promise((resolve, reject) => {
        fetch(`${baseUrl}/medias/ebook_detail`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json"
            },
        }).then(async (r: any) => {
            const res = await r.json();
            if (res.code === 200) {
                resolve(res);
            } else {
                reject(res);
            }
        }).catch((err) => {
            reject(null);
        });
    })
};

// 下载电子书
export const downloadEbook = async (data: any) => {
    return new Promise((resolve, reject) => {
        fetch(`${baseUrl}/medias/get_ebook`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json"
            }
        }).then(async (r: any) => {
            if (r.ok) {
                r.headers.forEach(element => {
                    console.log(element)
                });
                const blob = await r.blob();
                // 创建一个指向Blob对象的URL
                const urlObject = URL.createObjectURL(blob);
                // 创建一个<a>元素并模拟点击以下载文件
                const a = document.createElement('a');
                a.href = urlObject;
                a.download = "电子书.docx";
                document.body.appendChild(a);
                a.click();
                resolve(r);
            } else {
                reject(r?.msg)
            }
        }).catch((err) => {
            debugger
            reject(err);
        });
    })
};

// 图片url批量查询
export const getBulkImage = async (data: any) => {
    try {
        const res: any = await fetch(`${baseUrl}/medias/get_bulk_image`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "lang": "en",
                "Content-Type": "application/json"
            },
        })

        return res.status === 200 ? res.json() : [];
    } catch (error) {
        message.error("Fail to fetch data");
        return [];
    }
};