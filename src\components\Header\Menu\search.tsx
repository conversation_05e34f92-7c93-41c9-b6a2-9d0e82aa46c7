"use client";
import { useTranslations } from "next-intl";
import { useState, useRef, useEffect } from "react";
import { useRouter } from "@/navigation";
import { motion, AnimatePresence } from "framer-motion";
import clsx from "clsx";

const CustomCursor = () => {
	const [position, setPosition] = useState<{ x: number; y: number } | null>(null);

	useEffect(() => {
		const updatePosition = (e: MouseEvent) => {
			setPosition({ x: e.clientX, y: e.clientY });
		};

		document.addEventListener("mousemove", updatePosition);
		return () => document.removeEventListener("mousemove", updatePosition);
	}, []);

	if (!position) return null;

	return (
		<motion.div
			initial={{ scale: 0.8, opacity: 0 }}
			animate={{ scale: 1, opacity: 1 }}
			className="pointer-events-none fixed z-[9999] flex h-12 w-12 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-white/90 backdrop-blur-sm"
			style={{
				top: position.y,
				left: position.x,
			}}
		>
			<i className="ri-close-line text-xl text-black" />
		</motion.div>
	);
};

type Props = {
	className?: string;
	isVisible?: boolean;
};

export default function Search({ className, isVisible }: Props) {
	const t = useTranslations();
	const router = useRouter();
	const [isOpen, setIsOpen] = useState(false);
	const [inputValue, setInputValue] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const inputRef = useRef<HTMLInputElement>(null);
	const [showCustomCursor, setShowCustomCursor] = useState(false);

	const handleSearch = async () => {
		if (!inputValue.trim()) return;

		try {
			setIsLoading(true);
			await router.push(`/search?query=${encodeURIComponent(inputValue.trim())}`);
			setIsOpen(false);
			setInputValue("");
		} finally {
			setIsLoading(false);
		}
	};

	const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setInputValue(event.target.value);
	};

	const handleKeyDown = (event: React.KeyboardEvent) => {
		if (event.key === "Enter") {
			handleSearch();
		} else if (event.key === "Escape") {
			setIsOpen(false);
			setInputValue("");
		}
	};

	const clearInput = () => {
		setInputValue("");
		inputRef.current?.focus();
	};

	return (
		<>
			<motion.button
				onClick={() => setIsOpen(true)}
				className={`group flex h-10 w-10 items-center justify-center   ${className}`}
				aria-label="Open search"
			>
				<i className={clsx('ri-search-line ri-xl  transition-all duration-300 text-[#282828] hover:text-black')} />
			</motion.button>

			<AnimatePresence>
				{isOpen && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
						onClick={() => setIsOpen(false)}
						onMouseEnter={() => {
							setShowCustomCursor(true);
						}}
						style={{ cursor: "none" }}
					>
						<motion.div
							className="w-full max-w-3xl px-6"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: 20 }}
							transition={{ delay: 0.1 }}
							onClick={(e) => e.stopPropagation()}
							onMouseEnter={() => {
								setShowCustomCursor(false);
							}}
							onMouseLeave={() => {
								setShowCustomCursor(true);
							}}
							style={{ cursor: "default" }}
						>
							<div className="relative flex flex-col items-center">
								<div className="relative w-full">
									<div className="pointer-events-none absolute left-0 flex h-full items-center pl-4">
										{isLoading ? (
											<i className="ri-loader-4-line animate-spin text-xl text-white/80" />
										) : (
											<i className="ri-search-line text-xl text-white/80" />
										)}
									</div>
									<input
										ref={inputRef}
										value={inputValue}
										onChange={handleInputChange}
										onKeyDown={handleKeyDown}
										type="text"
										placeholder={t('nav.Type to search')}
										className="h-16 w-full rounded-full bg-white/10 pl-12 pr-24 text-lg text-white placeholder:text-white/80 focus:outline-none focus:ring-2 focus:ring-main"
										autoFocus
									/>
									<div className="absolute right-0 top-0 flex h-16 items-center space-x-2 px-4">
										<AnimatePresence>
											{inputValue && (
												<motion.button
													initial={{ opacity: 0, scale: 0.8 }}
													animate={{ opacity: 1, scale: 1 }}
													exit={{ opacity: 0, scale: 0.8 }}
													onClick={clearInput}
													className="flex items-center justify-center text-white/50 transition-colors hover:text-white"
													title={"clear"}
												>
													<i className="ri-close-line text-2xl" />
												</motion.button>
											)}
										</AnimatePresence>
										<motion.button
											onClick={handleSearch}
											className="flex h-10 w-10 items-center justify-center rounded-full bg-white/10 text-white transition-colors hover:bg-white/20"
											whileTap={{ scale: 0.95 }}
											title={"search"}
										>
											<i className="ri-search-line text-xl" />
										</motion.button>
									</div>
								</div>

								<motion.div
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									transition={{ delay: 0.2 }}
									className="mt-4 text-center text-sm text-white/40"
								>
									{t('nav.Press')}
								</motion.div>
							</div>
						</motion.div>
						{showCustomCursor && <CustomCursor />}
					</motion.div>
				)}
			</AnimatePresence>
		</>
	);
}
