"use client"
import React, { useEffect, useRef } from 'react';
import { Select } from 'antd';
import { useTranslations,useLocale } from 'next-intl';
import ViewSwitcher from './ViewSwitcher';
import { SlidersHorizontal } from "@phosphor-icons/react";
import { useProductSortStore } from '@/lib/store/productSort.store';
import { usePathname } from 'next/navigation';
import { defaultLocale } from '@/config';

interface FilterBarProps {
  showFilter: boolean;
  showDrawer: () => void;
  col: number;
  changeBreakpointColumnsObj: (col: number) => void;
  isMobile: boolean;
}

const FilterBar: React.FC<FilterBarProps> = ({ 
  showFilter, 
  showDrawer, 
  col, 
  changeBreakpointColumnsObj,
  isMobile
}) => {
  const t = useTranslations();
  const pathname = usePathname(); // 获取当前路径
  const { sortValue, setSortValue, resetSortState } = useProductSortStore();
  const locale = useLocale();
  // 使用ref保存上一次的路径，用于检测路由变化
  const prevPathRef = useRef(pathname);
  
  // 监听路由变化，重置排序状态
  useEffect(() => {
    if (pathname !== prevPathRef.current) {
      // 路径变化了，重置排序状态
      resetSortState();
      prevPathRef.current = pathname;
    }
  }, [pathname, resetSortState]);

  const handleSortChange = (value: string) => {
    setSortValue(value);
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-10 border-t-[1px] border-b-[1px] border-gray-300 py-4 gap-4 sm:gap-0">
      {/* 筛选区 */}
      {showFilter && (
      <div className="flex items-center">
          <div onClick={showDrawer} className="flex cursor-pointer items-center gap-x-2">
            <SlidersHorizontal size={24} />
            <span className={`${locale === defaultLocale ? "ib" : "font-semibold"}`}>{t('common.Filter')}</span>
          </div>
          </div>
        )}

      {/* 排序和视图切换区 */}
      <div className={`flex flex-wrap items-center ${showFilter ? 'justify-between sm:justify-end' : 'w-full justify-between'} gap-4`}>
        <div className="flex items-center">
          <span className={`text-sm whitespace-nowrap mr-2 ${locale === defaultLocale ? "ib" : "font-semibold"}`}>{t('common.Sort by')}:</span>
          <Select
            value={sortValue}
            style={{ width: isMobile ? 190: 180 }}
            onChange={handleSortChange}
            options={[
              { value: 'default', label: t('common.Default') },
              { value: 'best-selling', label: t('common.BestSelling') },
              { value: 'featured', label: t('common.Featured') },
              { value: 'price-asc', label: t('common.Pricelowtohigh') },
              { value: 'price-desc', label: t('common.Pricehightolow') },
              { value: 'name-asc', label: t('common.AlphabeticallyA-Z') },
              { value: 'name-desc', label: t('common.AlphabeticallyZ-A') },
            ]}
            popupClassName="custom-select-dropdown"
            listHeight={180}
            popupMatchSelectWidth={true}
            className="custom-select"
          />
        </div>

        <ViewSwitcher 
          col={col}
          changeBreakpointColumnsObj={changeBreakpointColumnsObj}
        />
      </div>
    </div>
  );
};

export default FilterBar; 