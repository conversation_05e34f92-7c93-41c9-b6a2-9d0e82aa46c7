'use client'

import React from 'react';
import ReactPaginate from 'react-paginate';

interface Props {
    pageCount: number
    pageChange: (selected: number) => void;
}

const HandlePagination: React.FC<Props> = ({ pageCount, pageChange }) => {
    return (
        <ReactPaginate
            previousLabel="<"
            nextLabel=">"
            pageCount={pageCount}
            pageRangeDisplayed={3}
            marginPagesDisplayed={2}
            onPageChange={(selectedItem) => pageChange(selectedItem.selected)}
            containerClassName={'pagination'}
            activeClassName={'active'}
        />
    );
};

export default HandlePagination;
