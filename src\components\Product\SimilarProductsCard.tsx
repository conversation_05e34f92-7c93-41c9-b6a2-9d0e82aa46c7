"use client";
import React, { useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import ProductImageBox from "./ProductImageBox";

const SimilarProductsCard = ({ data }: any) => {
	const [swiperExample, setSwiperExample] = useState<any>();
	const handlePrevOrNext = (type: "prev" | "next") => {
		if (type === "prev") {
			swiperExample?.slidePrev();
		} else {
			swiperExample?.slideNext();
		}
	};

	return (
		<div className="relative">
			<div
				onClick={() => {
					handlePrevOrNext("prev");
				}}
				className="product-button-prev absolute left-0 top-1/2 z-10 flex size-10 -translate-y-1/2 cursor-pointer items-center justify-center rounded-full bg-white shadow hover:opacity-60 "
			>
				<CaretLeft size={25} weight="bold" />
			</div>
			<div
				onClick={() => {
					handlePrevOrNext("next");
				}}
				className="product-button-next absolute right-0 top-1/2 z-10 flex size-10 -translate-y-1/2 cursor-pointer items-center justify-center rounded-full bg-white shadow hover:opacity-60"
			>
				<CaretRight size={25} weight="bold" />
			</div>

			<Swiper
				onSwiper={setSwiperExample}
				spaceBetween={15}
				slidesPerView={1}
				pagination={{ clickable: true }}
				navigation={{
					nextEl: ".product-button-next",
					prevEl: ".product-button-prev",
				}}
				breakpoints={{
					640: {
						slidesPerView: 2,
					},
					768: {
						slidesPerView: 3,
					},
					1024: {
						slidesPerView: 4,
					},
					1600: {
						slidesPerView: 5,
					},
				}}
				className="animate__animated animate__fadeIn !pb-14"
			>
				{data.map((item, index) => {
					return (
						<SwiperSlide key={index} className="!round-sm px-2">
							<ProductImageBox product={item} />
						</SwiperSlide>
					);
				})}
			</Swiper>
		</div>
	);
};

export default SimilarProductsCard;
