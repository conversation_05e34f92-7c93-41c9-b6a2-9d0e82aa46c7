"use client";

import clsx from 'clsx';
import React from 'react'

export default function Svg({isScrolled,ishead=false}:{isScrolled:boolean,ishead?:boolean}) {

  return (
    <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="16" 
    height="16" 
    viewBox="0 0 18 18"
  >
    <path
      d="M7.41375 5.81625L1.58625 0L0 1.58625L5.81625 7.4025L7.41375 5.81625ZM11.8125 0L14.1075 2.295L0 16.4137L1.58625 18L15.705 3.8925L18 6.1875V0H11.8125ZM12.1838 10.5863L10.5975 12.1725L14.1187 15.6938L11.8125 18H18V11.8125L15.705 14.1075L12.1838 10.5863Z"
      fill={isScrolled ? "#000" : "#fff"}
      className={clsx("transition-colors duration-200",ishead&&'group-hover:fill-[#d53a3d]')}
    />
  </svg>
  )
}



