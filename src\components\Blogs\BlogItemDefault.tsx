"use client";
import React from "react";
import Image from "next/image";
import * as Icon from "@phosphor-icons/react/dist/ssr";
import { Avatar, Tag } from "antd";
import moment from "moment/moment";
import { type Blog } from "@/lib/@types/api/blog";
import { Link } from "@/navigation";
import { CalendarOutlined, UserOutlined, TagsOutlined, AlignLeftOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

interface BlogProps {
	news: Blog.BlogListItem;
}

const BlogItemDefault: React.FC<BlogProps> = ({ news }) => {
	const t = useTranslations()
	return (
		<>
			<Link
				href={"/blog/" + news.blog_slug}
				className="block transform cursor-pointer overflow-hidden border border-gray-200 rounded-lg bg-white transition-all duration-300 hover:shadow-xl hover:shadow-black/10 hover:-translate-y-1 group"
			>
				<div className="relative overflow-hidden">
					{news.blog_cover_type ? (
						<SEOOptimizedImage 
							src={news.blog_cover_origin || "/image/default-image.webp"} 
							width={500} 
							height={350} 
							alt={news.blog_title} 
							className="h-[350px] w-full object-cover transition-transform duration-500 group-hover:scale-110"
						/>
					) : (
						<video
							autoPlay
							muted
							loop
							playsInline
							className="h-[350px] w-full object-cover transition-transform duration-500 group-hover:scale-110"
							src={news.blog_cover_origin}
							width="600"
						/>
					)}
					{/* 优化的玻璃划过效果 */}
					<div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
				</div>
				<div className="p-6 md:p-8">
					<div className="text-[#666] flex flex-wrap gap-x-4 gap-y-2 border-b border-gray-200 pb-4 mb-4">
						<div className="text-sm flex items-center">
							<UserOutlined className="mr-2 text-black" />
							<span className="text-[#666]">{process.env.NEXT_PUBLIC_COMPANY_NAME}</span>
						</div>
						<div className="text-sm flex items-center">
							<CalendarOutlined className="mr-2 text-black" />
							<span className="text-[#666]">{moment(news.blog_upload_time).format("MMM DD, YYYY")}</span>
						</div>
						{
							news.blog_classification_list && news.blog_classification_list.length > 0 && (
								<div className="text-sm flex items-center flex-wrap">
									<AlignLeftOutlined className="mr-2 text-black" />
									<span className="inline">
										{news.blog_classification_list.map((item, index) => (
											<React.Fragment key={item.cls_id}>
												<Link 
													href={`/category/${item.cls_slug}`} 
													className="text-[#666] hover:text-black transition-colors duration-300 inline-block"
												>
													{item.cls_name}
												</Link>
												{index < news.blog_classification_list.length - 1 && <span>,&nbsp;</span>}
											</React.Fragment>
										))}
									</span>
								</div>
							)
						}
						{
							news.blog_tag_list && news.blog_tag_list.length > 0 && (
								<div className="text-sm flex items-center flex-wrap">
									<TagsOutlined className="mr-2 text-black" />
									<span className="inline">
										{news.blog_tag_list.map((item, index) => (
											<React.Fragment key={item.tag_slug}>
												<Link
													href={`/tag/${item.tag_slug}`}
													className="text-[#666] hover:text-black transition-colors duration-300 inline-block"
												>
													{item.tag_name}
												</Link>
												{index < news.blog_tag_list.length - 1 && <span>,&nbsp;</span>}
											</React.Fragment>
										))}
									</span>
								</div>
							)
						}
					</div>
					<h3 className="mb-4 line-clamp-1 text-xl font-bold text-black group-hover:text-black transition-all duration-300 leading-tight">
						{news.blog_title}
					</h3>
					<p className="mb-6 line-clamp-2 text-base font-normal text-[#666] irs leading-relaxed">
						{news.blog_excerpt}
					</p>
					<div className="flex justify-center">
						<span className="inline-block px-6 py-3 text-sm font-medium text-white bg-black hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 uppercase tracking-wide">
							{t("blog.ReadMore")}
						</span>
					</div>
				</div>
			</Link>
		</>
	);
};

export default BlogItemDefault;
