  mutation AccountAddressUpdate(
    $id: ID!,
    $city: String,
    $companyName: String,
    $country: CountryCode,
    $countryArea: String,
    $firstName: String,
    $lastName: String,
    $phone: String,
    $streetAddress1: String,
    $postalCode: String
  ) {
    accountAddressUpdate(
      id:$id,
      input: {
        city: $city,
        companyName: $companyName,
        country: $country,
        countryArea: $countryArea,
        firstName: $firstName,
        lastName: $lastName,
        phone: $phone,
        streetAddress1: $streetAddress1,
        postalCode: $postalCode
      }
    ) {
      errors {
        field
        code
        message
      }
      address {
        id
        firstName
        lastName
        companyName
        streetAddress1
        city
        countryArea
        postalCode
        country {
          code
          country
        }
        phone
        isDefaultShippingAddress
      }
    }
  }