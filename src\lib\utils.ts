import * as THREE from "three";
import occtimportjs from "occt-import-js";
import { nullLiteral } from "@babel/types";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export const formatDate = (date: Date | number) => {
	return new Intl.DateTimeFormat("en-US", { dateStyle: "medium" }).format(date);
};

export const formatMoney = (amount: number, currency: string) =>
	new Intl.NumberFormat("en-US", {
		style: "currency",
		currency,
	}).format(amount);

export const formatMoneyRange = (
	range: {
		start?: { amount: number; currency: string } | null;
		stop?: { amount: number; currency: string } | null;
	} | null,
) => {
	const { start, stop } = range || {};
	const startMoney = start && formatMoney(start.amount, start.currency);
	const stopMoney = stop && formatMoney(stop.amount, stop.currency);

	if (startMoney === stopMoney) {
		return startMoney;
	}

	return `${startMoney} - ${stopMoney}`;
};

export function getHrefForVariant({
	productSlug,
	variantId,
}: {
	productSlug: string;
	variantId?: string;
}): string {
	const pathname = `/products/${encodeURIComponent(productSlug)}`;

	if (!variantId) {
		return pathname;
	}

	const query = new URLSearchParams({ variant: variantId });
	return `${pathname}?${query.toString()}`;
}

type dscObj = {
	data: { text: string };
	type: string;
};
type dscList = {
	blocks: dscObj[];
};
export const filterDsc = (dsc: string) => {
	const dscObj = JSON.parse(dsc) as dscList;
	let des = "";
	if (dscObj && dscObj.blocks.length > 0) {
		des = dscObj.blocks[0].data.text;
	}

	return des;
};

export async function LoadStep(fileUrl: string, buffer?: ArrayBuffer) {
	const wasmUrl = "/3DModals/occt-import-js.wasm";
	const targetObject = new THREE.Object3D();
	const occt = await occtimportjs({
		locateFile: (name: string) => {
			// return occtimportWasm
			return wasmUrl;
		},
	});

	if (!buffer) {
		let response = await fetch(fileUrl);
		buffer = await response.arrayBuffer();
	}
	// read the imported step file
	let fileBuffer = new Uint8Array(buffer);
	let result = occt.ReadStepFile(fileBuffer, nullLiteral());
	// process the geometries of the result
	for (let resultMesh of result.meshes) {
		let geometry = new THREE.BufferGeometry();

		geometry.setAttribute(
			"position",
			new THREE.Float32BufferAttribute(resultMesh.attributes.position.array, 3),
		);
		if (resultMesh.attributes.normal) {
			geometry.setAttribute(
				"normal",
				new THREE.Float32BufferAttribute(resultMesh.attributes.normal.array, 3),
			);
		}
		const index = Uint16Array.from(resultMesh.index.array);
		geometry.setIndex(new THREE.BufferAttribute(index, 1));

		let material = null;
		if (resultMesh.color) {
			const color = new THREE.Color(resultMesh.color[0], resultMesh.color[1], resultMesh.color[2]);
			material = new THREE.MeshPhongMaterial({ color: color });
		} else {
			material = new THREE.MeshPhongMaterial({ color: 0xaaaaaa });
		}

		const mesh = new THREE.Mesh(geometry, material);
		targetObject.add(mesh);
	}
	return targetObject;
}

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export const replaceCdnDomain = (url: string) => {
	return url;
	// const cdn = "https://nalide.cdn.pinshop.com";
	// if (!cdn) {
	//   return url;
	// }
	// if (url?.startsWith(process.env.NEXT_PUBLIC_WORDPRESS_URL as string)) {
	//   return url.replace(process.env.NEXT_PUBLIC_WORDPRESS_URL as string, cdn);
	// } else {
	//   return url;
	// }
};

// 提取youtube视频链接
export const getYoutubeLink = (url: string) => {
	const reg = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#\&\?]*).*/;
	const match = url.match(reg);
	return match && match[7].length === 11 ? match[7] : "";
};


// 设置html的overflow
export function setOverflow(overflow: string) {
	document.documentElement.style.overflow = overflow;
  }


  export function convertPriceToNumber(priceString: string): number {
    if (!priceString) return 0;
    if (isNaN(+priceString[0])) {
      priceString = priceString.replace(/[^0-9.]/g, "");
    }
  
    return parseFloat(priceString);
  }
  
  export function formatPrice(price: string) {
    const newPrice = convertPriceToNumber(price);
    return newPrice.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  // 防抖函数
export function debounce(fn: Function, delay: number) {
  let timer: any = null;
  return (...args: any[]) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      // @ts-ignore
      fn.apply(this, args);
    }, delay);
  };
}