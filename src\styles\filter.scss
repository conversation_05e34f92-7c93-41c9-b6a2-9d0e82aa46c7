// FilterDrawer 样式
.filter-drawer {
    .ant-drawer-header {
      padding: 20px 24px;
      padding-bottom: 0 !important;
      border-bottom: 0 !important;
      position: relative;
    }
    
    .ant-drawer-title {
      font-size: 18px;
      font-weight: 500;
    }
    
    .ant-drawer-close {
      position: absolute;
      right: 20px;
      top: 20px;
      margin: 0;
      padding: 0;
    }
    
    .ant-drawer-body {
      padding-top: 0 !important;
    }
    
    .ant-collapse-header {
      padding: 20px 0 !important;
    }
    
    .ant-collapse-item {
      border-bottom: 1px solid #e8e8e8 !important;
    //   padding-bottom: 12px;
    //   margin-bottom: 8px;
    }
    
    .ant-collapse-item:last-child {
      margin-bottom: 0;
    }
    
    .ant-collapse-content-box {
      padding: 0 0 10px 0 !important;
    }
    
    .ant-checkbox-wrapper {
      margin-left: 0;
      margin-bottom: 12px;
      font-size: 15px;
    }
    
    .ant-checkbox-wrapper:last-child {
      margin-bottom: 0;
    }

    // 基本样式
    .ant-checkbox-inner {
      border-radius: 0;
      border: 1px solid #000;
      width: 18px;
      height: 18px;
      background-color: #fff !important;
    }
    
    // 选中状态
    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #fff !important;
      border-color: #000 !important;
    }
    
    // 选中标记
    .ant-checkbox-checked .ant-checkbox-inner::after {
      border: none !important;
      transform: none !important;
      opacity: 1 !important;
      content: "" !important;
      position: absolute !important;
      width: 10px !important;
      height: 10px !important;
      background-color: #000 !important;
      left: 3px !important;
      top: 3px !important;
    }
    
    // 悬停状态
    .ant-checkbox-wrapper:hover .ant-checkbox-inner,
    .ant-checkbox:hover .ant-checkbox-inner,
    .ant-checkbox-input:focus + .ant-checkbox-inner {
      border-color: #000 !important;
      background-color: #fff !important;
    }
    
    // 选中+悬停状态
    .ant-checkbox-checked:hover .ant-checkbox-inner {
      background-color: #fff !important;
      border-color: #000 !important;
    }
    
    // 选中+悬停状态的标记
    .ant-checkbox-checked:hover .ant-checkbox-inner::after {
      background-color: #000 !important;
      border: none !important;
      transform: none !important;
      width: 10px !important;
      height: 10px !important;
      left: 3px !important;
      top: 3px !important;
    }

    // 禁用状态下的样式
    .ant-checkbox-disabled .ant-checkbox-inner {
      background-color: #f5f5f5 !important;
      border-color: #d9d9d9 !important;
    }
    
    .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
      background-color: #d9d9d9 !important;
    }

    // 大尺寸复选框样式
    .checkbox-large {
      font-size: 16px;
      
      .ant-checkbox-inner {
        width: 22px !important;
        height: 22px !important;
      }
      
      .ant-checkbox-checked .ant-checkbox-inner::after {
        width: 14px !important;
        height: 14px !important;
        left: 4px !important;
        top: 4px !important;
      }
    }

    // // 复选框组间距
    // .checkbox-group-large {
    //   .ant-checkbox-wrapper {
    //     margin-bottom: 16px;
    //   }
    // }

    // 价格滑块自定义样式
    .price-range-slider {
      .ant-slider-rail {
        background-color: black !important;
        height: 3px !important;
        // border-radius: 3px !important;
      }

      .ant-slider-track {
        background-color: black  !important;
        height: 3px !important;
        // border-radius: 3px !important;
      }
      .ant-slider-handle::after {
         background-color: #c3e92d !important;
         left: 50% !important;
         top: 50% !important;
         width: 12px !important;
         height: 12px !important;
         transform: translate(-50%, -50%) !important;
        }
      .ant-slider-handle {
        background-color: black !important;
        // border: 3px solid #000 !important;
        width: 24px !important;
        height: 24px !important;
        margin-top: -12px !important;
        box-shadow: none !important;
        border-radius: 50% !important;
        opacity: 1 !important;

        // &:hover {
        //   background-color: #9ACD32 !important;
        //   border-color: #000 !important;
        //   box-shadow: none !important;
        // }

        // &:focus {
        //   background-color: #9ACD32 !important;
        //   border-color: #000 !important;
        //   box-shadow: none !important;
        // }

        // &:active {
        //   background-color: #9ACD32 !important;
        //   border-color: #000 !important;
        //   box-shadow: none !important;
        // }
      }

      // .ant-slider-handle-dragging {
      //   background-color: #9ACD32 !important;
      //   border-color: #000 !important;
      //   box-shadow: none !important;
      // }
    }
  }
  
  // FilterBar 样式
  .custom-select-dropdown {
    padding: 0;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    
    .ant-select-item {
      padding: 12px 16px;
      font-size: 15px;
      transition: all 0.2s;
    }
    
    .ant-select-item-option-selected {
      background-color: #333 !important;
      color: #fff !important;
      font-weight: 500;
    }
    
    .ant-select-item-option-active:not(.ant-select-item-option-selected) {
      background-color: #f5f5f5;
    }
  }
  
  .ant-select-selector {
    border-radius: 50px !important;
    height: 30px !important;
    padding: 0 15px !important;
    border: 1px solid #e8e8e8 !important;
    display: flex;
    align-items: center;
  }
  
  .ant-select-selection-item {
    font-weight: 500;
    padding-right: 15px;
  }
  
  .ant-select-focused .ant-select-selector {
    border-color: #333 !important;
    box-shadow: 0 0 0 2px rgba(0,0,0,0.05) !important;
  }
  
  .ant-select:hover .ant-select-selector {
    border-color: #333 !important;
  }
  
  .ant-select-arrow {
    color: #333;
    right: 15px;
  } 