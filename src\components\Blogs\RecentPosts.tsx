'use client'
import React from 'react';
import Image from 'next/image';
import { Tag } from "antd";
import { Link } from "@/navigation";
import { type Blog } from '@/lib/@types/api/blog';
import { useTranslations } from "next-intl";
import moment from "moment/moment";
import { CalendarOutlined } from "@ant-design/icons";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
interface BlogDetailData {
	blog_filter_count: number;
	blog_list: Blog.BlogListItem[];
}

interface RecentPostsProps {
	blogDetail: BlogDetailData;
}

const RecentPosts: React.FC<RecentPostsProps> = ({ blogDetail }) => {
	const t = useTranslations()
	console.log(blogDetail, 'blogDetail');

	// 直接使用传入的blog列表
	const blogList = blogDetail?.blog_list || [];

	return (
		<div className="recent md:mt-10 mt-6 pb-8 border-b border-line">
			<div className="heading6">{t('blog.RecentPosts')}</div>
			<div className="list-recent pt-1">
				{blogList?.map((item) => (
					<Link href={`/blog/${item.blog_slug}`} className="item group flex gap-4 mt-5 cursor-pointer text-gray-900 items-center" key={item.blog_slug}>
						<div className="w-24 h-24 overflow-hidden flex-shrink-0">
							<SEOOptimizedImage
								src={item.blog_cover_origin}
								width={500}
								height={400}
								alt={item.blog_title}
								className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 ease-in-out"
							/>
						</div>

						<div className=''>
							<h6 className="text-[16px] my-1 line-clamp-2 text-[#121212] font-bold group-hover:text-main group-hover:-translate-y-0.5 transition-all duration-300">{item.blog_title}</h6>
							<p className='text-[#121212] text-[14px] line-clamp-1 group-hover:text-main transition-all group-hover:text-opacity-80 duration-300'>{item.blog_excerpt}</p>
						</div>
					</Link>
				))}
			</div>
		</div>
	);
};

export default RecentPosts;
