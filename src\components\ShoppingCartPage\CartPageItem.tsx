"use client";
import React, { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Link } from "@/navigation";
import Price from "@/components/Price/price";
// import Calculator from "@/components/Calculator"; // 不再使用，改为自定义计数器
import { getImagesForProduct } from "@/components/Product/imageGallery";
import { defaultLocale } from "@/config";
import Image from "next/image";
import { getColorImagePath } from "@/utils/categoryMapping";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import CustomCounter from "./CustomCounter";

interface CartPageItemProps {
	product: any;
	count: number;
	handelupdateLinesCount: (id: string) => void;
	handelupdateCount: (id: string, count: number) => Promise<void>;
}

function CartPageItem({
	product,
	count = 1,
	handelupdateLinesCount,
	handelupdateCount,
}: CartPageItemProps) {
	const [countValue, setCountValue] = useState(count);
	const [isLoading, setIsLoading] = useState(false);
	let locale = useLocale();
	const t = useTranslations("shop");
	
	// product
	let [Reaproduct] = useState(product.variant.product);
	// variant
	let [Reavariant] = useState(product.variant);
	const imagesList = getImagesForProduct(Reaproduct) as any[];

	const changeCount = async (val: number) => {
		try {
			setIsLoading(true);
			setCountValue(val);
			await handelupdateCount(product.id, val);
		} catch (error) {
			// 如果更新失败，恢复原来的数量
			setCountValue(count);
			console.error('Failed to update quantity:', error);
		} finally {
			setIsLoading(false);
		}
	};

	const handleRemove = () => {
		handelupdateLinesCount(product.id);
	};

	// 获取变体属性组合显示 - 参考 product-attributes.tsx 的逻辑
	const getVariantAttributes = () => {
		const attributes = Reavariant?.attributes || [];
		const excludedAttributes = ['cost', 'cost price', 'costprice'];

		// 调试产品分类信息
		// console.log('CartPageItem - Product category debug:', {
		// 	productCategory: Reaproduct?.category,
		// 	categorySlug: Reaproduct?.category?.slug,
		// 	fullProduct: Reaproduct
		// });

		return attributes
			.filter((attr: any) => {
				const attrName = attr.attribute?.name?.trim().toLowerCase();
				return attrName && !excludedAttributes.includes(attrName);
			})
			.map((attr: any) => {
				const attrName = attr.attribute?.name;
				// 参考 product-attributes.tsx 第150行的逻辑
				const attrValue = attr.values?.[0]?.name || attr.values?.[0]?.value;

				if (!attrValue) return null;

				// 参考 product-attributes.tsx 第155行的逻辑
				const normalizedValue = String(attrValue).trim();

				// 检查是否为颜色属性 - 参考 product-attributes.tsx 第516行
				const isColorAttribute = attrName?.toLowerCase().trim() === 'color';

				// 获取颜色对应的图片路径 - 参考 product-attributes.tsx 第519行
				const colorImagePath = isColorAttribute ? getColorImagePath(normalizedValue, product?.variant?.product?.category?.slug) : null;
				// console.log(product?.variant?.product?.category?.slug);
				
				console.log("colorimagePath",colorImagePath);
				
				return {
					name: attrName,
					value: normalizedValue,
					isColor: isColorAttribute,
					colorImagePath: colorImagePath
				};
			})
			.filter(Boolean); // 过滤掉 null 值
	};

	const variantAttributes = getVariantAttributes();

	return (
		<>
			{/* 桌面端表格布局 */}
			<div className="hidden xl:grid xl:grid-cols-12 gap-4 px-6 py-6 items-center">
				{/* 商品信息 - 6列 */}
				<div className="col-span-6 flex items-center space-x-4">
					{/* 商品图片 */}
					<div className="w-[180px] h-[180px] rounded border border-gray-200 flex-shrink-0 overflow-hidden">
						<Image
							src={imagesList[0]?.url}
							alt={Reaproduct.name}
							width={1000}
							height={1000}
							className="w-full h-full object-cover"
						/>
					</div>

					{/* 商品详情 */}
					<div className="flex-1 min-w-0 px-8">
						<Link
							href={`/product/${Reaproduct.slug}`}
							className="hover:text-main transition-colors "
						>
							<h3 className={`text-black line-clamp-2 mb-4 ${locale === defaultLocale ?"ib":" font-medium"} text-base`}>
								{locale === defaultLocale ? Reaproduct.name : Reaproduct.translation.name || Reaproduct.name}
							</h3>
						</Link>

						{/* 变体属性组合 - 参考 product-attributes.tsx 的渲染逻辑 */}
						{variantAttributes.map((attr: any, index: number) => (
							<div key={index} className="mt-2 text-sm text-black flex items-center">
								<span className={`${locale === defaultLocale ?"ib":" font-medium"}`}>{attr.name}:</span>
								{attr.isColor && attr.colorImagePath ? (
									// 参考 product-attributes.tsx 第543-555行的图片渲染逻辑
									<div className="ml-2 flex items-center">
										<div className="w-[74px] h-[30px] overflow-hidden">
											<SEOOptimizedImage
												src={attr.colorImagePath}
												alt={`RC ${attr.value} color`}
												width={1000}
												height={1000}
												className="w-full h-full object-cover"
												unoptimized
												quality={100}
											/>
										</div>
										{/* <span className="ml-1">{attr.value}</span> */}
									</div>
								) : (
									<span className="ml-1 text-[#a5a5a5]">{attr.value}</span>
								)}
							</div>
						))}
					</div>
				</div>

				{/* 价格 - 2列 */}
				<div className="col-span-2 text-center">
					<Price
						price={Reavariant?.pricing?.price?.gross.amount}
						className="text-black font-medium"
					/>
				</div>

				{/* 数量控制 - 2列 */}
				<div className="col-span-2 flex items-center justify-center relative">
					<CustomCounter
						count={countValue}
						onCountChange={changeCount}
						disabled={isLoading}
						maxCount={Reavariant.quantityAvailable}
					/>
					<button
						onClick={handleRemove}
						className="p-1 text-gray-400 right-3 absolute top-1/2 -translate-y-1/2 hover:text-red-500 transition-colors"
						title="Remove item"
					>
						<i className="ri-delete-bin-line text-lg"></i>
					</button>
				</div>

				{/* 总价 - 2列 */}
				<div className="col-span-2 text-center">
					{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
						<Price
							price={product.totalPrice.gross.amount}
							className="text-gray-900 font-medium"
						/>
					)}
				</div>
			</div>

			{/* 移动端卡片布局 */}
			<div className="xl:hidden p-4 space-y-4">
				<div className="flex items-start space-x-4">
					{/* 商品图片 */}
					<div className="w-[100px] h-[100px] flex-shrink-0 rounded-lg overflow-hidden bg-gray-100">
						<SEOOptimizedImage
							src={imagesList[0]?.url}
							alt={Reaproduct.name}
							width={1000}
							height={1000}
							className="w-full h-full object-cover"
						/>
					</div>

					{/* 商品信息 */}
					<div className="flex-1 min-w-0">
						<Link
							href={`/product/${Reaproduct.slug}`}
							className="hover:text-primary-600 transition-colors"
						>
							<h3 className={`text-black line-clamp-2 mb-4 ${locale === defaultLocale ?"ib":" font-medium"} text-base`}>
								{locale === defaultLocale ? Reaproduct.name : Reaproduct.translation.name || Reaproduct.name}
							</h3>
						</Link>

						{/* 变体属性组合 - 参考 product-attributes.tsx 的渲染逻辑 */}
						<div className="mt-1 text-sm text-black">
							{variantAttributes.map((attr: any, index: number) => (
								<div key={index} className="flex items-center text-sm mt-2">
									<span className={`${locale === defaultLocale ?"ib":" font-medium"}`}>{attr.name}:</span>
									{attr.isColor && attr.colorImagePath ? (
										// 参考 product-attributes.tsx 第543-555行的图片渲染逻辑
										<div className="ml-2 flex items-center">
											<div className="w-[50px] h-[20px] overflow-hidden">
												<SEOOptimizedImage
													src={attr.colorImagePath}
													alt={`RC ${attr.value} color`}
													width={16}
													height={16}
													className="w-full h-full object-cover"
													unoptimized
													quality={100}
													priority
												/>
											</div>
											{/* <span className="ml-1">{attr.value}</span> */}
										</div>
									) : (
										<span className="ml-1 text-[#a5a5a5]">{attr.value}</span>
									)}
								</div>
							))}
						</div>

						{/* <div className="mt-2">
							<Price
								price={Reavariant?.pricing?.price?.gross.amount}
								className="text-black font-medium"
							/>
						</div> */}
					</div>

					{/* 删除按钮 */}
					<button
						onClick={handleRemove}
						className="p-2 text-gray-400 hover:text-red-500 transition-colors"
						title="Remove item"
					>
						<i className="ri-delete-bin-line text-lg"></i>
					</button>
				</div>

				{/* 数量控制和总价 */}
				<div className="flex items-center justify-between">
					<CustomCounter
						count={countValue}
						onCountChange={changeCount}
						disabled={isLoading}
						maxCount={Reavariant.quantityAvailable}
					/>

					{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
						<div className="text-right">
							<Price
								price={product.totalPrice.gross.amount}
								className="text-black font-medium"
							/>
						</div>
					)}
				</div>
			</div>
		</>
	);
}

export default React.memo(CartPageItem);
