query ProductCategoriesList(	$channel: String! ,$locale: LanguageCodeEnum!, $first: Int) {
	categories(first: $first, level: 0) {
		edges {
			node {
				 products(channel:$channel ) {
          totalCount
        }
				...CategoryChildrenList
			}
		}
	}
}

fragment CategoryChildrenList on Category {
	...CategoryWithTranslation
	children(first: $first) {
    totalCount
		edges {
			node {
				...CategoryWithTranslation
        products {
                totalCount
          }
			}
		}
	}
}
