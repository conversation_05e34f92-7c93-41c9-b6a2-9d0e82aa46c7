"use client"
import React from 'react';
import { Link } from '@/navigation';
import { useTranslations } from 'next-intl';
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
import { defaultLocale } from '@/config';

interface CategoryCarouselProps {
  categories: any[];
  locale: string;
}

const CategoryCarousel: React.FC<CategoryCarouselProps> = ({ categories, locale }) => {
  const t = useTranslations();
  // console.log("categories=====", categories);
  return (
    <>
      <Swiper
        modules={[Navigation, Pagination]}
        spaceBetween={20}
        slidesPerView={2}
        // pagination={{
        //   clickable: true,
        // }}
        breakpoints={{
          640: { slidesPerView: 3, spaceBetween: 20 },
          768: { slidesPerView: 3, spaceBetween: 20 },
          1024: { slidesPerView: 4, spaceBetween: 20 },
          1280: { slidesPerView: 7, spaceBetween: 20 },
        }}
        className="!pb-10 categories-swiper"
      >
        {categories.map((category: any) => (
          <SwiperSlide key={category.node.id}>
            <Link
              href={`/products/${category.node.slug}`}
              className="group relative bg-[#f6f6f6] rounded-lg overflow-hidden transition-transform hover:scale-105 block h-full"
            >
              <div className="aspect-[1/1] relative p-4 group">
                <SEOOptimizedImage
                  width={500}
                  height={500}
                  quality={100}
                  src={`${JSON.parse(category.node.media)?.[0]?.url || "/image/default-image.webp"}`}
                  alt={locale === defaultLocale ? category.node.name : (category.node.translation?.name || category.node.name)}
                  className="w-full h-full object-contain"
                />
                {/* 玻璃滑过效果 - 只用于图片 */}
                {/* <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-[700ms] ease-out">
                  <div className="h-full w-1/3 bg-gradient-to-r from-transparent via-white/50 to-transparent transform skew-x-12 blur-sm"></div>
                </div> */}
              </div>
              <div className="absolute bottom-2 left-0 hover:translate-y-full right-0 h-[48%] rounded-md w-[95%] mx-auto bg-black bg-opacity-30 flex flex-col items-center justify-center text-white translate-y-full opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300 ease-out">
                <h3 className="text-base font-medium mb-0.5 px-2 text-center">
                  {locale === defaultLocale ? category.node.name : (category.node.translation?.name || category.node.name)}
                </h3>
                <span className="irs mb-2 text-sm">
                  {t("common.Total")}: {category.node.products?.totalCount || 0}
                </span>
                <Link href={`/products/${category.node.slug}`} className="text-xs inline-block lg:text-sm px-3 py-1 bg-white shadow-sm text-black hover:bg-black hover:text-white rounded-full hover:bg-opacity-90 transition-colors duration-200">
                  {t("common.View all")}
                </Link>
              </div>
            </Link>
          </SwiperSlide>
        ))}
      </Swiper>

      <style jsx global>{`
        .categories-swiper .swiper-pagination-bullet {
          opacity: 0.5;
          background-color: #ccc;
        }
        
        .categories-swiper .swiper-pagination-bullet-active {
          opacity: 1;
          background-color: #000;
        }
      `}</style>
    </>
  );
};

export default CategoryCarousel; 