import ShoppingCartPage from '@/components/ShoppingCartPage';
import { MyPageProps } from '@/lib/@types/base';
import { getBasePageSeo } from '@/lib/api/seo';
import { generateSeo } from '@/lib/utils/seo';
import { Metadata } from 'next';
import React from 'react'

/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["cart"]
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		title: "Shopping Cart",
		description: "View and manage your shopping cart items",
		ogType: "website",
	});
};

export default function CartPage() {
	return (
		<div>
			<ShoppingCartPage />
		</div>
	)
}
