import React from "react";
import { type MyPageProps } from "@/lib/@types/base";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { defaultLocale } from "@/config";
import { getChannelLanguageMap } from "@/lib/api/channel";
import ProductList from "@/components/ProductList/ProductList";
import { fetchProductData } from "@/lib/api/product";
import { executeGraphQL } from "@/lib/graphql";
import { ProductCategoriesListDocument } from "@/gql/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";
import { unstable_setRequestLocale } from "next-intl/server";
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["products"];
	const seo = await getBasePageSeo(props);
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

export default async function Products({ params }: MyPageProps) {
	const locale = params.locale;
	const channel = (await getChannelLanguageMap())[defaultLocale];
	const { products } = await fetchProductData({ locale, channel });
	unstable_setRequestLocale(locale);
	return <ProductList channel={channel} locale={locale} products={products} />;
}
