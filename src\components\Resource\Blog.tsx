"use client"
import React, { useEffect, useRef, useState } from 'react';

import type { Blog } from "@/lib/@types/api/blog";
import { getBlogList } from "@/lib/api/blog";
import BlogList from "@/components/Blogs/BlogList";
import { ArrowRightOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation.ts";

const Blog = ({ locale, search }: { locale: string, search: string }) => {
    const t = useTranslations();
    const [blogList, setBlogList] = useState<Blog.BlogListItem[]>([])
    const [pageSize, setPageSize] = useState(1)
    const limit = 12;

    const fetchBlogList = async (Params: Blog.GetBlogListParams): Promise<{ blogList: Blog.BlogListItem[], blogCount: number }> => {
        try {
            const res = await getBlogList(Params);
            return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
        } catch (error) {
            console.error("Error fetching blog list:", error);
            return { blogList: [], blogCount: 0 }; // 如果发生错误，返回一个空数组
        }
    }

    useEffect(() => {
        fetchBlogList({
            lang_code: { lang_code: locale },
            page: pageSize,
            limit,
            blog_title: search || ""
        }).then(res => {
            if (res) {
                setBlogList(res.blogList);
            }
        })
    }, []);

    return <div className="bg-white">
        <div className="container py-16 max-md:py-8">
            <div className="flex flex-col items-center justify-center">
                <div className="text-[3rem] max-md:text-2xl leading-normal text-[#212121] font-bold">{t('resource.Blog')}</div>
                <div className="mt-2 mb-8 w-[80%] text-base text-[#545454] text-center">
                    {t('resource.BlogDesc')}
                </div>
                <BlogList blogList={blogList.splice(0, 3)} />

                <Link href="/blog" className="mt-12 rounded-full bg-main hover:bg-[#2692f0] py-2 px-4 text-md group border text-white hover:!text-black">
                    {t('resource.See More Blog Here')}
                    <ArrowRightOutlined
                      className="transition-all transform duration-700 group-hover:translate-x-2 pl-2"
                    />
                </Link>
            </div>
        </div>
    </div>
}

export default Blog;
