"use client";
import React, { createContext, useEffect, useState } from "react";
import { getChannelLanguageMap } from "@/lib/api/channel";
// 创建一个 Context，用于在应用中传递语言映射信息
export const UserContext = createContext<{
	languageMapChannel: Record<string, string>;
}>({
	languageMapChannel: {
		en: "default-channel",
	},
});
// Channel 组件，用于获取并提供频道语言映射信息

export default React.memo(function Channel({ children }: { children: React.ReactNode }) {
	// 状态，用于存储频道语言映射
	const [languageMapChannel, setLanguageMapChannel] = useState<Record<string, string>>({});
	// 组件加载时，从 API 获取频道语言映射
	useEffect(() => {
		getChannelLanguageMap().then((r) => {
			setLanguageMapChannel(r);
		});
	}, []);
	// 返回一个提供语言映射信息的 Context.Provider 组件
	return (
		<UserContext.Provider
			value={{
				languageMapChannel,
			}}
		>
			{children}
		</UserContext.Provider>
	);
});
