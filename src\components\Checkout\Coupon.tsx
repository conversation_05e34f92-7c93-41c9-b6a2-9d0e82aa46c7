"use client";
import { useTranslations } from "next-intl";
import React, { useState } from "react";
import { Button } from "../Button";
import clsx from "clsx";
import { RingLoader } from "react-spinners";
import { ADDPromoCode, DELPromoCode } from "@/lib/api/Checkout";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { defaultLocale } from "@/config";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { App } from "antd";
import { PlusOutlined } from '@ant-design/icons';
import { Spin } from 'antd';

function Index({ Cartdata }) {
	const { cartList, isCart, BuyBowcheckoutId, BuyBowCartList } = useShoppingCartStore() as any;
	const { findCheckout, BuyBowfindCheckout } = useShoppingCart();
	const { message } = App.useApp();

	const [coupons, setCoupons] = useState([]);
	const [value, setValue] = useState("");
	const [applyLoading, setApplyLoading] = useState(false);
	const [removeLoading, setRemoveLoading] = useState<string[]>([]);
	const t = useTranslations();

	async function appCoupons() {
    setApplyLoading(true)
		let { checkoutAddPromoCode } = await ADDPromoCode(Cartdata.id, value);
		if (!checkoutAddPromoCode.errors.length) {
			const channel = (await getChannelLanguageMap())[defaultLocale];
			if (isCart) {
				// 1. 刷新购物车数据
				await findCheckout(channel);
			} else {
				await BuyBowfindCheckout(channel);
			}
      message.success(t('checkout.add23234'));
		} else {
			message.error(checkoutAddPromoCode.errors[0].message);
		}
    setApplyLoading(false)

	}

  async function del(checkoutId,promoCodeId){
  let {checkoutRemovePromoCode}=await  DELPromoCode(checkoutId,promoCodeId);
  if (!checkoutRemovePromoCode.errors.length) {
    const channel = (await getChannelLanguageMap())[defaultLocale];
    if (isCart) {
      // 1. 刷新购物车数据
      await findCheckout(channel);
    } else {
      await BuyBowfindCheckout(channel);
    }
    message.success(t('checkout.Deleted234'));
  } else {
    message.error(checkoutRemovePromoCode.errors[0].message);
  }
  }
	return (
		<div className="py-8 mt-8">
			<h2 className="text-lg font-medium text-gray-900">
				{t("order.Coupon")}
			</h2>
			<div className="relative">
				<div className="flex items-center bg-white  p-1.5  gap-x-3 transition-all duration-300">
					<input
						type="text"
						className="flex-1 h-11 bg-transparent text-gray-700 text-sm focus:outline-none px-4"
						placeholder={t("order.ab7a8ebe43699e4f03d83549ceeb3864e4de")}
						value={value}
						onChange={(e) => setValue(e.target.value)}
					/>
					<div
						onClick={() => appCoupons()}
						className={clsx(
							"min-w-[100px] h-10 py-3 rounded-sm text-sm font-medium transition-all duration-300",
							"flex items-center justify-center gap-2",
							value && !applyLoading 
								? "bg-gradient-to-r from-main to-main/90 text-white hover:shadow-md hover:from-main/90 hover:to-main cursor-pointer" 
								: "cursor-not-allowed"
						)}
					>
						{applyLoading ? (
							<Spin className="!text-white" size="small" />
						) : (
							<>
								<PlusOutlined className="text-base" />
								{t("order.apply").toUpperCase()}
							</>
						)}
					</div>
				</div>

				{Array.isArray(Cartdata.giftCards) && Cartdata.giftCards.length > 0 && (
					<div className="mt-6">
						<div className="space-y-3">
							{Cartdata.giftCards.map((item,index) => (
								<div 
									key={index} 
									className="group  animate__animated  animate__fadeInUp  flex items-center justify-between p-4 bg-white rounded-xl border border-gray-100 hover:border-main/30 transition-all duration-300"
								>
									<div className="flex items-center space-x-3">
										<div className="w-10 h-10 rounded-lg bg-gradient-to-br from-main/10 to-main/5 flex items-center justify-center">
											<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-main" fill="none" viewBox="0 0 24 24" stroke="currentColor">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
											</svg>
										</div>
										<span className="font-mono text-base text-gray-700 tracking-wide">
											{getcode(item.last4CodeChars)}
										</span>
									</div>
									<button
                  onClick={()=>del(Cartdata.id,item.id)}
                    type="button"
										className={clsx(
											"flex items-center text-xs font-medium text-gray-400 hover:text-red-500 transition-colors px-3 py-2 rounded-lg hover:bg-red-50 group-hover:bg-opacity-50",
											removeLoading.includes(item.coupon) && "opacity-50 pointer-events-none"
										)}
										disabled={removeLoading.includes(item.coupon)}
									>
										{removeLoading.includes(item.coupon) ? (
											<RingLoader color="#dba32a" size={12} loading={true} />
										) : (
											<>
												<svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
													<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
												</svg>
												{t("common.d229e36bfcfa8345f2da3663638e5ffcc57a").toLowerCase()}
											</>
										)}
									</button>
								</div>
							))}
						</div>
					</div>
				)}
			</div>
		</div>
	);
}

export default React.memo(Index);


function getcode(msg){
  return `****-****-${msg}`
}