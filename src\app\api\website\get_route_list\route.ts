import { NextResponse } from "next/server";
import { menus } from "@/lib/menu";
import { generateLink } from "@/lib/utils/util";


// Define a handler for GET requests
export async function GET() {
	try {
		let routes = [];
		menus.forEach((item) => {
			if (item.hasSlug) {
				routes.push({
					id: item.id,
					name: item.name,
					link: generateLink(item.link),
					flag: 0,
					isEditPage: item.show,
          route: item.link,
				});
			}

			if (item.children) {
				item.children.forEach((child) => {
					if (child.hasSlug) {
						routes.push({
							id: child.id,
							name: child.name,
							link: generateLink(child.link),
							flag: 0,
							isEditPage: child.show,
              route: child.link,
						});
					}
				});
			}
		});

		// Respond with the list of routes as JSON
    // @ts-ignore
		return NextResponse.json({ code: 200, detail: { ret: routes } });
	} catch (err) {
    // @ts-ignore
		return NextResponse.json({ code: 0, detail: { ret: [] } });
	}
}
