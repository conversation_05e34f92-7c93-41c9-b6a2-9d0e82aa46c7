"use client";
import React from "react";
import {
  FacebookIcon,
  FacebookShareButton,
  LinkedinIcon,
  LinkedinShareButton,
  TwitterIcon,
  TwitterShareButton,
  WhatsappIcon,
  WhatsappShareButton,
} from "react-share";
import { BodyText } from "@/components/BodyText";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import { usePathname } from "next/navigation";


export const SocialShare = () => {
  const pathname = usePathname();
  
  const fullUrl = `${process.env.NEXT_PUBLIC_SITE_URL}${pathname}`;
  const t = useTranslations();
  return (
    <div className="flex gap-7">
            <div  className="text-themeSecondary900 flex items-center">
        {t("product.Share On")}
      </div>
      <div className="flex gap-2">
        <FacebookShareButton url={fullUrl}>
          <FacebookIcon size={30} round />
        </FacebookShareButton>
        <TwitterShareButton url={fullUrl}>
          <TwitterIcon size={30} round />
        </TwitterShareButton>
        <LinkedinShareButton url={fullUrl}>
          <LinkedinIcon size={30} round />
        </LinkedinShareButton>
        <WhatsappShareButton url={fullUrl}>
          <WhatsappIcon size={30} round />
        </WhatsappShareButton>
        <Link href="https://www.youtube.com" target="_blank" rel="noopener noreferrer">
          <i className="ri-youtube-fill" style={{ fontSize: "30px", color: "red" }}></i>
        </Link>
        <Link href="https://www.instagram.com" target="_blank" rel="noopener noreferrer">
          <i className="ri-instagram-line" style={{ fontSize: '30px', color: 'red' }}></i>
        </Link>
      </div>
    </div>
  );
};
