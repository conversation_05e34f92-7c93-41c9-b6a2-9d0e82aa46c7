// 定义标注点的类型
export interface FeaturePoint {
    id: string;
    x: number; // 百分比位置 (0-100)
    y: number; // 百分比位置 (0-100)
    title: string;
    description: string;
}

// 产品特性数据配置
export interface ProductFeatureConfig {
    imageSrc: string;
    imageAlt: string;
    featurePoints: FeaturePoint[];
}

export const q1: ProductFeatureConfig = {
    imageSrc: "/image/mdt/QUASAR-Raw-Carbon-Non-Honeycomb-Core-Pickleball-Paddle-NeonTraIl-Meowzart.png",
    imageAlt: "RC QUASAR Raw Carbon Non Honeycomb Core Pickleball Paddle NeonTraIl Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 52,
            y: 10,
            title: "t1",
            description: "d1"
        },
        {
            id: "2",
            x: 54,
            y: 38,
            title: "t2",
            description: "d2"
        },
        {
            id: "3",
            x: 39,
            y: 93,
            title: "",
            description: "d3"
        },
    ]
};
export const q2: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Social Set.png",
    imageAlt: "RC Social Set",
    featurePoints: [
        {
            id: "1",
            x: 24,
            y: 44,
            title: "t3",
            description: ""
        },
        {
            id: "2",
            x: 42,
            y: 44,
            title: "t4",
            description: ""
        },
        {
            id: "3",
            x: 60,
            y: 44,
            title: "t5",
            description: ""
        },
        {
            id: "4",
            x: 80,
            y: 44,
            title: "t6",
            description: ""
        }
    ]
};
export const q3: ProductFeatureConfig = {
    imageSrc: "/image/mdt/NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Kiki.png",
    imageAlt: "RC NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Kiki",
    featurePoints: [
        {
            id: "1",
            x: 56,
            y: 18,
            title: "t7",
            description: "d4"
        },
        {
            id: "2",
            x: 50,
            y: 94,
            title: "",
            description: "d5"
        },
    ]
};

export const q4: ProductFeatureConfig = {
    imageSrc: "/image/mdt/NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Pop.png",
    imageAlt: "RC NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Pop",
    featurePoints: [
        {
            id: "1",
            x: 56,
            y: 18,
            title: "t8",
            description: "d6"
        },
        {
            id: "2",
            x: 50,
            y: 94,
            title: "",
            description: "d7"
        },
    ]
};
export const q5: ProductFeatureConfig = {
    imageSrc: "/image/mdt/QUASAR Titanium-carbon Nomex Pickleball Paddle-Flare.png",
    imageAlt: "RC QUASAR Titanium-carbon Nomex Pickleball Paddle-Flare",
    featurePoints: [
        {
            id: "1",
            x: 45,
            y: 12,
            title: "t9",
            description: "d8"
        },
        {
            id: "2",
            x: 53,
            y: 32,
            title: "t10",
            description: "d9"
        },
        {
            id: "3",
            x: 49,
            y: 93,
            title: "",
            description: "d10"
        },
    ]
};
export const q6: ProductFeatureConfig = {
    imageSrc: "/image/mdt/QUASAR-Raw-Carbon-Dynamic-Neck-Core-Pickleball-Paddle-CyberBeat-Meowzart.png",
    imageAlt: "RC QUASAR-Raw-Carbon-Dynamic-Neck-Core-Pickleball-Paddle-CyberBeat-Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 60,
            y: 10,
            title: "t11",
            description: "d11"
        },
        {
            id: "2",
            x: 50,
            y: 18,
            title: "t12",
            description: "d12"
        },
        {
            id: "3",
            x: 48,
            y: 60,
            title: "t13",
            description: "d13"
        },
        {
            id: "4",
            x: 38,
            y: 92,
            title: "",
            description: "d14"
        },
    ]
};
export const q7: ProductFeatureConfig = {
    imageSrc: "/image/mdt/QUASAR Kevlar Aramid Pickleball Paddle-Stride.png",
    imageAlt: "RC QUASAR Kevlar Aramid Pickleball Paddle-Stride",
    featurePoints: [
        {
            id: "1",
            x: 32,
            y: 33,
            title: "t14",
            description: "d15"
        },
        {
            id: "2",
            x: 43,
            y: 60,
            title: "t15",
            description: "d16"
        },
        {
            id: "3",
            x: 80,
            y: 18,
            title: "",
            description: "d17"
        },
    ]
};
export const q8: ProductFeatureConfig = {
    imageSrc: "/image/mdt/QUASAR-Raw-Carbon-Dynamic-Neck-Core-Pickleball-Paddle-DreamGlow-POP.png",
    imageAlt: "RC QUASAR-Raw-Carbon-Dynamic-Neck-Core-Pickleball-Paddle-DreamGlow-POP",
    featurePoints: [
        {
            id: "1",
            x: 60,
            y: 10,
            title: "t16",
            description: "d18"
        },
        {
            id: "2",
            x: 50,
            y: 18,
            title: "t17",
            description: "d19"
        },
        {
            id: "3",
            x: 48,
            y: 60,
            title: "t18",
            description: "d20"
        },
        {
            id: "4",
            x: 38,
            y: 92,
            title: "",
            description: "d21"
        },
    ]
};
export const q9: ProductFeatureConfig = {
    imageSrc: "/image/mdt/QUASAR 3D 18K Carbon Pickleball Paddle-Tess.png",
    imageAlt: "RC QUASAR 3D 18K Carbon Pickleball Paddle-Tess",
    featurePoints: [
        {
            id: "1",
            x: 53,
            y: 11,
            title: "t19",
            description: "d22"
        },
        {
            id: "2",
            x: 48,
            y: 18,
            title: "t20",
            description: "d23"
        },
        {
            id: "3",
            x: 53,
            y: 50,
            title: "t21",
            description: "d24"
        },
        {
            id: "4",
            x: 51,
            y: 94,
            title: "",
            description: "d25"
        },
    ]
};
export const q10: ProductFeatureConfig = {
    imageSrc: "/image/mdt/PULSE 18K Carbon Fiber Pickleball Paddle-Vivid.png",
    imageAlt: "RC PULSE 18K Carbon Fiber Pickleball Paddle-Vivid",
    featurePoints: [
        {
            id: "1",
            x: 43,
            y: 15,
            title: "t22",
            description: "d26"
        },
        {
            id: "2",
            x: 58,
            y: 28,
            title: "t23",
            description: "d27"
        },
        {
            id: "3",
            x: 50,
            y: 94,
            title: "",
            description: "d28"
        },
    ]
};
export const q11: ProductFeatureConfig = {
    imageSrc: "/image/mdt/NOVA Fiberglass Pickleball Paddle-Prisma.png",
    imageAlt: "RC NOVA Fiberglass Pickleball Paddle-Prisma",
    featurePoints: [
        {
            id: "1",
            x: 45,
            y: 35,
            title: "t24",
            description: "d29"
        },
        {
            id: "2",
            x: 54,
            y: 35,
            title: "t25",
            description: "d30"
        },
        {
            id: "3",
            x: 50,
            y: 94,
            title: "",
            description: "d31"
        },
    ]
};
export const q12: ProductFeatureConfig = {
    imageSrc: "/image/mdt/NOVA Composite Pickleball Paddle-Slam Genius.png",
    imageAlt: "RC NOVA Composite Pickleball Paddle-Slam Genius",
    featurePoints: [
        {
            id: "1",
            x: 45,
            y: 10,
            title: "t26",
            description: "d32"
        },
        {
            id: "2",
            x: 56,
            y: 35,
            title: "t27",
            description: "d33"
        },
        {
            id: "3",
            x: 50,
            y: 94,
            title: "",
            description: "d34"
        },
    ]
};
export const q13: ProductFeatureConfig = {
    imageSrc: "/image/mdt/AXIS Graphite Pickleball Paddle-Nano.png",
    imageAlt: "RC AXIS Graphite Pickleball Paddle-Nano",
    featurePoints: [
        {
            id: "1",
            x: 45,
            y: 14,
            title: "t28",
            description: "d35"
        },
        {
            id: "2",
            x: 54,
            y: 32,
            title: "t29",
            description: "d36"
        },
        {
            id: "3",
            x: 52,
            y: 65,
            title: "t30",
            description: "d37"
        },
        {
            id: "4",
            x: 50,
            y: 94,
            title: "",
            description: "d38"
        },
    ]
};
export const q14: ProductFeatureConfig = {
    imageSrc: "/image/mdt/NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Meowzart.png",
    imageAlt: "RC NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 56,
            y: 18,
            title: "t8",
            description: "d6"
        },
        {
            id: "2",
            x: 50,
            y: 94,
            title: "",
            description: "d7"
        },
    ]
};
export const q15: ProductFeatureConfig = {
    imageSrc: "/image/mdt/PULSE Raw Carbon Pickleball Paddle-Chaotic Flow.png",
    imageAlt: "RC PULSE Raw Carbon Pickleball Paddle-Chaotic Flow",
    featurePoints: [
        {
            id: "1",
            x: 58,
            y: 16,
            title: "t28",
            description: "d35"
        },
        {
            id: "2",
            x: 47,
            y: 23,
            title: "t29",
            description: "d36"
        },
        {
            id: "3",
            x: 46,
            y: 58,
            title: "t30",
            description: "d37"
        },
        {
            id: "4",
            x: 30,
            y: 85,
            title: "",
            description: "d38"
        },
    ]
};
export const q16: ProductFeatureConfig = {
    imageSrc: "/image/mdt/AXIS Composite Pickleball Paddle-Fade.png",
    imageAlt: "RC AXIS Composite Pickleball Paddle-Fade",
    featurePoints: [
        {
            id: "1",
            x: 55,
            y: 25,
            title: "t34",
            description: "d43"
        },
        {
            id: "2",
            x: 60,
            y: 60,
            title: "t35",
            description: "d44"
        },
        {
            id: "4",
            x: 22,
            y: 75,
            title: "",
            description: "d38"
        },
    ]
};
export const q17: ProductFeatureConfig = {
    imageSrc: "/image/mdt/AXIS-3K-Carbon-Fiber-Pickleball-Paddle-Flex.png",
    imageAlt: "RC AXIS-3K-Carbon-Fiber-Pickleball-Paddle-Flex",
    featurePoints: [
        {
            id: "1",
            x: 46,
            y: 16,
            title: "t36",
            description: "d45"
        },
        {
            id: "2",
            x: 56,
            y: 15,
            title: "t37",
            description: "d46"
        },
        {
            id: "3",
            x: 53,
            y: 59,
            title: "t38",
            description: "d47"
        },
        {
            id: "4",
            x: 50,
            y: 95,
            title: "",
            description: "d38"
        },
    ]
};
export const q18: ProductFeatureConfig = {
    imageSrc: "/image/mdt/NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Flying Meowzart.png",
    imageAlt: "RC NOVA Fiberglass Pickleball Paddle-Meowzart Go!-Flying Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 56,
            y: 18,
            title: "t39",
            description: "d49"
        },
        {
            id: "2",
            x: 50,
            y: 94,
            title: "",
            description: "d7"
        },
    ]
};
export const q19: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Balance-Tape-black.png",
    imageAlt: "RC Pickleball-Paddle-Balance-Tape-black",
    featurePoints: [
        // {
        //     id: "1",
        //     x: 40,
        //     y: 16,
        //     title: "t40",
        //     description: "d50"
        // },
        {
            id: "2",
            x: 62,
            y: 39,
            title: "t41",
            description: ""
        },
        // {
        //     id: "3",
        //     x: 50,
        //     y: 87,
        //     title: "t42",
        //     description: "d52"
        // },
        // {
        //     id: "4",
        //     x: 50,
        //     y: 95,
        //     title: "",
        //     description: "d38"
        // },
    ]
};
export const q20: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Balance-Tape-while.png",
    imageAlt: "RC Pickleball-Paddle-Balance-Tape-while",
    featurePoints: [
        // {
        //     id: "1",
        //     x: 48,
        //     y: 15,
        //     title: "t43",
        //     description: "d53"
        // },
        {
            id: "2",
            x: 62,
            y: 39,
            title: "t44",
            description: ""
        },
        // {
        //     id: "3",
        //     x: 50,
        //     y: 94,
        //     title: "",
        //     description: "d34"
        // },
    ]
};
export const q21: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Glow-Pickleball.png",
    imageAlt: "RC Glow-Pickleball",
    featurePoints: [
        {
            id: "1",
            x: 60,
            y: 25,
            title: "t45",
            description: ""
        },
        {
            id: "2",
            x: 40,
            y: 75,
            title: "t46",
            description: ""
        },
    ]
};
export const q22: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Outdoor-Pickleballs-Virgo.png",
    imageAlt: "RC Outdoor-Pickleballs-Virgo",
    featurePoints: [
        {
            id: "1",
            x: 63,
            y: 40,
            title: "t47",
            description: "d55"
        },
    ]
};
export const q23: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Outdoor-Pickleballs-Libra.png",
    imageAlt: "RC Outdoor-Pickleballs-Libra",
    featurePoints: [
        {
            id: "1",
            x: 63,
            y: 40,
            title: "t48",
            description: "d56"
        },
    ]
};
export const q24: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Outdoor-Pickleballs-Leo.png",
    imageAlt: "RC Outdoor-Pickleballs-Leo",
    featurePoints: [
        {
            id: "1",
            x: 63,
            y: 40,
            title: "t49",
            description: "d57"
        },
    ]
};
export const q25: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Low-Noise-Pickleball.png",
    imageAlt: "RC Low-Noise-Pickleball",
    featurePoints: [
        {
            id: "1",
            x: 63,
            y: 40,
            title: "t50",
            description: ""
        },
    ]
};
export const q26: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-G-S2.png",
    imageAlt: "RC RC-G-S2",
    featurePoints: [
        {
            id: "1",
            x: 35,
            y: 50,
            title: "t51",
            description: "d58"
        },
        {
            id: "2",
            x: 60,
            y: 20,
            title: "t52",
            description: "d59"
        },
        {
            id: "3",
            x: 60,
            y: 58,
            title: "t53",
            description: "d60"
        },
    ]
};
export const q27: ProductFeatureConfig = {
    imageSrc: "/image/mdt/3-in-1-Paddle-Bag.png",
    imageAlt: "RC 3-in-1-Paddle-Bag",
    featurePoints: [
        {
            id: "1",
            x: 33,
            y: 70,
            title: "t54",
            description: "d61"
        },
        {
            id: "2",
            x: 45,
            y: 45,
            title: "t55",
            description: "d62"
        },
        {
            id: "3",
            x: 64,
            y: 45,
            title: "t56",
            description: "d63"
        },
        {
            id: "4",
            x: 68,
            y: 62,
            title: "t57",
            description: "d64"
        },
    ]
};
export const q28: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Convertible-Pickleball-Bag.png",
    imageAlt: "RC Convertible-Pickleball-Bag",
    featurePoints: [
        {
            id: "1",
            x: 30,
            y: 60,
            title: "t58",
            description: "d65"
        },
        {
            id: "2",
            x: 44,
            y: 20,
            title: "t59",
            description: "d66"
        },
        {
            id: "3",
            x: 68,
            y: 45,
            title: "t60",
            description: "d67"
        },
    ]
};
export const q29: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Bracelet.png",
    imageAlt: "RC Pickleball-Bracelet",
    featurePoints: [
        {
            id: "1",
            x: 44,
            y: 23,
            title: "t61",
            description: "d68"
        },
        {
            id: "2",
            x: 52,
            y: 70,
            title: "t62",
            description: "d69"
        },
    ]
};
export const q30: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Protective-Eyewear.png",
    imageAlt: "RC Pickleball-Protective-Eyewear",
    featurePoints: [
        {
            id: "1",
            x: 53,
            y: 60,
            title: "t63",
            description: "d70"
        },
        {
            id: "2",
            x: 60,
            y: 22,
            title: "t64",
            description: "d71"
        },
        {
            id: "3",
            x: 72,
            y: 65,
            title: "t65",
            description: "d72"
        },
        {
            id: "4",
            x: 74,
            y: 40,
            title: "t66",
            description: "t73"
        },
    ]
};
export const q31: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Sweatband.png",
    imageAlt: "RC Sweatband",
    featurePoints: [
        {
            id: "1",
            x: 59,
            y: 34,
            title: "t67",
            description: ""
        },
    ]
};
export const q32: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Wristband.png",
    imageAlt: "RC RC-Wristband",
    featurePoints: [
        {
            id: "1",
            x: 59,
            y: 55,
            title: "t68",
            description: ""
        },
    ]
};
export const q33: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Protection-Tape-while.png",
    imageAlt: "RC Pickleball-Paddle-Protection-Tape-while",
    featurePoints: [
        {
            id: "1",
            x: 65,
            y: 70,
            title: "t69",
            description: ""
        },
    ]
};
export const q34: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Protection-Tape-black.png",
    imageAlt: "RC Pickleball-Paddle-Protection-Tape-black",
    featurePoints: [
        {
            id: "1",
            x: 65,
            y: 70,
            title: "t70",
            description: ""
        },
    ]
};
export const q35: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Necklace.png",
    imageAlt: "RC Pickleball-Necklace",
    featurePoints: [
        {
            id: "1",
            x: 37,
            y: 47,
            title: "t71",
            description: "d73"
        },
        {
            id: "2",
            x: 50,
            y: 88,
            title: "t72",
            description: "d74"
        },
    ]
};
export const q36: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Eraser.png",
    imageAlt: "RC Pickleball-Paddle-Eraser",
    featurePoints: [
        {
            id: "1",
            x: 54,
            y: 76,
            title: "t74",
            description: ""
        },
    ]
};
export const q37: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Gradinet-Towel.png",
    imageAlt: "RC Gradinet-Towel",
    featurePoints: [
        {
            id: "1",
            x: 42,
            y: 32,
            title: "t75",
            description: "d75"
        },
        {
            id: "2",
            x: 56,
            y: 55,
            title: "t76",
            description: ""
        },
    ]
};
export const q38: ProductFeatureConfig = {
    imageSrc: "/image/mdt/overgrip.png",
    imageAlt: "RC overgrip",
    featurePoints: [
        {
            id: "1",
            x: 57,
            y: 30,
            title: "t77",
            description: ""
        },
    ]
};
export const q39: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Eraser-Meowzart.png",
    imageAlt: "RC Pickleball-Paddle-Eraser-Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 40,
            y: 32,
            title: "t78",
            description: "d76"
        },
        {
            id: "2",
            x: 56,
            y: 70,
            title: "t79",
            description: "d77"
        },
    ]
};
export const q40: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Cover-RC.png",
    imageAlt: "RC Pickleball-Paddle-Cover-RC",
    featurePoints: [
        {
            id: "1",
            x: 40,
            y: 20,
            title: "t80",
            description: ""
        },
        {
            id: "2",
            x: 63,
            y: 65,
            title: "t81",
            description: ""
        },
    ]
};
export const q41: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Cover-Meowzart.png",
    imageAlt: "RC Pickleball-Paddle-Cover-Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 40,
            y: 30,
            title: "t82",
            description: ""
        },
        {
            id: "2",
            x: 63,
            y: 65,
            title: "t83",
            description: ""
        },
    ]
};
export const q42: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Tie-Dye-Sports-Socks.png",
    imageAlt: "RC RC-Tie-Dye-Sports-Socks",
    featurePoints: [
        {
            id: "1",
            x: 46,
            y: 60,
            title: "t84",
            description: "d78"
        },
        {
            id: "2",
            x: 48,
            y: 80,
            title: "t85",
            description: "d79"
        },
    ]
};
export const q43: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Airline-Sweat-Absorbing-Towel.png",
    imageAlt: "RC Airline-Sweat-Absorbing-Towel",
    featurePoints: [
        {
            id: "1",
            x: 65,
            y: 40,
            title: "t86",
            description: "d80"
        },
        {
            id: "2",
            x: 34,
            y: 80,
            title: "t87",
            description: ""
        },
    ]
};
export const q44: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Airline-Travel-Tumbler.png",
    imageAlt: "RC Airline-Travel-Tumbler",
    featurePoints: [
        {
            id: "1",
            x: 48,
            y: 58,
            title: "t88",
            description: ""
        },
        {
            id: "2",
            x: 60,
            y: 40,
            title: "t89",
            description: ""
        },
        {
            id: "3",
            x: 58,
            y: 75,
            title: "t90",
            description: ""
        },
    ]
};
export const q45: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Thermal-Bottle.png",
    imageAlt: "RC Thermal-Bottle",
    featurePoints: [
        {
            id: "1",
            x: 52,
            y: 15,
            title: "t91",
            description: ""
        },
        {
            id: "2",
            x: 52,
            y: 60,
            title: "t92",
            description: ""
        },
    ]
};
export const q46: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Earring.png",
    imageAlt: "RC Pickleball-Earring",
    featurePoints: [
        {
            id: "1",
            x: 36,
            y: 58,
            title: "t93",
            description: "d81"
        },
        {
            id: "2",
            x: 59,
            y: 38,
            title: "t94",
            description: "d82"
        },
    ]
};
export const q47: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Bag-Charm.png",
    imageAlt: "RC Bag Charm",
    featurePoints: [
        {
            id: "1",
            x: 42,
            y: 40,
            title: "t95",
            description: "d83"
        },
        {
            id: "2",
            x: 54,
            y: 37,
            title: "t96",
            description: "d84"
        },
    ]
};
export const q48: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Two-Tone-Athletic-Cap.png",
    imageAlt: "RC Two-Tone-Athletic-Cap",
    featurePoints: [
        {
            id: "1",
            x: 53,
            y: 60,
            title: "t97",
            description: "d85"
        },
        {
            id: "2",
            x: 72,
            y: 40,
            title: "t98",
            description: "d86"
        },
    ]
};
export const q49: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Packable-4-Way-Stretch-Cap.png",
    imageAlt: "RC Packable-4-Way-Stretch-Cap",
    featurePoints: [
        {
            id: "1",
            x: 40,
            y: 60,
            title: "t99",
            description: "d87"
        },
        {
            id: "2",
            x: 50,
            y: 23,
            title: "t100",
            description: "d88"
        },
        {
            id: "3",
            x: 65,
            y: 65,
            title: "t101",
            description: "d89"
        },
    ]
};
export const q50: ProductFeatureConfig = {
    imageSrc: "/image/mdt/RC-Packable-4-Way-Stretch-Cap---Meowzart.png",
    imageAlt: "RC RC-Packable-4-Way-Stretch-Cap---Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 40,
            y: 60,
            title: "t102",
            description: "d90"
        },
        {
            id: "2",
            x: 50,
            y: 23,
            title: "t103",
            description: "d91"
        },
        {
            id: "3",
            x: 65,
            y: 65,
            title: "t104",
            description: "d92"
        },
    ]
};
export const q51: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Mirrored-Sun-Visor.png",
    imageAlt: "RC Mirrored-Sun-Visor",
    featurePoints: [
        {
            id: "1",
            x: 38,
            y: 38,
            title: "t105",
            description: "d93"
        },
        {
            id: "2",
            x: 66,
            y: 73,
            title: "t106",
            description: "d94"
        },
    ]
};
export const q52: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Puffy-Sports-Bag.png",
    imageAlt: "RC Puffy-Sports-Bag",
    featurePoints: [
        {
            id: "1",
            x: 35,
            y: 74,
            title: "t107",
            description: "d95"
        },
        {
            id: "2",
            x: 43,
            y: 34,
            title: "t108",
            description: "d96"
        },
        {
            id: "3",
            x: 65,
            y: 48,
            title: "t109",
            description: "d97"
        },
    ]
};
export const q53: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Canvas-Bag.png",
    imageAlt: "RC Pickleball-Canvas-Bag",
    featurePoints: [
        {
            id: "1",
            x: 40,
            y: 50,
            title: "t110",
            description: "d98"
        },
        {
            id: "2",
            x: 55,
            y: 30,
            title: "t111",
            description: "d99"
        },
        {
            id: "3",
            x: 54,
            y: 73,
            title: "t112",
            description: "d100"
        },
    ]
};
export const q54: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Crossbody-Pickleball-Bag.png",
    imageAlt: "RC Crossbody-Pickleball-Bag",
    featurePoints: [
        {
            id: "1",
            x: 40,
            y: 30,
            title: "t113",
            description: "d101"
        },
        {
            id: "2",
            x: 53,
            y: 30,
            title: "t114",
            description: "d102"
        },
        {
            id: "3",
            x: 58,
            y: 70,
            title: "t115",
            description: "d103"
        },
    ]
};
export const q55: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-Paddle-Bag.png",
    imageAlt: "RC Pickleball-Paddle-Bag",
    featurePoints: [
        {
            id: "1",
            x: 35,
            y: 32,
            title: "t116",
            description: "d104"
        },
        {
            id: "2",
            x: 56,
            y: 84,
            title: "t117",
            description: "d105"
        },
        {
            id: "3",
            x: 60,
            y: 50,
            title: "t119",
            description: "d106"
        },
    ]
};
export const q56: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Dopamine-Pickleball-Paddle-Bag.png",
    imageAlt: "RC Dopamine-Pickleball-Paddle-Bag",
    featurePoints: [
        {
            id: "1",
            x: 38,
            y: 68,
            title: "t120",
            description: "d107"
        },
        {
            id: "2",
            x: 43,
            y: 48,
            title: "t121",
            description: "d108"
        },
        {
            id: "3",
            x: 54,
            y: 23,
            title: "t122",
            description: "d109"
        },
        {
            id: "4",
            x: 60,
            y: 75,
            title: "t123",
            description: "d110"
        },
    ]
};
export const q57: ProductFeatureConfig = {
    imageSrc: "/image/mdt/Pickleball-functional-Backpack.png",
    imageAlt: "RC Pickleball-functional-Backpack",
    featurePoints: [
        {
            id: "1",
            x: 43,
            y: 30,
            title: "t124",
            description: "d111"
        },
        {
            id: "2",
            x: 43,
            y: 60,
            title: "t125",
            description: "d112"
        },
        {
            id: "3",
            x: 58,
            y: 25,
            title: "t126",
            description: "d113"
        },
    ]
};
// 产品特性数据映射
export const productFeaturesMap: Record<string, ProductFeatureConfig> = {
    'quasar-raw-carbon-non-honeycomb-core-pickleball-paddle-neontrail-meowzart': q1,
    'mbti-composite-pickleball-paddle-judging': q2,
    'mbti-fiberglass-pickleball-paddle-introverted': q2,
    'mbti-composite-pickleball-paddle-perceiving': q2,
    'mbti-fiberglass-pickleball-paddle-extraverted': q2,
    'nova-fiberglass-pickleball-paddle-kiki': q3,
    'nova-fiberglass-pickleball-paddle-meowzart-go-pop': q4,
    'quasar-titanium-carbon-nomex-pickleball-paddle-flare': q5,
    'quasar-raw-carbon-dynamic-neck-core-pickleball-paddle-cyberbeat-meowzart': q6,
    'quasar-kevlar-aramid-pickleball-paddle-stride': q7,
    'quasar-raw-carbon-dynamic-neck-core-pickleball-paddle-dreamglow-pop': q8,
    'quasar-3d-18k-carbon-pickleball-paddle-tess': q9,
    'pulse-18k-carbon-fiber-pickleball-paddle-vivid': q10,
    'nova-fiberglass-pickleball-paddle-prisma': q11,
    'nova-composite-pickleball-paddle-slam-genius': q12,
    'axis-3k-carbon-fiber-pickleball-paddle-flex': q13,
    'nova-fiberglass-pickleball-paddle-meowzart': q14,
    'pulse-raw-carbon-pickleball-paddle-chaotic-flow': q15,
    'axis-composite-pickleball-paddle-fade': q16,
    'axis-3k-carbon-fiber-pickleball-paddle-flex-2': q17,
    'nova-fiberglass-pickleball-paddle-style-meets-performance': q18,
    'pulse-graphite-air-flow-pickleball-paddle-aero': q19,
    'nova-carbon-fiber-pickleball-paddle-control-spin-and-style': q20,
    'glow-pickleball': q21,
    'outdoor-pickleballs-virgo': q22,
    'outdoor-pickleballs-libra': q23,
    'outdoor-pickleballs-leo': q24,
    'low-noise-pickleball': q25,
    'rc-tie-dye-sports-socks': q26,
    '3-in-1-paddle-bag': q27,
    'convertible-pickleball-bag': q28,
    'pickleball-bracelet':q29,
    'pickleball-protective-eyewear':q30,
    'rc-sweatband':q31,
    'rc-wristband':q32,
    'pickleball-paddle-protection-tape-pu':q33,
    'pickleball-paddle-protection-tape-black':q34,
    'pickleball-necklace':q35,
    'pickleball-paddle-eraser-rc':q36,
    'rc-gradinet-towel':q37,
    'overgrip': q38,
    'pickleball-paddle-eraser-meowzart': q39,
    'pickleball-paddle-cover-rc': q40,
    'pickleball-paddle-cover-meowzart': q41,
    'rc-tie-dye-sports-socks-2': q42,
    'rc-airline-sweat-absorbing-towel': q43,
    'rc-airline-travel-tumbler':q44,
    'rc-thermal-bottle':q45,
    'pickleball-earring':q46,
    'rc-bag-charm':q47,
    'rc-two-tone-athletic-cap':q48,
    'rc-packable-4-way-stretch-cap':q49,
    'rc-packable-4-way-stretch-cap-meowzart':q50,
    'mirrored-sun-visor':q51,
    'puffy-sports-bag':q52,
    'pickleball-canvas-bag':q53,
    'crossbody-pickleball-bag':q54,
    'pickleball-paddle-bag':q55,
    'dopamine-pickleball-paddle-bag':q56,
    'pickleball-functional-backpack':q57
};
// 默认产品特性（如果没有指定产品或找不到对应产品，返回 null 表示隐藏）
export const defaultProductFeatures = null;
