
import React from "react";
import { MyPageProps } from "@/lib/@types/base";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { unstable_setRequestLocale } from "next-intl/server";
import Compare from "@/components/Compare";
import TouristOrderInquiryPage from "@/components/Checkout/TouristOrderInquiryPage";
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page=["tourist-order-inquiry"]
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

export default function ContactUs(props:MyPageProps) {
  unstable_setRequestLocale(props.params.locale);
	// const t = useTranslations();
	return <>
	<TouristOrderInquiryPage />
	</>
}
