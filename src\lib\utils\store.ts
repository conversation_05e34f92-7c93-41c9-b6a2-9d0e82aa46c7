import { PersistStorage } from "zustand/middleware";
import { getCookie, setCookie } from "cookies-next";

export const sessionStoragePersistStorage: PersistStorage<any> = {
	getItem: async (name: string) => {
		const value = sessionStorage.getItem(name);
		return value ? JSON.parse(value) : null;
	},
	setItem: async (name: string, value: Object) => {
		sessionStorage.setItem(name, JSON.stringify(value));
	},
	removeItem: async (name: string) => {
		sessionStorage.removeItem(name);
	},
} as any;

export const cookieStoragePersistStorage = (maxAge?: number): PersistStorage<any> => {
	return {
		getItem: async (name: string) => {
			const value = getCookie(name);
			return value ? JSON.parse(value as string) : null;
		},
		setItem: async (name: string, value: Object) => {
			if (maxAge) {
				setCookie(name, JSON.stringify(value), { maxAge });
			} else {
				setCookie(name, JSON.stringify(value));
			}
		},
		removeItem: async (name: string) => {
			setCookie(name, null);
		},
	} as any;
};
