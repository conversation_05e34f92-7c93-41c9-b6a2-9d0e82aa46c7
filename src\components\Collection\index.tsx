"use client";
import { fetchSearchProductsData } from "@/lib/api/product";
import { useLoveStore } from "@/lib/store/love.store";
import clsx from "clsx";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { RingLoader } from "react-spinners";
import ProductCard from "../Product/product-card";
import EmptyState from "../EmptyState";
import { ProductListItemFragment } from "@/gql/graphql";
import Masonry from "react-masonry-css";
import { HomeTaile } from "../Contact/ConcatPage";
import CompareList from "../CompareList";
import MyEmpty from "../MyEmpty";
export const fetchData = async (
	ids: number[],
	setLoading: (loading: boolean) => void,
	locale: string,
): Promise<any> => {
	try {
		setLoading(true);
		const { products } = await fetchSearchProductsData({ ids: ids, locale, channel: "default-channel" });
		console.log(products.edges, "productsproductsproducts");

		return products.edges;
	} finally {
		setLoading(false);
	}
};

function Index() {
	const [loveList, setLoveList] = useState([]);
	const t = useTranslations();
	const { loveIds, removeAllLove } = useLoveStore();
	const [loading, setLoading] = useState(false);
	const locale = useLocale();
	useEffect(() => {
		if (!loveIds.length || !locale) return setLoveList([]);
		fetchData(loveIds, setLoading, locale).then((res) => {
			setLoveList(res);
		});
	}, [loveIds, locale]);

	const breakpointColumnsObj = {
		default: 4,
		1100: 3,
		768: 2
	};


	return (
		<div>
			<HomeTaile msg={t("common.COLLECTION")} />
			<div className="container my-16">
				{!loading && !!loveList.length && (
					<div className="e-flex mb-4">
						<div className="cursor-pointer text-sm duration-300 hover:text-main hover:underline" onClick={removeAllLove}>
							<i className="ri-delete-bin-6-line"></i>
							{t("common.2404e3fb11a4cd4619b87d75ba56e345d8b0")}
						</div>
					</div>
				)}
				{loading ? (
					<div className="c-flex min-h-[50vh]">
						<RingLoader color="#000" />
					</div>
				) : loveList.length ? (
					<Masonry breakpointCols={breakpointColumnsObj}
						className="my-masonry-grid"
						columnClassName="my-masonry-grid_column">
						{loveList.map((item, index) => {
							return (
								<div key={index}>
									<ProductCard showCompare={false} productItem={item.node as ProductListItemFragment} locale={locale} />
								</div>


							);
						})}
					</Masonry>
				) : (
					<MyEmpty text={t('nav.Wishlist')} description={t('nav.Youhave')} className="py-20 max-md:py-4">
						<i className="ri-heart-3-line text-3xl  !text-black"></i>
					</MyEmpty>
				)}
			</div>
			<div className="max-md:hidden">
				<CompareList />
			</div>
		</div>
	);
}

export default React.memo(Index);
