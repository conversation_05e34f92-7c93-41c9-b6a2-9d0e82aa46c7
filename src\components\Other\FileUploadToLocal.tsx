'use client';
import { InboxOutlined } from '@ant-design/icons';
import { message, Upload, Slider, Row, Col, InputNumber } from 'antd';
import React, { useState } from 'react';
import type { UploadProps } from 'antd';
import LoadNormalModal from "@/components/3DNormalModal";
import { ColorPicker } from 'antd';


export default function FileUploadAndDisplay() {
	const { Dragger } = Upload;
	const [fileArrayBuffer, setFileArrayBuffer] = useState<ArrayBuffer | null>(null);
	const [fileName, setFileName] = useState<string | null>(null);
	const [metalness, setMetalness] = useState<number>(0.5);
	const [roughness, setRoughness] = useState<number>(0.5);
	const [color, setColor] = useState<string>('#cccccc');

	const props: UploadProps = {
		name: 'file',
		multiple: false, // 设置为单文件上传
		beforeUpload(file) {
			// 使用 FileReader 读取文件内容为 ArrayBuffer
			const reader = new FileReader();
			reader.onload = (e) => {
				const result = e.target?.result as ArrayBuffer;
				// 设置 ArrayBuffer 和文件名
				setFileArrayBuffer(result);
				setFileName(file.name);
				message.success(`${file.name} file uploaded and ready for display.`);
			};
			reader.onerror = () => {
				message.error(`${file.name} file upload failed.`);
			};
			// 读取文件内容为 ArrayBuffer
			reader.readAsArrayBuffer(file);
			// 防止默认上传行为
			return Upload.LIST_IGNORE;
		},
		onDrop(e) {
			console.log('Dropped files', e.dataTransfer.files);
		},
	};

	return (
		<>
			<Row gutter={16} className="mb-8">
				<Col span={8}>
					<p>Metalness</p>
					<Slider
						min={0}
						max={1}
						step={0.01}
						value={metalness}
						onChange={(value) => setMetalness(value)}
					/>
				</Col>
				<Col span={8}>
					<p>roughness</p>
					<Slider
						min={0}
						max={1}
						step={0.01}
						value={roughness}
						onChange={(value) => setRoughness(value)}
					/>
				</Col>
				<Col span={8}>
					<p>颜色</p>
					<ColorPicker value={color} onChange={(value,css) => setColor(css)} />
				</Col>
			</Row>

			<Dragger {...props} className="h-[300px] w-[300px]" accept={'.glb,.gltf,.3dm,.obj,.fbx,.stl,.stp,.step'}>
				<p className="ant-upload-drag-icon">
					<InboxOutlined />
				</p>
				<p className="ant-upload-text">Please click/drag in this area to upload a 3D file</p>
				<p className="ant-upload-hint">
					Support file formats ：glb，gltf，3dm，obj，fbx，stl，stp，step.
				</p>
			</Dragger>

			{/* 如果文件已上传，展示它 */}
			{fileArrayBuffer && (
				<LoadNormalModal
					modalUrl={''} // 传递 ArrayBuffer 给 View3DModal
					arrayBuffer={fileArrayBuffer}
					className="!h-[800px]  !rounded-lg pt-20 bg-gray-50 mt-20"
					fileName={fileName}
					metalness={metalness}
					roughness={roughness}
					color={color}
				/>
			)}
		</>
	);
}
