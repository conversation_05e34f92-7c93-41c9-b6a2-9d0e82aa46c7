//定义
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
 //右边栏打开于关闭
type State = {
  open: boolean;
  setOpen: (bool:boolean) => void;
};
export const CartDrawerOpen = create(
  devtools(
    persist<State>(
      (set, get) => ({
        open: false,
        setOpen: (bool:boolean) => set((state) => ({ open: bool})),
      }),
      {
        name: "CartDrawerOpen",
      }
    ),
    {
      name: "CartDrawerOpen",
    }
  )
);
