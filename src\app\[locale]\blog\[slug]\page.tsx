import moment from "moment";
import { Avatar } from "antd";
import React, { useEffect } from "react";
import { Link } from "@/navigation";
import { getBlogPageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { type SlugMyPageProps } from "@/lib/@types/base";
import { getBlogCls, getBlogDetail } from "@/lib/api/blog";
import { SocialShare } from "@/components/Other/Share";
import { fetchBlogList } from "@/app/[locale]/page";
import BlogArticle from "@/components/Blogs/BlogArticle";
import FollowUs from "@/components/Social/FollowUs";
import { ClockCircleFilled } from "@ant-design/icons";
import BlogComment from "@/components/Blogs/BlogComment.tsx";
import FeatureBlogs from "@/components/Blogs/FeaturedBlogs.tsx";
import { NotFouned } from "@/components/404";
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";

interface BlogDetailPageProps {
	params: {
		locale: string;
		slug: string;
	};
}

// 动态生成SEO的metadata
export const generateMetadata = async (props: SlugMyPageProps) => {
	const seo = await getBlogPageSeo(props);
	// @ts-ignore
	return generateSeo(props, {
		...seo,
		currentPath: `/blog/${props.params.slug}`,
		ogType: "article",
	});
};

// 博客详情页面组件
export default async function BlogDetailPage({ params }: BlogDetailPageProps) {
	const { slug, locale } = params;
	// 获取博客详情
	const blog = await getBlogDetail(slug, locale);
	if (!blog) {
		return <NotFouned link={"/blog"} />;
	}

	// 获取相关文章
	const { blogList } = await fetchBlogList({
		lang_code: { lang_code: params.locale },
		page: 1,
		limit: 6,
		cls_slug_in: [{ slug: blog?.blog_classification_list[0].cls_slug || "default" }],
	});
	let preSlug = { slug: "", title: "", upload_time: "" };
	let nxtSlug = { slug: "", title: "", upload_time: "" };
	// console.log(blogList,2323);
	if (blogList.length > 0) {
		const index = blogList.findIndex((item) => item.blog_slug === slug);
		// console.log(index, "index");

		if (index > -1) {
			// 上一篇（循环到最后）
			const prevIndex = (index - 1 + blogList.length) % blogList.length;
			preSlug = {
				slug: blogList[prevIndex].blog_slug,
				title: blogList[prevIndex].blog_title,
				upload_time: blogList[prevIndex].blog_upload_time,
			};

			// 下一篇（循环到第一）
			const nextIndex = (index + 1) % blogList.length;
			nxtSlug = {
				slug: blogList[nextIndex].blog_slug,
				title: blogList[nextIndex].blog_title,
				upload_time: blogList[nextIndex].blog_upload_time,
			};
		}
	}

	const clsList = await getBlogCls({ lang_code: { lang_code: params.locale } });

	unstable_setRequestLocale(params.locale);
	const t = await getTranslations();
	const cleanArticleBody = blog.blog_content
		.replace(/<[^>]+>/g, "")
		.replace(/\n+/g, " ")
		.replace(/\s+/g, " ")
		.trim();

	// 构建结构化数据
	const structuredData = {
		"@context": "https://schema.org",
		"@type": "BlogPosting",
		"@id": `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${blog.slug}`,
		mainEntityOfPage: {
			"@type": "WebPage",
			"@id": `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${blog.slug}`,
		},
		headline: blog.blog_title,
		description: blog.blog_seoDescription || blog.blog_excerpt,
		image: blog.cover_url,
		keywords: blog.blog_seoKeyword.join(", "),
		datePublished: blog.update_time,
		dateModified: blog.update_time,
		author: {
			"@type": "Person",
			name: blog.blog_author,
		},
		publisher: {
			"@type": "Organization",
			name: process.env.NEXT_PUBLIC_COMPANY_NAME,
			logo: {
				"@type": "ImageObject",
				url: `${process.env.NEXT_PUBLIC_SITE_URL}/image/logo-r.png`,
			},
		},
		articleSection: blog.blog_classification_list[0].cls_name,
		articleBody: cleanArticleBody,
	};
	return (
		<>
			<script
				id={`blog-structured-data-${slug}`}
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
			/>
			{/* 隐藏图片用于SEO，添加itemprop="image"属性 */}
			{blog.cover_url && (
				<div style={{ display: "none" }}>
					<img src={blog.cover_url} alt={blog.blog_title} itemProp="image" />
				</div>
			)}

			<div className="bg-white">
				{/* 面包屑导航 */}
				<div className="border-b border-gray-100 bg-gray-50/50">
					<div className="container py-6">
						<nav className="flex items-center space-x-2 text-sm">
							<Link
								href="/"
								className="text-gray-600 transition-colors hover:text-black"
							>
								{t("menu.Home")}
							</Link>
							<span className="text-gray-400">/</span>
							<Link
								href="/blog"
								className="text-gray-600 transition-colors hover:text-black"
							>
								{t("menu.Blog")}
							</Link>
							<span className="text-gray-400">/</span>
							<span className="text-gray-900 font-medium">
								{blog.blog_title}
							</span>
						</nav>
					</div>
				</div>

				{/* 主要内容区域 */}
				<div className="container mx-auto py-12">
					<BlogArticle
						content={blog?.blog_content || ""}
						blogList={blogList}
						blog={blog}
						clsList={clsList}
					/>

					{/* 上一篇和下一篇博客导航 */}
					{(preSlug.slug || nxtSlug.slug) && (
						<nav className="my-12 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
							<div className="flex items-center justify-between gap-8">
								{/* Previous Post */}
								{preSlug.title ? (
									<Link
										href={`/blog/${preSlug.slug}`}
										className="group flex flex-1 items-center space-x-4 rounded-lg p-4 transition-all duration-300 hover:bg-gray-50 hover:shadow-md"
									>
										<div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-gray-100 transition-all duration-300 group-hover:bg-black group-hover:shadow-lg">
											<svg
												className="h-5 w-5 text-gray-500 transition-colors group-hover:text-white"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth={2}
													d="M15 19l-7-7 7-7"
												/>
											</svg>
										</div>
										<div className="min-w-0 flex-1">
											<p className="mb-1 text-xs font-semibold uppercase tracking-wider text-gray-500">
												{t("blog.PreviousPost")}
											</p>
											<h4 className="line-clamp-2 text-base font-semibold text-gray-900 transition-colors group-hover:text-black">
												{preSlug.title}
											</h4>
											<time className="mt-1 text-sm text-gray-500">
												{moment(preSlug?.upload_time).format("MMM DD, YYYY")}
											</time>
										</div>
									</Link>
								) : (
									<div className="flex-1" />
								)}

								{/* Divider */}
								{preSlug.title && nxtSlug.title && (
									<div className="h-16 w-px bg-gray-200" />
								)}

								{/* Next Post */}
								{nxtSlug.title ? (
									<Link
										href={`/blog/${nxtSlug.slug}`}
										className="group flex flex-1 items-center space-x-4 rounded-lg p-4 text-right transition-all duration-300 hover:bg-gray-50 hover:shadow-md"
									>
										<div className="min-w-0 flex-1">
											<p className="mb-1 text-xs font-semibold uppercase tracking-wider text-gray-500">
												{t("blog.NextPost")}
											</p>
											<h4 className="line-clamp-2 text-base font-semibold text-gray-900 transition-colors group-hover:text-black">
												{nxtSlug.title}
											</h4>
											<time className="mt-1 text-sm text-gray-500">
												{moment(nxtSlug?.upload_time).format("MMM DD, YYYY")}
											</time>
										</div>
										<div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-gray-100 transition-all duration-300 group-hover:bg-black group-hover:shadow-lg">
											<svg
												className="h-5 w-5 text-gray-500 transition-colors group-hover:text-white"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
											</svg>
										</div>
									</Link>
								) : (
									<div className="flex-1" />
								)}
							</div>
						</nav>
					)}
				</div>

				{/* 相关文章区域 */}
				<div className="bg-gray-50 py-16">
					<div className="container">
						<div className="mb-12 text-center">
							<h2 className="text-3xl font-bold text-gray-900 mb-4">
								{t("nav.Featured Blogs")}
							</h2>
							<div className="mx-auto h-1 w-20 bg-black rounded-full"></div>
						</div>

						<div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
							{blogList.map((item) => (
								<Link
									key={item.blog_id}
									href={"/blog/" + item.blog_slug}
									className="group flex flex-col overflow-hidden rounded-lg bg-white shadow-md transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
								>
									{/* 图片容器 */}
									<div className="relative aspect-[16/9] overflow-hidden">
										<img
											src={item.blog_cover_origin}
											alt={item.blog_title}
											className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
										/>
										{/* 渐变遮罩 */}
										<div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

										{/* 玻璃扫光效果 */}
										<div className="absolute inset-0 translate-x-[-100%] skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent transition-transform duration-700 group-hover:translate-x-[100%]" />
									</div>

									{/* 文字内容 */}
									<div className="flex flex-1 flex-col p-6">
										<h3 className="mb-3 line-clamp-2 text-lg font-semibold text-gray-900 transition-colors group-hover:text-black">
											{item.blog_title}
										</h3>

										{/* 文章摘要 */}
										<p className="mb-4 line-clamp-3 flex-1 text-sm text-gray-600 leading-relaxed">
											{item.blog_excerpt}
										</p>

										{/* 底部信息 */}
										<div className="flex items-center justify-between border-t border-gray-100 pt-4">
											<time className="flex items-center gap-2 text-sm text-gray-500">
												<svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
													<path
														strokeLinecap="round"
														strokeLinejoin="round"
														strokeWidth={2}
														d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
													/>
												</svg>
												{moment(item.blog_upload_time).format("MMM DD, YYYY")}
											</time>

											<div className="flex items-center text-sm font-medium text-gray-900 transition-colors group-hover:text-black">
												{t("blog.ReadMore")}
												<svg className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
													<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
												</svg>
											</div>
										</div>
									</div>
								</Link>
							))}
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
