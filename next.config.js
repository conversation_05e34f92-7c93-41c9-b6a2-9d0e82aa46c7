import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

/** @types {import("next").NextConfig} */
const nextConfig = {
	swcMinify: true,
	compress: true,
	reactStrictMode: false,
	transpilePackages: ["three"],
	async headers() {
		return [
			{
				source: "/:path*",
				headers: [
					{
						key: "Cross-Origin-Opener-Policy",
						value: "same-origin-allow-popups",
					},
				],
			},
			{
				source: "/image/:path*",
				headers: [
					{
						key: "Cache-Control",
						value: "public, max-age=31536000, immutable",
					},
					{
						key: "Accept-Ranges",
						value: "bytes",
					},
				],
			},
			{
				source: "/fonts/:path*",
				headers: [
					{
						key: "Cache-Control",
						value: "public, max-age=31536000, immutable",
					},
				],
			},
		];
	},

	images: {
		remotePatterns: [
			{
				protocol: "https", // 如果你的图片是通过 https 协议加载的
				hostname: "*", // 允许所有域名
				pathname: "/**", // 允许所有路径
			},
		],
	},
	async rewrites() {
		return [
			{
				source: "/sitemap.xml",
				destination: "/api/sitemap", // Proxy to Backend
			},
		];
	},
	experimental: {
		typedRoutes: false,
		serverComponentsExternalPackages: ["isolated-vm"],
	},
};

export default withNextIntl(nextConfig);
