import FileUploadToLocale from "@/components/Other/FileUploadToLocal";
import type { MyPageProps } from "@/lib/@types/base";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";

export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["view-3d"]
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
}
const View3D=()=>{
	return <>
		<div className="container pt-40 pb-10">
			<FileUploadToLocale></FileUploadToLocale>
		</div>
	</>
}
export default  View3D;
