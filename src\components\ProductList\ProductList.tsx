"use client";
import ProductCard from "@/components/Product/product-card";
import { ProductListItemFragment, ProductListPaginatedQuery } from "@/gql/graphql";
import { Empty, message, Spin } from "antd";
import React, { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { fetchProductByCategoryData, fetchProductData } from "@/lib/api/product";
import { Button } from "../Button";
import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { FILTER_ENABLED_SLUGS } from "../ProductsLayoutPage";
import Masonry from "react-masonry-css";
import CompareList from "../CompareList";
import { useCompareStore } from "@/lib/store/Compare.store";
import MyEmpty from "../MyEmpty";
import { useBreakpointColumns } from "@/lib/store/breakpointColumnsObj";
import { useProductSortStore, sortProducts } from "@/lib/store/productSort.store";
import { useProductFilterStore } from "@/lib/store/productFilter.store";

const ProductList = ({
	products,
	locale,
	channel,
	slug,

}: {
	products: any;
	locale: string;
	channel: string;
	slug?: string;
}) => {
	// 搜索页面无loadmore ==> ?
	const [hasMore, setHasMore] = useState(products?.pageInfo?.hasNextPage);
	const [moreLoading, setMoreLoading] = useState(false);
	const [after, setAfter] = useState(products?.pageInfo?.endCursor);
	const [productsList, setProductsList] = useState(products);
	const t = useTranslations();
	const pathname = usePathname(); // 获取当前路径

	// 获取筛选和排序状态
	const { sortValue, originalOrder, setOriginalOrder, resetSortState } = useProductSortStore();
	const { filters, isFilterActive, resetFilterState } = useProductFilterStore();
	let { breakpointColumns } = useBreakpointColumns();

	// 是否为需要自动加载全部数据的页面
	const isAutoLoadPage = slug && FILTER_ENABLED_SLUGS.includes(slug);

	// 是否显示比较功能 - 只有匹配到特定的products/slug才显示
	const shouldShowCompare = slug && ['paddle',
		'social-set',
		'nova-series',
		'axis-series',
		'pulse-series',
		'quasar-series',
		'ip-collection',
		'usap-approved',].includes(slug);

	// 是否正在递归加载中
	const [isRecursiveLoading, setIsRecursiveLoading] = useState(false);

	// 初始加载是否完成
	const initialLoadDoneRef = useRef(false);

	// 保存路径的ref，用于检测路由变化
	const prevPathRef = useRef(pathname);

	// 使用ref保存上一次的排序值，用于判断是否需要重排，频繁触发重排影响性能，数据变多可能会卡顿
	const prevSortValueRef = useRef(sortValue);

	// 使用ref保存上一次的筛选条件，用于检测变化
	const prevFiltersRef = useRef(JSON.stringify(filters));

	// 监听路由变化，重置筛选和排序状态
	useEffect(() => {
		if (pathname !== prevPathRef.current) {
			// 路径变化了，重置状态
			resetSortState();
			resetFilterState();
			prevPathRef.current = pathname;
			initialLoadDoneRef.current = false;
		}
	}, [pathname, resetSortState, resetFilterState]);

	// 初始化时保存原始顺序
	useEffect(() => {
		if (products?.edges?.length && Object.keys(originalOrder).length === 0) {
			setOriginalOrder(products.edges);
		}
	}, [products, originalOrder, setOriginalOrder]);

	// 对新加载的数据进行预排序，减少DOM重绘
	const onLoadMore = useCallback(async (customCursor = null) => {
		try {
			setMoreLoading(true);
			let res = null;
			// 使用传入的自定义游标或者状态中的after
			const cursorToUse = customCursor !== null ? customCursor : after;

			if (slug) {
				res = (await fetchProductByCategoryData({ slug, locale, channel, after: cursorToUse }))?.category;
			} else {
				res = await fetchProductData({ locale, channel, after: cursorToUse });
			}

			if (!res || !res.products || !res.products.edges) {
				throw new Error("Failed to fetch products");
			}

			// 保存新加载数据的原始顺序
			const newEdges = res.products.edges;
			const currentLength = productsList.edges.length;

			// 更新原始顺序映射
			const newOrderMap = { ...originalOrder };
			newEdges.forEach((product, index) => {
				if (product.node && product.node.id) {
					// 新加载的数据索引从当前长度开始
					newOrderMap[product.node.id] = currentLength + index;
				}
			});
			setOriginalOrder([...productsList.edges, ...newEdges]);

			// 对新加载的数据进行预排序，然后再合并
			const sortedNewEdges = sortProducts(newEdges, sortValue, newOrderMap);

			// 更新状态时，将已排序的新数据与现有数据合并
			setProductsList((prev) => {
				// 如果排序方式是默认的，直接追加
				if (sortValue === 'default') {
					return {
						...prev,
						edges: [...prev.edges, ...newEdges], // 使用未排序的原始数据
					};
				}

				// 否则，需要对整个列表重新排序
				const allEdges = [...prev.edges, ...sortedNewEdges];
				const sortedAllEdges = sortProducts(allEdges, sortValue, newOrderMap);

				return {
					...prev,
					edges: sortedAllEdges,
				};
			});

			// 更新分页状态
			const hasNextPage = res.products.pageInfo.hasNextPage;
			const endCursor = res.products.pageInfo.endCursor;

			setHasMore(hasNextPage);
			setAfter(endCursor);

			// 返回分页信息，供递归加载使用
			return {
				hasNextPage,
				endCursor,
				newEdgesCount: newEdges.length
			};
		} catch (e) {
			console.error("Failed to load products:", e);
			message.error("load error");
			setHasMore(false);
			return { hasNextPage: false, endCursor: null, newEdgesCount: 0 };
		} finally {
			setMoreLoading(false);
		}
	}, [locale, channel, after, slug, sortValue, productsList, originalOrder, setOriginalOrder, t]);

	// 递归加载所有页面的函数
	const loadAllPages = useCallback(async () => {
		// 如果没有更多数据或者已经在加载中，则不执行
		if (!hasMore || isRecursiveLoading) return;

		// 设置递归加载状态
		setIsRecursiveLoading(true);

		try {
			const MAX_PAGES = 20; // 最大页数限制，避免无限循环
			let currentPage = 0;
			let stillHasMore = true;
			let noNewDataCount = 0; // 计数器，用于检测没有新数据的情况
			let currentCursor = after; // 使用当前state中的after作为初始游标

			// 循环加载所有页面，直到没有更多数据或者达到最大页数
			while (stillHasMore && currentPage < MAX_PAGES) {
				currentPage++;
				console.log(`加载第${currentPage}页，使用游标: ${currentCursor}`);

				// 显式传递当前游标到onLoadMore
				const result = await onLoadMore(currentCursor);

				// 用返回的游标更新当前游标，以便下次循环使用
				currentCursor = result.endCursor;

				// 更新循环条件
				stillHasMore = result.hasNextPage;

				// 如果加载的新数据为0，计数器+1
				if (result.newEdgesCount === 0) {
					noNewDataCount++;
				} else {
					noNewDataCount = 0; // 如果有新数据，重置计数器
				}

				// 如果连续两次没有新数据，认为已加载完毕
				if (noNewDataCount >= 2) {
					stillHasMore = false;
					setHasMore(false);
					break;
				}

				// 如果没有下一页或没有游标，结束循环
				if (!result.hasNextPage || !result.endCursor) {
					break;
				}

				// 添加短暂延迟，避免请求过快
				if (stillHasMore) {
					await new Promise(resolve => setTimeout(resolve, 500));
				}
			}

			// 如果达到最大页数限制，强制设置hasMore为false
			if (currentPage >= MAX_PAGES) {
				setHasMore(false);
			}

			// 标记初始加载完成
			initialLoadDoneRef.current = true;
		} catch (error) {
			console.error("Failed to load all pages:", error);
			message.error(t("common.loadAllError"));
		} finally {
			setIsRecursiveLoading(false);
		}
	}, [hasMore, isRecursiveLoading, onLoadMore, t]);

	// 特定slug页面的初始自动加载
	useEffect(() => {
		// 只有特定slug页面、有更多数据、且尚未完成初始加载时执行
		if (isAutoLoadPage && hasMore && !initialLoadDoneRef.current && !isRecursiveLoading) {
			// 延迟执行，给页面渲染一些时间
			const timer = setTimeout(() => {
				loadAllPages();
			}, 1000);

			return () => clearTimeout(timer);
		}
	}, [isAutoLoadPage, hasMore, isRecursiveLoading, loadAllPages]);

	// 监听筛选条件变化，在特定页面上重新加载所有数据
	useEffect(() => {
		const currentFiltersStr = JSON.stringify(filters);

		// 检查筛选条件是否有变化
		if (prevFiltersRef.current !== currentFiltersStr) {
			prevFiltersRef.current = currentFiltersStr;

			// 仅在特定slug页面且有更多数据时，重新加载所有页面
			if (isAutoLoadPage && hasMore && isFilterActive && !isRecursiveLoading) {
				// 延迟执行，避免频繁触发
				const timer = setTimeout(() => {
					loadAllPages();
				}, 800);

				return () => clearTimeout(timer);
			}
		}
	}, [filters, isAutoLoadPage, hasMore, isFilterActive, isRecursiveLoading, loadAllPages]);

	// 根据筛选条件过滤产品
	const filterProducts = useCallback((products) => {
		if (!isFilterActive || !products?.edges?.length) return products;

		// 提取增强匹配算法为通用函数
		const hasMatchingAttribute = (attrValues, filterValues) => {
			if (!attrValues || !attrValues.length) return false;

			// 从属性值中提取名称并标准化
			const normalizedAttrValues = attrValues.map(v => v.name.toLowerCase().trim());

			// 检查是否有任何筛选值匹配属性值
			return filterValues.some(filterVal => {
				const normalizedFilterVal = filterVal.toLowerCase().trim().replace(/\s+/g, '');
				return normalizedAttrValues.some(attrVal => {
					const normalizedAttrVal = attrVal.replace(/\s+/g, '');
					return normalizedAttrVal === normalizedFilterVal ||
						normalizedAttrVal.includes(normalizedFilterVal) ||
						normalizedFilterVal.includes(normalizedAttrVal);
				});
			});
		};

		// 通用属性过滤函数
		const filterByAttribute = (product, filterValues, attributeSlug) => {
			if (!filterValues.length) return true; // 没有筛选值，不过滤

			const productAttributes = product.attributes || [];
			const attr = productAttributes.find(attr => attr.attribute?.slug === attributeSlug);

			if (!attr) return false; // 没有找到对应属性，过滤掉

			return hasMatchingAttribute(attr.values, filterValues);
		};

		const filteredEdges = products.edges.filter((item) => {
			const product = item.node;
			// 可用性过滤
			if (filters.availability.length > 0 && filters.availability.length < 2) {
				// 只有当不是同时选择了有库存和无库存时才进行筛选
				// 检查产品是否有库存（任何变体的quantityAvailable > 0）
				const variants = product.variants || [];
				const hasStock = variants.some(variant => (variant.quantityAvailable || 0) > 0);

				// 如果选择了"有库存"但产品无库存，或选择了"无库存"但产品有库存，则过滤掉
				if (
					(filters.availability.includes('inStock') && !hasStock) ||
					(filters.availability.includes('outOfStock') && hasStock)
				) {
					return false;
				}
			}
			// 如果同时选择了"有库存"和"无库存"或者没有选择任何库存选项，则显示所有产品

			// 价格过滤
			if (filters.price[0] > 0 || filters.price[1] < 180) {
				const productPrice = parseFloat((product.pricing?.priceRange?.start?.gross?.amount || 0).toString());
				if (productPrice < filters.price[0] || productPrice > filters.price[1]) {
					return false;
				}
			}

			// 使用通用属性过滤函数进行所有属性的过滤
			// 材料过滤
			if (!filterByAttribute(product, filters.material, 'main-material')) return false;

			// 核心技术过滤
			if (!filterByAttribute(product, filters.coreTechnology, 'core-technology')) return false;

			// 边缘保护过滤
			if (!filterByAttribute(product, filters.edgeGuard, 'edge-guard')) return false;

			// 厚度过滤
			if (!filterByAttribute(product, filters.thickness, 'thickness')) return false;

			// 重量过滤
			if (!filterByAttribute(product, filters.weight, 'weight')) return false;

			// 性能类型过滤
			if (!filterByAttribute(product, filters.performanceType, 'performance-type')) return false;

			// 二级分类过滤
			if (filters.subcategory && filters.subcategory.length > 0) {
				// 假设产品有category字段，且category.slug为二级分类slug
				if (!product.category || !filters.subcategory.includes(product.category.slug)) {
					return false;
				}
			}

			return true;
		});

		return { ...products, edges: filteredEdges };
	}, [filters, isFilterActive]);

	// 根据排序值对产品进行排序并应用筛选条件
	const filteredAndSortedProducts = useMemo(() => {
		if (!productsList?.edges?.length) return { edges: [] };

		// 首先应用筛选条件
		const filteredProducts = filterProducts(productsList);

		// 如果排序值没有变化，不需要重新排序
		if (prevSortValueRef.current === sortValue) {
			return filteredProducts;
		}

		// 更新ref
		prevSortValueRef.current = sortValue;

		// 使用sortProducts工具函数进行排序
		const sortedEdges = sortProducts(filteredProducts.edges, sortValue, originalOrder);

		return { ...filteredProducts, edges: sortedEdges };
	}, [productsList, sortValue, originalOrder, filterProducts]);

	// 是否正在加载中
	const isLoading = moreLoading || isRecursiveLoading;

	return (
		<>
			{!filteredAndSortedProducts?.edges.length && !isLoading && (
				<MyEmpty text={''} description={t('nav.Nogoods')} className="py-20 max-md:py-4">
					<i className="ri-shopping-cart-2-fill text-4xl !text-ddd"></i>
				</MyEmpty>
			)}

			{/* @ts-ignore */}
			<Masonry breakpointCols={breakpointColumns}
				className="my-masonry-grid"
				columnClassName="my-masonry-grid_column">
				{filteredAndSortedProducts?.edges.map((item) => (
					<ProductCard
						key={item.node.slug}
						productItem={item.node as ProductListItemFragment}
						locale={locale}
						showCompare={shouldShowCompare}
					/>
				))}
			</Masonry>

			{isLoading && (
				<div className="flex justify-center my-6">
					<Spin size="large" />
				</div>
			)}

			{/* 只在非特定slug页面显示"加载更多"按钮 */}
			{hasMore && !isLoading && !isAutoLoadPage && (
				<div className="mt-6 flex justify-center">
					<Button
						onClick={() => onLoadMore(after)}
						className="!rounded-sm bg-black hover:bg-opacity-80"
					>
						{t("common.loadmore")}
					</Button>
				</div>
			)}

			<div className="max-md:hidden">
				<CompareList />
			</div>
		</>
	);
};

export default ProductList;
