"use client";
import axios from "axios";
import { deleteCookie } from "cookies-next";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { toast } from "react-toastify";
import {useRouter  } from "@/navigation";


export function useCocartLogout() {
  const router = useRouter();
  const [isLogout, setIsLogout] = useState(false);
  const t = useTranslations();

  async function logout() {
    try {
      if (isLogout) return;
      setIsLogout(true);
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/cocart/v2/logout`
      );
      if (res.data) {
        deleteCookie("__user__login__info");
        deleteCookie("user-store");
        router.push("/");
        toast(t("message.ee500c73376d3c46b79a7316e3308880a6a5"), {
          type: "success"
        });
      }
    } catch (error: any) {
      toast(t("message.b03f242682e69f415a28cd1ae45825c4d8af"), { type: "error" });
    } finally {
      setIsLogout(false);
    }
  }

  return {
    logout
  };
}

