import { create } from "zustand";
import { persist } from "zustand/middleware";
import { UserApi } from "@/lib/@types/api/user";
import { cookieStoragePersistStorage } from "@/lib/utils/store";

type State = {
	userInfo: UserApi.UserInfo | null;
	setUserInfo: (userInfo: State["userInfo"]) => void;
};
export const useUserStore = create(
	persist<State>(
		(set, get) => ({
			userInfo: null,
			setUserInfo: (userInfo) => {
				set({ userInfo });
			},
		}),
		{
			name: "user-store",
			storage: cookieStoragePersistStorage(),
		},
	),
);
