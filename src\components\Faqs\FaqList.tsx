'use client'
import { useCategory } from "@/context/CategoriesContext.tsx";
import React, { useEffect, useState } from "react";
import FAQ from "@/components/Faqs/FaqCard.tsx";
import { useTranslations } from "next-intl";

const FaqList= ()=>{
	const { categories } = useCategory();

	const t=useTranslations()


	return <>
					<h2 className="text-4xl font-bold my-10">{t('common.FAQ')}</h2>
					<FAQ></FAQ>
	</>
}
export default FaqList
