import { create } from "zustand";
import { persist } from "zustand/middleware";

type State = {
  sortValue: string;
  setSortValue: (value: string) => void;
  originalOrder: Record<string, number>; // 保存原始顺序的索引
  setOriginalOrder: (products: any[]) => void;
  resetSortState: () => void; // 添加重置状态的方法
};

export const useProductSortStore = create(
  persist<State>(
    (set) => ({
      sortValue: 'default', // 默认排序
      setSortValue: (value) => set({ sortValue: value }),
      originalOrder: {}, // 保存原始顺序的索引
      setOriginalOrder: (products) => {
        // 为每个产品创建一个索引映射，用于恢复原始顺序
        const orderMap: Record<string, number> = {};
        products.forEach((product, index) => {
          if (product.node && product.node.id) {
            orderMap[product.node.id] = index;
          }
        });
        set({ originalOrder: orderMap });
      },
      resetSortState: () => set({
        sortValue: 'default',
        originalOrder: {}
      }),
    }),
    {
      name: "product-sort-store",
    }
  )
);

// 检查产品是否为featured（根据metadata中的custom/featured字段）
const isProductFeatured = (product: any): boolean => {
  if (!product?.node?.metadata || !Array.isArray(product.node.metadata)) {
    return false;
  }
  
  // 查找key为'custom/featured'的metadata项
  const featuredMeta = product.node.metadata.find(
    (meta: any) => meta.key === 'custom/featured'
  );
  
  // 如果找到并且value为"true"，则返回true
  return featuredMeta && featuredMeta.value === "true";
};

// 排序产品的工具函数
export const sortProducts = (products: any[], sortValue: string, originalOrder: Record<string, number> = {}) => {
  if (!products || !products.length) return [];
  
  const sortedProducts = [...products];
  
  switch (sortValue) {
    case 'name-asc':
      return sortedProducts.sort((a, b) => a.node.name.localeCompare(b.node.name));
    case 'name-desc':
      return sortedProducts.sort((a, b) => b.node.name.localeCompare(a.node.name));
    case 'price-asc':
      return sortedProducts.sort((a, b) => {
        const priceA = a.node.pricing?.priceRange?.start?.gross?.amount || 0;
        const priceB = b.node.pricing?.priceRange?.start?.gross?.amount || 0;
        return priceA - priceB;
      });
    case 'price-desc':
      return sortedProducts.sort((a, b) => {
        const priceA = a.node.pricing?.priceRange?.start?.gross?.amount || 0;
        const priceB = b.node.pricing?.priceRange?.start?.gross?.amount || 0;
        return priceB - priceA;
      });
    case 'best-selling':
      return sortedProducts.sort((a, b) => {
        // 获取rating，如果没有则默认为0
        const ratingA = a.node.rating || 0;
        const ratingB = b.node.rating || 0;
        // 按rating从高到低排序
        return ratingB - ratingA;
      });
    case 'featured':
      // 根据metadata中的custom/featured字段排序，true的排在前面
      return sortedProducts.sort((a, b) => {
        const aIsFeatured = isProductFeatured(a);
        const bIsFeatured = isProductFeatured(b);
        
        // 如果a是featured而b不是，a排在前面
        if (aIsFeatured && !bIsFeatured) return -1;
        // 如果b是featured而a不是，b排在前面
        if (!aIsFeatured && bIsFeatured) return 1;
        
        // 如果两者featured状态相同，则按评分排序
        const ratingA = a.node.rating || 0;
        const ratingB = b.node.rating || 0;
        return ratingB - ratingA;
      });
    case 'default':
    default:
      // 默认排序，恢复原始顺序
      if (Object.keys(originalOrder).length > 0) {
        return sortedProducts.sort((a, b) => {
          const indexA = originalOrder[a.node.id] !== undefined ? originalOrder[a.node.id] : Number.MAX_SAFE_INTEGER;
          const indexB = originalOrder[b.node.id] !== undefined ? originalOrder[b.node.id] : Number.MAX_SAFE_INTEGER;
          return indexA - indexB;
        });
      }
      // 如果没有原始顺序信息，则保持当前顺序
      return sortedProducts;
  }
}; 