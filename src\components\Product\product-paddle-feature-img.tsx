"use client";
import React, { useState, useRef } from "react";
import Image from "next/image";
import { Drawer } from "antd";
import {
	FeaturePoint,
	ProductFeatureConfig,
	defaultProductFeatures,
	productFeaturesMap,
} from "@/data/product-features";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useTranslations } from "next-intl";
// 组件属性类型
interface ProductPaddleFeatureImgProps {
	productKey?: string;
	customConfig?: ProductFeatureConfig;
	className?: string;
}

export default function ProductPaddleFeatureImg({
	productKey,
	customConfig,
	className = "",
}: ProductPaddleFeatureImgProps) {
	const [activePoint, setActivePoint] = useState<string | null>(null);
	const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
	const [selectedMobilePoint, setSelectedMobilePoint] = useState<FeaturePoint | null>(null);
	const imageRef = useRef<HTMLDivElement>(null);
	const t = useTranslations("productFeature");
	// 获取产品配置
	const config =
		customConfig || (productKey ? productFeaturesMap[productKey] : null) || defaultProductFeatures;

	// 如果没有配置数据，不渲染组件
	if (!config) {
		return null;
	}

	// 计算弹窗位置的函数 - 带边缘检测
	const getPopupPosition = (point: FeaturePoint) => {
		if (!imageRef.current) return { left: "0px", top: "0px", arrowPosition: "right" };

		const cardWidth = 320; // 弹窗宽度
		const cardHeight = 150; // 弹窗预估高度
		const margin = 16; // 最小边距
		const spacing = 30; // 弹窗与标注点的间距

		// 获取图片容器的尺寸
		const containerRect = imageRef.current.getBoundingClientRect();
		const containerWidth = containerRect.width;
		const containerHeight = containerRect.height;

		// 计算标注点的实际像素位置
		const pointPixelX = (point.x / 100) * containerWidth;
		const pointPixelY = (point.y / 100) * containerHeight;

		let left = 0;
		let top = pointPixelY;
		let arrowPosition = "right";

		// 水平位置检测
		const leftSidePosition = pointPixelX - cardWidth - spacing;
		const rightSidePosition = pointPixelX + spacing;

		if (leftSidePosition >= margin) {
			// 左侧有足够空间
			left = leftSidePosition;
			arrowPosition = "right";
		} else if (rightSidePosition + cardWidth <= containerWidth - margin) {
			// 右侧有足够空间
			left = rightSidePosition;
			arrowPosition = "left";
		} else {
			// 两侧都没有足够空间，选择较好的一侧
			if (pointPixelX > containerWidth / 2) {
				// 点在右侧，强制显示在左侧
				left = Math.max(margin, pointPixelX - cardWidth - spacing);
				arrowPosition = "right";
			} else {
				// 点在左侧，强制显示在右侧
				left = Math.min(containerWidth - cardWidth - margin, pointPixelX + spacing);
				arrowPosition = "left";
			}
		}

		// 垂直位置检测
		const topPosition = pointPixelY - cardHeight / 2;
		const bottomBoundary = containerHeight - margin;

		if (topPosition < margin) {
			// 上边界溢出，调整到最小边距
			top = margin;
		} else if (topPosition + cardHeight > bottomBoundary) {
			// 下边界溢出，调整到底部边距
			top = bottomBoundary - cardHeight;
		} else {
			// 垂直居中
			top = topPosition;
		}

		return {
			left: `${left}px`,
			top: `${top}px`,
			arrowPosition,
		};
	};

	// 处理标注点点击
	const handlePointClick = (pointId: string) => {
		const point = config.featurePoints.find((p: FeaturePoint) => p.id === pointId);
		if (!point) return;

		// 检查是否为移动端（屏幕宽度小于768px）
		const isMobile = window.innerWidth < 768;

		if (isMobile) {
			// 移动端：打开抽屉
			setSelectedMobilePoint(point);
			setMobileDrawerOpen(true);
		} else {
			// 桌面端：切换弹窗
			setActivePoint(activePoint === pointId ? null : pointId);
		}
	};

	return (
		<div className={`relative ${className}`}>
			{/* 添加简单的呼吸动画 */}
			<style jsx>{`
				@keyframes simple-breathe {
					0%,
					100% {
						transform: scale(1.2);
						opacity: 0.6;
					}
					50% {
						transform: scale(1.6);
						opacity: 0.2;
					}
				}
				.animate-simple-breathe {
					animation: simple-breathe 4s ease-in-out infinite;
				}
			`}</style>

			{/* 图片容器 - 使用 h-auto 保持图片比例 */}
			<div ref={imageRef} className="relative w-full">
				<SEOOptimizedImage
					src={config.imageSrc}
					alt={config.imageAlt}
					width={1920}
					height={800}
					className="h-auto w-full"
					priority
				/>

				{/* 标注点 - 使用响应式定位确保不偏移 */}
				{config.featurePoints.map((point: FeaturePoint) => (
					<div
						key={point.id}
						className="absolute z-20"
						style={{
							left: `${point.x}%`,
							top: `${point.y}%`,
							transform: "translate(-50%, -50%)", // 使用CSS transform确保居中
						}}
					>
						{/* 外圈透明呼吸动画 - 一个简单的圆圈放大缩小 */}
						<div className="animate-simple-breathe absolute inset-0 rounded-full bg-white/60"></div>

						{/* 标注点按钮 */}
						<button
							onClick={() => handlePointClick(point.id)}
							className={`
                                relative z-10 flex h-[20px] w-[20px] items-center
                                justify-center
                                rounded-full bg-white transition-all
                                duration-300 ease-in-out md:h-[44px] md:w-[44px]
                            `}
							aria-label={`View feature: ${point.title}`}
						>
							{/* 黑色加号/减号 */}
							<span className="text-sm  text-gray-800 transition-transform duration-200 md:text-xl">
								{activePoint === point.id ? "−" : "+"}
							</span>
						</button>
					</div>
				))}

				{/* 特性详情卡片 - 智能定位 */}
				{activePoint &&
					(() => {
						const selectedPoint = config.featurePoints.find((p: FeaturePoint) => p.id === activePoint);
						if (!selectedPoint) return null;

						const position = getPopupPosition(selectedPoint);

						return (
							<div
								className="absolute z-30"
								style={{
									left: position.left,
									top: position.top,
								}}
							>
								<div className="animate-in slide-in-from-left  w-80 max-w-xs transform bg-white/80 p-6 backdrop-blur-xl transition-all duration-300">
									{/* 内容 */}
									<div>
										{selectedPoint.title && (
											<h3 className="mb-2 text-lg font-bold text-gray-900">{t(selectedPoint.title)}</h3>
										)}
										{selectedPoint.description && (
											<p className="text-sm leading-relaxed text-gray-600">{t(selectedPoint.description)}</p>
										)}
									</div>

									{/* 指向标注点的箭头 - 根据位置调整方向 */}
									{/* {position.arrowPosition === 'right' && (
                                    <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2">
                                        <div className="w-0 h-0 border-l-8 border-l-white border-t-8 border-t-transparent border-b-8 border-b-transparent"></div>
                                    </div>
                                )}
                                {position.arrowPosition === 'left' && (
                                    <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2">
                                        <div className="w-0 h-0 border-r-8 border-r-white border-t-8 border-t-transparent border-b-8 border-b-transparent"></div>
                                    </div>
                                )} */}
								</div>
							</div>
						);
					})()}

				{/* 点击遮罩关闭弹窗 - 仅桌面端 */}
				{activePoint && (
					<div className="absolute inset-0 z-20 hidden md:block" onClick={() => setActivePoint(null)} />
				)}
			</div>

			{/* 移动端抽屉 */}
			<Drawer
				title={selectedMobilePoint?.title ? t(selectedMobilePoint.title) : ""}
				placement="bottom"
				onClose={() => setMobileDrawerOpen(false)}
				open={mobileDrawerOpen}
				height="auto"
				className="md:hidden"
				styles={{
					body: { padding: "20px" },
					header: {
						borderBottom: "1px solid #f0f0f0",
						fontSize: "18px",
						fontWeight: "bold",
					},
				}}
			>
				{selectedMobilePoint && (
					<div className="space-y-4">
                        {
                            selectedMobilePoint.description && <p className="text-base leading-relaxed text-gray-600">{t(selectedMobilePoint.description)}</p>
                        }
					</div>
				)}
			</Drawer>
		</div>
	);
}
