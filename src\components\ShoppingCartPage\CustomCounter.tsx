import React, { useState, useEffect } from 'react';

interface CustomCounterProps {
  count: number;
  onCountChange: (newCount: number) => void;
  disabled?: boolean;
  maxCount?: number;
  minCount?: number;
}

const CustomCounter: React.FC<CustomCounterProps> = ({
  count,
  onCountChange,
  disabled = false,
  maxCount = Infinity,
  minCount = 1
}) => {
  const [inputValue, setInputValue] = useState(count.toString());
  const [isEditing, setIsEditing] = useState(false);

  // 当外部count变化时，更新输入值
  useEffect(() => {
    if (!isEditing) {
      setInputValue(count.toString());
    }
  }, [count, isEditing]);

  const handleDecrease = () => {
    const newCount = Math.max(minCount, count - 1);
    onCountChange(newCount);
  };

  const handleIncrease = () => {
    const newCount = Math.min(maxCount, count + 1);
    onCountChange(newCount);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // 只允许数字输入
    if (/^\d*$/.test(value)) {
      setInputValue(value);
    }
  };

  const handleInputBlur = () => {
    setIsEditing(false);
    const numValue = parseInt(inputValue) || minCount;
    const clampedValue = Math.max(minCount, Math.min(maxCount, numValue));
    setInputValue(clampedValue.toString());
    if (clampedValue !== count) {
      onCountChange(clampedValue);
    }
  };

  const handleInputFocus = () => {
    setIsEditing(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleInputBlur();
    }
  };

  return (
    <div className="flex items-center bg-[#f6f6f6]">
      <button
        onClick={handleDecrease}
        disabled={disabled || count <= minCount}
        className="w-8 h-8 text-xl flex items-center justify-center text-black disabled:opacity-50 disabled:cursor-not-allowed"
      >
        −
      </button>
      <input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        onFocus={handleInputFocus}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        className="w-12 h-8 text-center text-sm font-medium text-gray-900 bg-transparent border-none outline-none disabled:opacity-50"
        min={minCount}
        max={maxCount}
      />
      <button
        onClick={handleIncrease}
        disabled={disabled || count >= maxCount}
        className="w-8 h-8 text-xl flex items-center justify-center text-black disabled:opacity-50 disabled:cursor-not-allowed"
      >
        +
      </button>
    </div>
  );
};

export default CustomCounter;
