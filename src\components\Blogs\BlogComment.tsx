'use client'
import { Avatar } from "antd";
import React, { useEffect, useState } from "react";
import { Buildings } from "@phosphor-icons/react";
import SumitCommentBtn from "@/components/Blogs/SumitCommentBtn.tsx";
import { getBlogComment } from "@/lib/api/blog.ts";

const BlogComment=({id}:{id:number})=>{
	const [list,setList]=useState([])
	const [page,setPage]=useState(1)
	const [loading,setLoading]=useState(false)
	const [count,setCount]=useState(0)
	const getComments=async()=>{
		try {
			setLoading(true)
			const res=await getBlogComment( page,  10,  id )
			if(res.code===200){
				setCount(res.detail.comment_count)
				if(page===1){
					setList(res.detail.ret)
				}else{
					const list1=list.concat(res.detail.ret)
					setList(list1)
				}
				if(list.length<count){
					setPage(page+1)
				}
			}
			setLoading(false)
		}catch (e){
			setLoading(false)
		}

	}
	useEffect(()=>{
		setPage(1)
		getComments()

	},[id])
   return <>
		 <ul>
			 {list&&list.map((item,index)=>{
				 return <li key={item.id} className={`${list.length===(index+1) ? 'border-b-white' :'border-b-gray-400'} border-b  py-4`}>
					 <div className="flex">
						 <Avatar size={35} className="object-cover object-top">{item.comment_user_first_name.substring(0, 1)}</Avatar>
						 <span className="font-bold text-lg px-4" >{item.comment_user_first_name }</span>
						 <div className="flex bg-[#eaedf2] p-1 items-center text-[#314c7f] text-xs rounded-sm">
							 <Buildings size={20} weight="fill" className="" />
							 <span >{item.comment_user_company}</span></div>
					 </div>
					 <p className="pl-10 py-4 text-[#314c7f]">{item.comment_content}</p>

				 </li>
			 })}
		 </ul>
		 <div className="text-center  py-8">
			 {(list.length<count||count===0)&&<span className="text-main hover:text-black  cursor-pointer" onClick={()=>getComments()}>Click to expand more</span>}
		 </div>
		 {/*提交评论*/}
<SumitCommentBtn blog_id={id}></SumitCommentBtn>
	 </>
}

export default BlogComment
