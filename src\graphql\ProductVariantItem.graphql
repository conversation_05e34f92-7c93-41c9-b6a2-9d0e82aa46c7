fragment ProductVariantItem on ProductVariant {
	quantityAvailable
	sku
	metadata {
		key
		value
	}
	weight {
		unit
		value
	}
	attributes {
		attribute {
			name
			translation(languageCode: $locale) {
				name
			}
		}
		values {
			inputType
			file {
				url
				contentType
			}
			name
			translation(languageCode: $locale) {
				name
			}
		}
	}
	product {
		id
		name
		translation(languageCode: $locale) {
			name
		}
		metadata {
			key
			value
		}
		slug
		category {
			translation(languageCode: $locale) {
				name
			}
			name
		}
	}
	pricing {
		price {
			tax {
				amount
				currency
			}
			net {
				amount
				currency
			}
			gross {
				amount
				currency
			}
		}
	}
	translation(languageCode: $locale) {
		name
	}
	name
	id
}
