'use client'
import React from 'react';
import { type Blog } from '@/lib/@types/api/blog';
import BlogItemDefault from "@/components/Blogs/BlogItemDefault";
import clsx from 'clsx';
import MyEmpty from '../MyEmpty';
import { useTranslations } from 'next-intl';
interface BlogListProps {
	blogList: Blog.BlogListItem[];
}

const BlogList: React.FC<BlogListProps> = ({ blogList }) => {
  const t=useTranslations()
  return blogList.length > 0 ? (
    <div
      className="list-blog grid gap-x-5 gap-y-5  grid-cols-2   max-md:grid-cols-1 "
    >
      {blogList.map((item, index) => {

        return (
          <div
            key={index}
            className='w-full'

          >
            <BlogItemDefault news={item} />
          </div>
        );
      })}
    </div>
  ) : (
    <div className="w-full h-full flex justify-center items-center">
<MyEmpty link={'/blog'} text={''} description={t('nav.Noblog')} bottomText={t('nav.Return_to_blog')}  className="py-20 max-md:py-4">
                  <i className="ri-draft-line text-4xl  !text-ddd"></i>
                </MyEmpty>
    </div>
  );
};



export default BlogList;
