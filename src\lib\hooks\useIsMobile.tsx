import { useState, useEffect } from "react";

/**
 * 是否是手机端
 * Custom hook to determine if the current device is mobile
 * @returns {boolean} - `true` if the device is mobile, `false` otherwise
 */
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768); // Define mobile as width <= 768px
    };

    // Initial check
    handleResize();

    // Listen to window resize
    window.addEventListener("resize", handleResize);

    // Cleanup listener
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return isMobile;
};

export default useIsMobile;
