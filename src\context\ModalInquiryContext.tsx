'use client'

import React, { createContext, useContext, useState, type ReactNode } from 'react';

interface ModalInquiryContextProps {
	children: ReactNode;
}

interface ModalInquiryContextValue {
	isModalOpen: boolean;
	openModalInquiry: (is3dValue) => void;
	closeModalInquiry: () => void;
	changePreloaded3DObj: (obj: Record<string, any>) => void;
	Preloaded3DObj: Record<string, any>;
	is3d: boolean;
	setIs3d: (value: boolean) => void;
	VariantActive3DName: string;
	setVariantActive3DName: (value: string) => void;
}

const ModalInquiryContext = createContext<ModalInquiryContextValue | undefined>(undefined);

export const useModalInquiryContext = (): ModalInquiryContextValue => {
	const context = useContext(ModalInquiryContext);
	if (!context) {
		throw new Error('useModalInquiryContext must be used within a ModalSearchProvider');
	}
	return context;
};

export const ModalInquiryProvider: React.FC<ModalInquiryContextProps> = ({ children }) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [Preloaded3DObj, setPreloaded3DObj] = useState<Record<string, any>>({});
	const [is3d, setIs3d] = useState(false);
	const [VariantActive3DName, setVariantActive3DName] = useState<string>('');

	const openModalInquiry = (is3dValue = false) => {
		setIsModalOpen(true);
		setIs3d(is3dValue);
	};

	const closeModalInquiry = () => {
		setIsModalOpen(false);
		setIs3d(false);
	};

	const changePreloaded3DObj = (obj: Record<string, any>): void => {
		setPreloaded3DObj(obj);
	};

	const contextValue: ModalInquiryContextValue = {
		isModalOpen,
		openModalInquiry,
		closeModalInquiry,
		changePreloaded3DObj,
		Preloaded3DObj,
		is3d,
		setIs3d,
		VariantActive3DName,
		setVariantActive3DName
	};

	return (
		<ModalInquiryContext.Provider value={contextValue}>
			{children}
		</ModalInquiryContext.Provider>
	);
};
