"use client";
import React, { useEffect, useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import Price from "@/components/Price/price";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import CartPageItem from "./CartPageItem";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { useRouter } from "@/navigation";
import { useUserStore } from "@/lib/store/user";
import MyEmpty from "@/components/MyEmpty";
import { Button } from "@/components/Button";
import { Link } from "@/navigation";
import { defaultLocale } from "@/config";
import Partner from "@/components/ZC/Partner";
const LSVG = () => {
	return (
		<svg
			viewBox="0 0 1024 1024"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			p-id="1463"
			width="20"
			height="20"
		>
			<path
				d="M51.2 512l204.8-153.6v102.4h716.8v102.4H256v102.4l-204.8-153.6z"
				fill="#000000"
				p-id="1464"
			></path>
		</svg>
	);
};
export default function ShoppingCartPage() {
	const { cartList, setisCart } = useShoppingCartStore();
	let router = useRouter();
	const { userInfo } = useUserStore();
	const t = useTranslations();
	const locale = useLocale();
	const [itemCount, setItemCount] = useState<number>(0);
	const { removeCartItem, updateLinesCount } = useShoppingCart();

	// 删除一行购物车
	function handelupdateLinesCount(id: string) {
		removeCartItem([id]);
	}

	async function handelupdateCount(id: string, count: number): Promise<void> {
		try {
			await updateLinesCount({
				checkoutId: cartList.id,
				lines: [
					{
						lineId: id,
						quantity: count,
					},
				],
			});
		} catch (error) {
			console.error("Update cart item failed:", error);
			throw error;
		}
	}

	useEffect(() => {
		// 更新购物车数量
		setItemCount(cartList?.lines?.length || 0);
	}, [cartList?.lines?.length]);

	function tocheckout() {
		setisCart(true);
		router.push("/checkout");
	}

	// 继续购买功能
	const handleContinueShopping = () => {
		// 尝试返回上一页
		if (typeof window !== "undefined" && window.history.length > 1) {
			router.back();
		} else {
			// 如果没有历史记录，跳转到首页
			router.push("/");
		}
	};

	return (
		<>
			<div className="min-h-screen ">
				{/* 面包屑导航 */}
				<div className="border-b border-t border-gray-200 py-4">
					<nav className="container flex items-center space-x-2 text-sm">
						<Link href="/" className="text-gray-600 transition-colors hover:text-black">
							{t("menu.Home")}
						</Link>
						<span className="text-gray-400">/</span>
						<span className="text-black">{t("shop.0b1dcf5c5c3d9b4fe53b771d965840f7159e")}</span>
					</nav>
				</div>
				<div className="container py-16">
					{/* 页面标题 */}
					<div className="mb-6">
						<div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between md:items-center">
							<div className="flex-1">
								<h1
									className={`${
										locale === defaultLocale ? "ib" : "font-semibold"
									} text-5xl text-black max-md:text-2xl`}
								>
									{t("common.yourShoppingCart")}
								</h1>
								{/* {cartList?.lines?.length > 0 && (
								<p className="mt-2 text-gray-500">
									<span>({itemCount} </span>
									<span>{t("shop.items")})</span>
								</p>
							)} */}
							</div>

							{/* 继续购买按钮 */}
							<div className="mt-2 flex-shrink-0 sm:mt-0">
								<button
									onClick={handleContinueShopping}
									className="inline-flex w-full items-center justify-center border border-black bg-white px-4 py-2  text-sm font-medium text-black transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 sm:w-auto"
								>
									<span className="mr-2">
										<LSVG />
									</span>
									<span className="text-black">{t("common.ContinueShopping")}</span>
								</button>
							</div>
						</div>
					</div>

					{cartList && cartList?.lines?.length > 0 ? (
						<div className="space-y-6">
							{/* 购物车商品列表 */}
							<div className="border border-l-0 border-r-0 border-gray-200 bg-white">
								{/* 表头 - 只在桌面端显示 */}
								<div className="hidden gap-4 border-b border-gray-200 px-6 py-4 text-sm font-medium uppercase tracking-wider text-black xl:grid xl:grid-cols-12">
									<div className="col-span-6 uppercase">{t("checkout.product")}</div>
									<div className="col-span-2 text-center uppercase">{t("checkout.price")}</div>
									<div className="col-span-2 text-center uppercase">{t("checkout.quantity")}</div>
									<div className="col-span-2 text-center uppercase">{t("checkout.total")}</div>
								</div>

								{/* 商品列表 */}
								<div className="divide-y divide-gray-200">
									{cartList?.lines.map((product, index) => {
										return (
											<CartPageItem
												key={index}
												product={product}
												count={product.quantity}
												handelupdateLinesCount={handelupdateLinesCount}
												handelupdateCount={handelupdateCount}
											/>
										);
									})}
								</div>
							</div>

							{/* 订单摘要 - 放在表格下面 */}
							<div className="flex justify-end">
								<div className="w-full max-w-[280px] space-y-4">
									<div className="flex items-center justify-between text-3xl text-black">
										<span>{t("checkout.sb")}</span>
										<Price price={cartList?.totalPrice?.gross?.amount} className="text-3xl" />
									</div>
									<div className="text-sm text-black">{t("checkout.sh")}</div>

									<div className="mt-6">
										<Link
											href="/checkout"
											className="block w-full bg-[#83c000] py-3 text-center text-2xl text-white transition-colors  hover:bg-opacity-80 hover:text-white"
										>
											{t("checkout.Checkout")}
										</Link>
									</div>
								</div>
							</div>
						</div>
					) : (
						<div className=" bg-white p-12 text-center">
							<MyEmpty text={""} description={t("nav.Nogoods")} className="py-20">
								<i className="ri-shopping-cart-2-fill mb-4 text-6xl text-gray-300"></i>
							</MyEmpty>
							{/* <div className="mt-8">
							<Link
								href="/"
								className="inline-block bg-black text-white px-8 py-3 rounded-md hover:bg-gray-800 transition-colors"
							>
								{t("common.StartShopping")}
							</Link>
						</div> */}
						</div>
					)}
				</div>
			</div>
			<Partner />
		</>
	);
}
