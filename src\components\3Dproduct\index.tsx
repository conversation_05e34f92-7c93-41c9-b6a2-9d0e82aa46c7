"use client";
import React, { startTransition, Suspense, useEffect, useMemo, useState } from "react";
import LoadNormalModal, { PreviewObject } from "../3DNormalModal/customIndex";
import { defaultLocale } from "@/config";
import { useLocale, useTranslations } from "next-intl";
import { Collapse, Image as AntImage } from "antd";
import { SketchOutlined, ZoomInOutlined, EyeOutlined } from "@ant-design/icons";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";

function Index({ product }) {
	// 新增两个状态用于控制预览弹窗
	const [previewUrl, setPreviewUrl] = useState<string | null>(null);
	const [tdEdit, _] = useState(() => {
		try {
			const parsedData = JSON.parse(product.threeModeCustoms || "[]") as any[];
			return parsedData
				.filter((item) => item.enable)
				.map((item) => ({
					...item,
				}));
		} catch (error) {
			console.error("解析 threeModeCustoms 失败:", error);
			return [];
		}
	}) as any;
	const { openModalInquiry, changePreloaded3DObj, VariantActive3DName } = useModalInquiryContext();
	const [previewConfig, setPreviewConfig] = useState<PreviewObject[]>([]);
	const [loading, setLoading] = useState(true);
	useEffect(() => {
		if (tdEdit.length > 0) {
			const initialConfig = tdEdit
				.filter((item) => item.enable)
				.map((item) => ({
					meshName: item.name,
					rename: item.rename,
					materialBaseImg: "",
					materialNormalImg: "",
					materialRoughnessImg: "",
					materialMetalnessImg: "",
					materialAoImg: "",
					patternImage: "",
					color: "",
				}));
			setPreviewConfig(initialConfig);
		}
	}, [tdEdit]);
	let locale = useLocale();
	const t = useTranslations();
	const [preloaded3D, setPreloaded3D] = useState(false);
	const threeModel = useMemo<any>(() => {
		const json = product.threeModel;
		return json ? JSON.parse(json)[0] : null;
	}, [product]);

	useEffect(() => {
		if (product) {
			if (product.metadata) {
				const threeModelJson = product.metadata.find((item) => item.key === "threeModel");
				if (threeModelJson) {
					const threeModel = JSON.parse(threeModelJson.value)[0];
					if (threeModel) {
						setLoading(true);
						// 先进行数据获取
						fetch(process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + `/medias/file_download/` + threeModel.id)
							.then((response) => response.arrayBuffer())
							.then(() => {
								if (tdEdit.length > 0) {
									// 使用 startTransition 包装状态更新
									startTransition(() => {
										setPreloaded3D(true);
										setLoading(false);
									});
								}
							})
							.catch((error) => {
								console.error("Error preloading 3D model:", error);
								setLoading(false);
							});
					}
				}
			}
		}
	}, [product]);

	const [IsRotate, setIsRotate] = useState(false);

	function OpenModel() {
		let newarr = [
			...previewConfig.map((item) => {
				return {
					color: item.color,
					materialBaseImg: item.materialBaseImg,
					patternImage: item.patternImage,
					meshName: item.meshName,
					rename: item.rename,
				};
			}),
		];
		let obj = {
			inquiryData: [
				{
					quantity: 1,
					variant: {
						name: VariantActive3DName,
						product: {
							name: product.name,
							id: product.id,
							slug: product.slug,
							media: product.media,
							metadata: [{ key: "media", value: product.media }],
						},
					},
				},
			],

			Textures: newarr,
		};

		changePreloaded3DObj(obj);
		openModalInquiry(true);
	}

	return (
		<>
			{tdEdit.length>0 && (
				<div className="TDproduct">
					<h2 className="mb-6  box-border text-[26px] font-medium text-black max-md:px-2 max-md:text-2xl md:leading-10">
						{t("product.3DCustomization")}
					</h2>
					<div className="container1730 grid h-[50vh] grid-cols-3  max-md:h-auto  max-md:grid-cols-1">
						<div className="relative col-span-2">
							{loading ? (
								<div className="flex h-full w-full items-center justify-center">
									<Loading3DModel text={t("product.Loading3DModel")} />
								</div>
							) : (
								<div className={`h-full w-full rounded-none  max-md:h-[50vh] `}>
									{threeModel && (
										<Suspense
											fallback={
												<div className="flex h-full w-full items-center justify-center">
													<Loading3DModel text={t("product.Loading3DModel")} />
												</div>
											}
										>
											<LoadNormalModal
												modalUrl={
													process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + `/medias/file_download/` + threeModel.id
												}
												className="h-full w-full"
												fileName={threeModel.name}
												metalness={threeModel.metalness}
												color={threeModel.color}
												roughness={threeModel.roughness}
												preloaded={preloaded3D}
												// @ts-ignore
												IsRotate={IsRotate}
												//背景颜色
												backgroundColor={threeModel.backgroundColor}
												preViewObj={previewConfig}
											/>
										</Suspense>
									)}
								</div>
							)}

							<div className="absolute bottom-1 left-1 ">
								<div className="flex gap-x-2">
									{!IsRotate ? (
										<span className="cursor-pointer" onClick={() => setIsRotate(!IsRotate)}>
											<GetHovered />
										</span>
									) : (
										<span className="rotating-span cursor-pointer" onClick={() => setIsRotate(!IsRotate)}>
											<GetHovered />
										</span>
									)}
								</div>
							</div>
						</div>

						{/* 右侧 */}
						<div className="Executive col-span-1  box-border overflow-y-auto p-3">
							{/* 1 */}
							<Collapse
								accordion
								className="custom-collapse"
								expandIcon={({ isActive }) => (
									<div className="flex items-center justify-center  rounded-full ">
										<i
											className={` text-xl transition-transform ${
												isActive ? "ri-subtract-line" : "ri-add-line"
											}`}
										/>
									</div>
								)}
								style={{
									background: "white",
								}}
								items={tdEdit
									.filter((item) => item.enable)
									.map((item) => {
										const currentPreview: PreviewObject = previewConfig.find(
											(cfg) => cfg.meshName === item.name,
										) || {
											meshName: item.name,
											materialBaseImg: item.materials?.[0]?.baseColor?.[0]?.url,
											materialNormalImg: item.materials?.[0]?.normalMap?.[0]?.url,
											materialRoughnessImg: item.materials?.[0]?.roughnessMap?.[0]?.url,
											materialMetalnessImg: item.materials?.[0]?.metalnessMap?.[0]?.url,
											materialAoImg: item.materials?.[0]?.aoMap?.[0]?.url,
											patternImage: item.textures?.[0]?.url,
											color: item.colors?.[0],
										};
										return {
											key: item.name,
											label: item.rename || item.name,
											children: (
												<div className="space-y-4">
													{/* 材质预览 */}
													{item.materials && item.materials.length > 0 && (
														<div>
															<div className="mb-2 text-gray-500">{t("product.MaterialsList")}</div>
															<div className="flex flex-wrap gap-2">
																<div
																	className={`h-20 w-20 cursor-pointer overflow-hidden rounded border transition-all ${
																		!currentPreview.materialBaseImg
																			? "border-blue-500 ring-2 ring-blue-500"
																			: ""
																	}`}
																	onClick={() => {
																		setPreviewConfig((prev) =>
																			prev.map((cfg) =>
																				cfg.meshName === item.name
																					? {
																							...cfg,
																							materialBaseImg: undefined,
																							materialNormalImg: undefined,
																							materialRoughnessImg: undefined,
																							materialMetalnessImg: undefined,
																							materialAoImg: undefined,
																						}
																					: cfg,
																			),
																		);
																	}}
																>
																	<div className="flex h-full w-full items-center justify-center bg-gray-100">
																		<i className="ri-close-line text-xl text-gray-400" />
																	</div>
																</div>
																{item.materials.map(
																	(material, index) =>
																		material.baseColor &&
																		material.baseColor.map(
																			(baseColor: { url: string; name: string }, colorIndex: number) => (
																				<div
																					key={`${index}-${colorIndex}`}
																					className={`h-25 relative flex w-20 cursor-pointer flex-col items-center justify-center overflow-hidden rounded border transition-all ${
																						currentPreview.materialBaseImg === baseColor.url
																							? "border-blue-500 ring-2 ring-blue-500"
																							: ""
																					}`}
																					onClick={() => {
																						setPreviewConfig((prev) =>
																							prev.map((cfg) =>
																								cfg.meshName === item.name
																									? {
																											...cfg,
																											materialBaseImg: baseColor.url,
																											materialNormalImg: material.normalMap?.[0]?.url,
																											materialRoughnessImg: material.roughnessMap?.[0]?.url,
																											materialMetalnessImg: material.metalnessMap?.[0]?.url,
																											materialAoImg: material.aoMap?.[0]?.url,
																										}
																									: cfg,
																							),
																						);
																					}}
																				>
																					<img
																						src={baseColor.url}
																						alt={baseColor.name}
																						className="h-20 w-full object-cover"
																					/>
																					<p className="text-center  text-xs ">{material.name}</p>
																					<div
																						className="absolute right-1 top-1 z-10 flex h-7 w-7 cursor-pointer items-center justify-center rounded-full bg-white/80 shadow-sm hover:bg-white"
																						onClick={(e) => {
																							e.stopPropagation();
																							setPreviewUrl(baseColor.url);
																						}}
																					>
																						<EyeOutlined style={{ fontSize: 18, color: "#333" }} />
																					</div>
																				</div>
																			),
																		),
																)}
															</div>
														</div>
													)}
													{/* 纹理预览 */}
													{item.textures && item.textures.length > 0 && (
														<div>
															<div className="mb-2 text-gray-500 ">{t("product.TexturesList")}</div>
															<div className="flex flex-wrap gap-2">
																<div
																	className={`relative  h-20 w-20 cursor-pointer overflow-hidden rounded border transition-all ${
																		!currentPreview.patternImage ? "border-blue-500 ring-2 ring-blue-500" : ""
																	}`}
																	onClick={() => {
																		setPreviewConfig((prev) =>
																			prev.map((cfg) =>
																				cfg.meshName === item.name
																					? { ...cfg, patternImage: undefined }
																					: cfg,
																			),
																		);
																	}}
																>
																	<div className="flex h-full w-full items-center justify-center bg-gray-100">
																		<i className="ri-close-line text-xl text-gray-400" />
																	</div>
																</div>
																{item.textures.map((texture, index) => (
																	<div
																		key={index}
																		className={`h-20 w-20 cursor-pointer overflow-hidden rounded border transition-all ${
																			currentPreview.patternImage === texture.url
																				? "border-blue-500 ring-2 ring-blue-500"
																				: ""
																		}`}
																		onClick={() => {
																			setPreviewConfig((prev) =>
																				prev.map((cfg) =>
																					cfg.meshName === item.name
																						? { ...cfg, patternImage: texture.url }
																						: cfg,
																				),
																			);
																		}}
																	>
																		<img
																			src={texture.url}
																			alt={texture.name}
																			className="h-full w-full object-cover"
																		/>
																		<div
																			className="absolute right-1 top-1 z-10 flex h-7 w-7 cursor-pointer items-center justify-center rounded-full bg-white/80 shadow-sm hover:bg-white"
																			onClick={(e) => {
																				e.stopPropagation();
																				setPreviewUrl(texture.url);
																			}}
																		>
																			<EyeOutlined style={{ fontSize: 18, color: "#333" }} />
																		</div>
																	</div>
																))}
															</div>
														</div>
													)}
													{/* 颜色预览 */}
													{item.colors && item.colors.length > 0 && (
														<div>
															<div className="mb-2 text-gray-500">{t("product.ColorsList")}</div>
															<div className="flex flex-wrap gap-2">
																<div
																	className={`flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-gray-200 ${
																		!currentPreview.color ? "border-blue-500 ring-2 ring-blue-500" : ""
																	}`}
																	onClick={() => {
																		setPreviewConfig((prev) =>
																			prev.map((cfg) =>
																				cfg.meshName === item.name ? { ...cfg, color: undefined } : cfg,
																			),
																		);
																	}}
																>
																	<i className="ri-close-line text-gray-400" />
																</div>
																{item.colors.map((color, index) => (
																	<div
																		key={index}
																		className={`h-8 w-8 cursor-pointer rounded-full border border-gray-200 ${
																			currentPreview.color === color
																				? "border-blue-500 ring-2 ring-blue-500"
																				: ""
																		}`}
																		style={{ backgroundColor: color }}
																		onClick={() => {
																			setPreviewConfig((prev) =>
																				prev.map((cfg) =>
																					cfg.meshName === item.name ? { ...cfg, color } : cfg,
																				),
																			);
																		}}
																	/>
																))}
															</div>
														</div>
													)}
												</div>
											),
										};
									})}
							/>

							{/* Sticky Inquiry Button */}
							<div className="sticky bottom-0 bg-white pt-5">
								<div
									onClick={OpenModel}
									className="!font-poppins Executive !flex-1 cursor-pointer !rounded-sm border-[1px] border-black !bg-[#fafafa] !px-3 !py-3 text-center !text-black hover:!bg-black hover:!text-white"
								>
									<SketchOutlined className="text-lg" /> {t("product.Customized inquiry")}
								</div>
							</div>
						</div>
					</div>
					{previewUrl && (
						<AntImage
							style={{ display: "none" }}
							preview={{
								visible: true,
								src: previewUrl,
								onVisibleChange: (vis) => (vis ? null : setPreviewUrl(null)),
							}}
							src={previewUrl}
							alt="preview"
						/>
					)}
				</div>
			)}
		</>
	);
}

export default React.memo(Index);

function GetHovered() {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 38 38">
			<path fill="none" d="M0 0H38V38H0z" />
			<g>
				<path
					fill="#fff"
					stroke="rgba(0,0,0,0)"
					strokeMiterlimit="10"
					d="M14.483 28.411a11.562 11.562 0 0 0 2.547-.575l.481 1.384a13.245 13.245 0 0 1-2.869.649zm-5.644.8l.482-1.382a11.562 11.562 0 0 0 2.547.575l-.168 1.458a12.867 12.867 0 0 1-2.861-.65zm10.552-2.506a12.155 12.155 0 0 0 2.046-1.619l1.03 1.035a13.131 13.131 0 0 1-2.3 1.828zm-15.506-.593l1.036-1.044a11.038 11.038 0 0 0 2.046 1.625l-.782 1.244a13.086 13.086 0 0 1-2.3-1.825zm19.2-3.063a12.27 12.27 0 0 0 1.15-2.34l1.377.495a13.612 13.612 0 0 1-1.3 2.634zm-22.33-1.88l1.384-.486a11.409 11.409 0 0 0 1.143 2.345l-1.237.789a12.951 12.951 0 0 1-1.29-2.648zM0 16.756h1.464a11.631 11.631 0 0 0 .08 1.379l-1.458.174A13.214 13.214 0 0 1 0 16.756zm0 0A13.169 13.169 0 0 1 20.675 5.918l-1-5.663L21.116 0l1.538 8.667-8.753.9-.147-1.45 6.533-.675a11.715 11.715 0 0 0-18.823 9.314z"
					transform="translate(6.062 2.799)"
				/>
			</g>
		</svg>
	);
}

export function Loading3DModel({ text }: { text: string }) {
	return (
		<div className="flex flex-col items-center justify-center">
			<div className="loading-3d">
				<div className="circle"></div>
				<div className="circle"></div>
				<div className="circle"></div>
			</div>
			<div className="mt-4 text-lg font-medium text-gray-600">{text}</div>
			<style jsx>{`
				.loading-3d {
					position: relative;
					width: 80px;
					height: 80px;
					transform-style: preserve-3d;
					animation: rotate3d 2s infinite linear;
				}
				.circle {
					position: absolute;
					width: 100%;
					height: 100%;
					border: 4px solid #000;
					border-radius: 50%;
					opacity: 0.6;
				}
				.circle:nth-child(1) {
					transform: rotateX(0deg);
				}
				.circle:nth-child(2) {
					transform: rotateX(60deg);
				}
				.circle:nth-child(3) {
					transform: rotateX(120deg);
				}
				@keyframes rotate3d {
					0% {
						transform: rotateX(0deg) rotateY(0deg);
					}
					100% {
						transform: rotateX(360deg) rotateY(360deg);
					}
				}
			`}</style>
		</div>
	);
}
