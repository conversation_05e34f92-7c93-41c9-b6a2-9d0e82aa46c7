"use client";
import clsx from 'clsx';
import React, { useEffect, useState } from 'react';
import Mobileindex from '../Translate/Mobileindex';
import { useUserStore } from '@/store/user.store';
import { useRouter } from "@/navigation";
import { useTranslations } from 'next-intl';
import { deleteCookie } from "cookies-next";
import { useUserAuth } from '@/lib/hooks/useUserAuth';
import { message } from 'antd';

function Index({handleMenuMobile}) {
  const t = useTranslations();
  const { userInfo, getUserInfo } = useUserStore();
  const [mounted, setMounted] = useState(false);
  const { useSignOut } = useUserAuth();
  const router=useRouter()
  useEffect(() => {
    setMounted(true);
    getUserInfo();
  }, []);

  const jumpCenter = () => {
    if (userInfo) {
      deleteCookie("__user__login__info");
      deleteCookie("user-store");
      useSignOut();
      router.push("/");
      handleMenuMobile();
      message.success(t('message.logout successful'));
    } else {
      handleMenuMobile();
      const login = document.querySelector("#web-login") as HTMLLIElement;
      login.click();
    }
  };

  // 在客户端渲染之前不显示文本内容
  if (!mounted) {
    return (
      <div className='Mobilefooter w-full h-50px absolute left-0 bottom-0 px-3'>
        <div className='border-t-[1px] border-[#ececec] py-6 flex'>
          <button className="flex items-center px-4 py-2 hover:bg-gray-300 border-[1px] border-[#ececec] rounded-full">
            <i className={clsx("ri-user-line text-2xl")}></i>
            <span className="mx-2"></span>
          </button>
          <Mobileindex />
        </div>
      </div>
    );
  }

  return (
    <div className='Mobilefooter w-full'>
      <div className='border-t-[1px] border-[#ececec] py-6 flex'>
        <button onClick={jumpCenter} className="flex items-center px-4 py-2 hover:bg-gray-300 border-[1px] border-[#ececec] rounded-full">
          <i className={clsx("ri-user-line text-2xl")}></i>
          <span className="mx-2">{userInfo ? t("common.logout") : t("common.login")}</span>
        </button>
        <Mobileindex />
      </div>
    </div>
  );
}

export default React.memo(Index);