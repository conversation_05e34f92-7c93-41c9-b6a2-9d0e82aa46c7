"use client";
import React, { useEffect, useState } from "react";
import { Link } from "@/navigation";
import { useTranslations } from "next-intl";
import { defaultLocale } from "@/config";
import { ProductListItemFragment } from "@/gql/graphql";
import { useLoveStore } from "@/lib/store/love.store";
import clsx from "clsx";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css";
import { productTranslationName } from "@/lib/utils/util";
import { useCompareStore } from "@/lib/store/Compare.store";
import Image from "next/image";

// Helper functions (assuming these are defined elsewhere or above)
export type ImgObj = { id?: number; url: string; name: string; type: string };
export type Metadata = { __typename?: "MetadataItem" | undefined; key: string; value: string };
export const filterCateImg = (metadataList: Metadata[]) => {
	let imgUrlList: ImgObj[] = [];
	if (metadataList && metadataList.length > 0) {
		const media = metadataList.filter((i: Metadata) => i.key === "media");
		if (media.length > 0) imgUrlList = JSON.parse(media[0].value) as ImgObj[];
	}
	return imgUrlList;
};
type dscObj = {
	data: { text: string };
	type: string;
};
type dscList = {
	blocks: dscObj[];
};

export const filterSortDsc = (dsc: string) => {
	let sortDsc = "";
	let longDsc = "";
	if (dsc) {
		const dscObj = JSON.parse(dsc) as dscList;

		if (dscObj && dscObj.blocks.length > 0) {
			sortDsc = dscObj.blocks[1].data.text;
			longDsc = dscObj.blocks[0].data.text;
		}
	}

	return { sortDsc, longDsc };
};
export default function ProductCard({
	productItem,
	locale,
	showCompare = false,
}: {
	productItem: ProductListItemFragment;
	locale: string;
	showCompare?: boolean;
}) {
	const [isOpen, setIsOpen] = useState(false);
	const [imageUrl, setImageUrl] = useState("");
	const { compareIds, setcCmpareProducts, changeShow } = useCompareStore();
	const t = useTranslations();
	// console.log(productItem,'productItem======');
	const pt = productItem.media;
	const imgs: ImgObj[] = pt ? JSON.parse(pt) as ImgObj[] : [];
	const [imageLoading, setImageLoading] = useState(true);

    useEffect(() => {
        setImageLoading(true);
    }, [productItem.id]);

	const addOrDelCompare = (event: React.MouseEvent | React.ChangeEvent<HTMLInputElement>, product) => {
		event.stopPropagation();
		const id = product?.id;
		if (id) {
			// 如果产品已经在对比列表中，允许移除
			// 如果产品不在对比列表中，只有在对比列表未满（少于2个）时才允许添加
			if (compareIds.includes(id) || compareIds.length < 2) {
				setcCmpareProducts(id);
				changeShow(true);
			}
		}
	};

	let ProductName =
		locale === defaultLocale
			? productTranslationName(productItem.name)
			: productTranslationName(productItem.translation)
			? productTranslationName(productItem.translation.name)
			: productTranslationName(productItem.name);

	return (
		<div className="group relative flex flex-col overflow-hidden bg-white transition-all duration-500">
			<div className="relative overflow-hidden">
				<Link href={"/product/" + productItem.slug} className="block aspect-square 2xl:aspect-[12/9]">
					{/* Base Image */}
					<Image
						width={800}
						height={900}
						quality={100}
						alt={ProductName}
						className={`h-full w-full object-contain transition-all duration-500 ease-in-out ${imgs.length > 1 ? 'group-hover:opacity-0' : ''}`}
						src={imgs[0]?.url || "/image/default-image.webp"}
						onLoad={() => setImageLoading(false)}
						onError={() => setImageLoading(false)}
					/>
					{/* Hover Image */}
					{imgs.length > 1 && (
						<Image
							width={800}
							height={900}
							quality={100}
							alt={ProductName}
							className="absolute inset-0 h-full w-full object-contain opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100"
							src={imgs[1].url}
							aria-hidden="true"
						/>
					)}

					{/* Spinner Overlay */}
					{imageLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm">
                            <div className="h-8 w-8 animate-spin rounded-full border-4 border-solid border-gray-300 border-t-gray-600"></div>
                        </div>
                    )}
				</Link>
			</div>

			<div className="flex flex-1 flex-col gap-1 p-5 max-md:gap-1 max-md:p-3">
				<div className="flex items-start justify-between gap-3">
					<Link
						href={"/product/" + productItem.slug}
						className="line-clamp-1 flex-1 text-sm !text-black transition-colors duration-300 group-hover:text-black max-md:line-clamp-2 max-md:!text-sm irs font-semibold"
					>
						{ProductName}
					</Link>
				</div>
				{renderPrice(productItem.pricing?.priceRange)}
				{showCompare && (
					<div className="mt-2 flex items-center">
						<div
							className={clsx(
								"relative h-4 w-4 rounded-full border-2 cursor-pointer transition-all duration-200",
								compareIds.includes(productItem.id)
									? "bg-black"
									: "border-[#e4e4e4] bg-white hover:border-gray-400",
								!compareIds.includes(productItem.id) && compareIds.length >= 2 && "opacity-50 cursor-not-allowed"
							)}
							onClick={(e) => {
								e.stopPropagation();
								if (compareIds.includes(productItem.id) || compareIds.length < 2) {
									addOrDelCompare(e, productItem);
								}
							}}
						>
							{/* 隐藏的checkbox用于表单处理 */}
							<input
								type="checkbox"
								id={`compare-${productItem.id}`}
								className="sr-only"
								checked={compareIds.includes(productItem.id)}
								onChange={(e) => {
									// 只有在允许的情况下才处理change事件
									if (compareIds.includes(productItem.id) || compareIds.length < 2) {
										addOrDelCompare(e, productItem);
									}
								}}
								disabled={!compareIds.includes(productItem.id) && compareIds.length >= 2}
							/>

						</div>
						<label
							htmlFor={`compare-${productItem.id}`}
							className={clsx(
								"ml-2 text-sm text-[#555555] irs",
								!compareIds.includes(productItem.id) && compareIds.length >= 2
									? "opacity-50 cursor-not-allowed"
									: "cursor-pointer"
							)}
							onClick={(e) => {
								e.stopPropagation();
								// 只有在允许的情况下才处理点击
								if (compareIds.includes(productItem.id) || compareIds.length < 2) {
									addOrDelCompare(e, productItem);
								}
							}}
						>
							{t("nav.Compare")}
						</label>
					</div>
				)}
			</div>
			{isOpen && <Lightbox mainSrc={imageUrl} onCloseRequest={() => setIsOpen(false)} />}
		</div>
	);
}

export function IconLove(props: { product?: any }) {
	let product = props.product;

	const [isLove, setIsLove] = useState(false);
	const { loveIds, setLoveProducts } = useLoveStore();
	const t = useTranslations();

	// console.log(loveIds,'product');
	useEffect(() => {
		if (loveIds.length && product?.id) {
			setIsLove(loveIds.includes(product.id));
		} else {
			setIsLove(false);
		}
	}, [loveIds]);
	const addOrDelLike = (event: React.MouseEvent) => {
		event.stopPropagation(); //
		const id = product?.id;
		if (id) {
			setLoveProducts(id);
		}
	};
	return (
		<i
			onClick={addOrDelLike}
			className={clsx(
				" text-xl text-[#000]",
				isLove ? " ri-heart-fill !text-[#d53a3d]" : "ri-heart-line !text-[#000]",
			)}
		></i>
	);
}
export function IconCompare(props: { product?: any }) {
	let product = props.product;

	const [isCompare, setIsCompare] = useState(false);
	const { compareIds, setcCmpareProducts, changeShow } = useCompareStore();
	const t = useTranslations();

	useEffect(() => {
		if (compareIds.length && product?.id) {
			setIsCompare(compareIds.includes(product.id));
		} else {
			setIsCompare(false);
		}
	}, [compareIds]);

	const addOrDelCompare = (event: React.MouseEvent, product) => {
		event.stopPropagation();
		const id = product?.id;
		if (id && (isCompare || compareIds.length < 2)) {
			setcCmpareProducts(id);
			changeShow(true);
		}
	};

	return (
		<i
			onClick={(e) => addOrDelCompare(e, product)}
			className={clsx(
				"text-xl text-[#000]",
				isCompare ? "ri-delete-bin-fill" : "ri-heart-line !text-[#000]",
				!isCompare && compareIds.length >= 2 && "opacity-50 cursor-not-allowed"
			)}
		></i>
	);
}

const renderPrice = (price) => {
	// 添加空值检查
	if (!price?.start || !price?.stop) return null;

	try {
		// 获取价格信息
		const startPrice = price.start.gross;
		const stopPrice = price.stop.gross;
		const currency = startPrice.currency || stopPrice.currency || '$';

		// 比较价格是否相同
		const isSamePrice = Number(startPrice.amount) === Number(stopPrice.amount);

		// 价格格式化，保留两位小数
		const formatPrice = (amount) => {
			return typeof amount === 'number'
				? amount.toFixed(2)
				: typeof amount === 'string'
					? parseFloat(amount).toFixed(2)
					: '0.00';
		};

		if (isSamePrice) {
			// 如果价格相同，只显示一个价格
			return (
				<div className="flex items-center">
					<span className="font-medium text-[#6a6a6a] irs">
						$ {formatPrice(startPrice.amount)}
					</span>
				</div>
			);
		} else {
			// 显示价格范围
			return (
				<div className="flex items-center space-x-2 max-md:flex-col max-md:items-start max-md:space-x-0">
					<span className="font-medium text-[#6a6a6a] irs">
						$ {formatPrice(startPrice.amount)} - {formatPrice(stopPrice.amount)}
					</span>
				</div>
			);
		}
	} catch (error) {
		console.error("Error rendering price:", error);
		return <span className="text-gray-500">Error rendering price</span>;
	}
};
