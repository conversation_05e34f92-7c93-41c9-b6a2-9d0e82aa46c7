"use client";
import { useTranslations } from "next-intl";
import { motion } from "framer-motion";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useEffect, useState, useRef } from "react";
import { Link } from "@/navigation";
import { defaultLocale } from "@/config";
import { Popover } from "antd";

// 定义每个部位对应的位置和产品类型
const positionMap = {
    "socks": { bottom: "20%", right: "12%" },
    "towel": { top: "55%", left: "33%" },
    "apparel": { top: "30%", left: "25%" },
    "eraser": { top: "48%", right: "28%" },
    "paddle": { top: "55%", right: "28%" }
};

export default function PickleballAccessories({ data, locale }: { data: { edges: any[] }, locale: string }) {
    const t = useTranslations();
    const [activeItem, setActiveItem] = useState<string | null>(null);
    const [popoverOpen, setPopoverOpen] = useState<string | null>(null);
    const [products, setProducts] = useState<any[]>([]);
    const [isMobile, setIsMobile] = useState(false);
    const [isLgDown, setIsLgDown] = useState(false);
    const productListRef = useRef<HTMLDivElement>(null);
    const productRefs = useRef<Map<string, HTMLDivElement>>(new Map());
    const containerRef = useRef<HTMLDivElement>(null);

    // 检测屏幕尺寸
    useEffect(() => {
        const checkScreenSize = () => {
            const isMobileSize = window.innerWidth <= 768;
            const isLgDownSize = window.innerWidth < 1024;
            
            setIsMobile(isMobileSize);
            setIsLgDown(isLgDownSize);
            
            // 如果是小屏幕并且产品已加载，则默认选中第一个产品
            if (isLgDownSize && products.length > 0 && !popoverOpen) {
                const firstProduct = products[0];
                if (firstProduct) {
                    setActiveItem(firstProduct.id);
                    setPopoverOpen(firstProduct.id);
                }
            }
        };
        
        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
        return () => window.removeEventListener('resize', checkScreenSize);
    }, [products, popoverOpen]);

    useEffect(() => {
        if (data?.edges) {
            console.log("data=====", data);
            const processedProducts = data.edges
                .map((edge: any) => {
                    const node = edge.node;
                    const accessoryType = node.metadata?.find((meta: any) =>
                        meta.key === "custom/home-pickleball-accessories"
                    )?.value;

                    const sortOrder = node.metadata?.find((meta: any) =>
                        meta.key === "custom/home-pickleball-accessories-sort"
                    )?.value;

                    if (!accessoryType || !positionMap[accessoryType]) {
                        return null;
                    }

                    return {
                        id: node.id,
                        name: node.name,
                        slug: node.slug,
                        price: node.pricing?.priceRange?.start?.gross?.amount
                            ? `$ ${node.pricing.priceRange.start.gross.amount.toFixed(2)}`
                            : "",
                        image: JSON.parse(node.media) || node.thumbnail?.url,
                        dotPosition: positionMap[accessoryType],
                        type: accessoryType,
                        sortOrder: sortOrder ? parseInt(sortOrder, 10) : Number.MAX_SAFE_INTEGER,
                        translation: node.translation
                    };
                })
                .filter(Boolean)
                .sort((a, b) => {
                    if (a.sortOrder !== Number.MAX_SAFE_INTEGER && b.sortOrder !== Number.MAX_SAFE_INTEGER) {
                        return a.sortOrder - b.sortOrder;
                    }
                    if (a.sortOrder !== Number.MAX_SAFE_INTEGER) return -1;
                    if (b.sortOrder !== Number.MAX_SAFE_INTEGER) return 1;
                    return 0;
                });

            setProducts(processedProducts);
            
            // 产品加载后，设置默认选中第一个
            if (processedProducts.length > 0) {
                const firstProduct = processedProducts[0];
                setActiveItem(firstProduct.id);
                
                // 如果是小屏幕，同时打开弹窗
                if (isLgDown) {
                    setPopoverOpen(firstProduct.id);
                }
            }
        }
    }, [data, isLgDown]);

    const handleItemInteraction = (id: string) => {
        setActiveItem(id);
        if (isLgDown) {
            // 如果点击的是当前打开的项，则关闭它
            if (popoverOpen === id) {
                setPopoverOpen(null);
            } else {
                setPopoverOpen(id);
            }
        }
    };


    if (!data?.edges || products.length === 0) {
        return null;
    }

    // 产品卡片内容组件
    const renderProductCard = (item: any) => {
        
        return (
            <div className="flex flex-col items-center bg-white p-1">
                <div className="w-16 h-16 mb-2">
                    <SEOOptimizedImage
                        src={item.image?.[0]?.url || "/image/default-image.webp"}
                        alt={item.name}
                        width={100}
                        height={100}
                        className="w-full h-full object-contain"
                    />
                </div>
                <h3 className="text-sm font-medium text-gray-900 text-center line-clamp-2 mb-1">
                    {locale === defaultLocale ? item.name : (item.translation?.name || item.name)}
                </h3>
                {item.price && <p className="text-[#555555] text-xs mb-2">{item.price}</p>}
                <Link
                    href={`/product/${item.slug}`}
                    className="inline-block px-3 py-1 bg-[#83c000] hover:text-white text-white rounded hover:bg-opacity-80 transition-colors text-xs w-full text-center"
                >
                    {t("banner.Shopnow")}
                </Link>
            </div>
        );
    };

    return (
        <section className="container py-12 md:py-16 lg:py-20">
            <div className="grid grid-cols-12 gap-6 lg:gap-8 max-w-[980px] 1921xl:max-w-[1472px] mx-auto">
                {/* 左侧图片区域 */}
                <div className="col-span-12 lg:col-span-6 relative order-2 lg:order-1 lg:flex items-center justify-center 1921xl:col-span-5">
                    <div className="relative overflow-hidden w-full" ref={containerRef}>
                        <SEOOptimizedImage
                            src="/image/home/<USER>"
                            alt="Pickleball Player with Accessories"
                            width={600}
                            height={800}
                            className="w-full h-auto object-contain"
                            quality={100}
                        />
                        <div className="absolute inset-0">
                            {products.map((item) => (
                                <Popover
                                    key={item.id}
                                    content={renderProductCard(item)}
                                    trigger={isLgDown ? "click" : []}
                                    open={isLgDown ? popoverOpen === item.id : false}
                                    onOpenChange={(visible) => {
                                        if (isLgDown) {
                                            setPopoverOpen(visible ? item.id : null);
                                        }
                                    }}
                                    placement={item.dotPosition.bottom ? "top" : "bottom"}
                                    arrow={{ pointAtCenter: true }}
                                    overlayStyle={{ maxWidth: '180px' }}
                                    overlayInnerStyle={{ padding: '8px' }}
                                    zIndex={40}
                                    destroyTooltipOnHide
                                >
                                    <motion.button
                                        className="absolute"
                                        style={item.dotPosition}
                                        onClick={() => isLgDown && handleItemInteraction(item.id)}
                                        onMouseEnter={() => !isLgDown && handleItemInteraction(item.id)}
                                        animate={{ scale: activeItem === item.id ? 1.15 : 1 }}
                                        transition={{ duration: 0.3, type: 'spring', stiffness: 300, damping: 20 }}
                                    >
                                        <div className={`
                                            w-5 h-5 max-2xl:w-5 max-2xl:h-5 rounded-full flex items-center justify-center 
                                            cursor-pointer active:scale-95 touch-manipulation
                                            transition-all duration-300
                                            ${activeItem === item.id
                                                ? 'bg-white shadow-[0_0_18px_4px_rgba(255,255,255,0.6)] animate-pulse'
                                                : 'bg-white/50'
                                            }
                                        `}>
                                            <div className="w-3 h-3 rounded-full bg-[#83c000]" />
                                        </div>
                                    </motion.button>
                                </Popover>
                            ))}
                        </div>
                    </div>
                </div>

                {/* 右侧产品列表 - 仅在桌面端显示 */}
                <div className="col-span-12 lg:col-span-6 order-1 lg:order-2 hidden lg:block 1921xl:col-span-7">
                    <h2 className={`text-2xl md:text-3xl 2xl:text-4xl mb-4 pb-4 border-b border-b-[#000] ${locale === defaultLocale ? "ib" : "font-semibold"}`}>
                        {t("home.PickleballAccessories")}
                    </h2>
                    <div className="relative">
                        <div
                            ref={productListRef}
                            className="
                                space-y-3
                                relative
                                1921xl:space-y-10
                            "
                        >
                            {products.map((item) => (
                                <motion.div
                                    key={item.id}
                                    ref={el => {
                                        if (el) productRefs.current.set(item.id, el);
                                    }}
                                    onMouseEnter={() => handleItemInteraction(item.id)}
                                    className={`
                                        flex items-center gap-4 
                                        bg-white 
                                        rounded-none
                                        shadow-none
                                        pb-4
                                        1921xl:pb-7
                                        border-b-[#7f7f7f]
                                        border-b
                                        ${activeItem === item.id ? 'opacity-100' : 'opacity-40'}
                                    `}
                                    transition={{ duration: 0.2 }}
                                >
                                    <div className="
                                        relative flex-shrink-0
                                        w-[90px] h-[90px]
                                    ">
                                        <SEOOptimizedImage
                                            src={item.image?.[0]?.url || "/image/default-image.webp"}
                                            alt={item.name}
                                            width={500}
                                            height={500}
                                            className="w-full h-full object-contain"
                                        />
                                    </div>
                                    <div className="flex-grow">
                                        <h3 className={`text-base font-medium text-gray-900 ${locale === defaultLocale ? "ib" : "font-semibold"}`}>
                                            {locale === defaultLocale ? item.name : (item.translation?.name || item.name)}
                                        </h3>
                                        {item.price && <p className="text-[#555555] text-sm my-3">{item.price}</p>}
                                        <Link
                                            href={`/product/${item.slug}`}
                                            className="inline-block px-4 py-2 bg-[#83c000] hover:text-white text-white rounded hover:bg-opacity-80 transition-colors text-sm font-normal"
                                        >
                                            {t("banner.Shopnow")}
                                        </Link>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}