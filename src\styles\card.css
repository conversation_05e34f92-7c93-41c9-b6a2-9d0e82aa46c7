.swiperc4 {
  position: relative;
  font-size: 0;
}

.swiperc4 .swiper-slide {
  display: inline-block;
  vertical-align: top;
  width: 14.285%;
}

/*联系页面*/
.swiperc4 .swiper-slide.slide4 {
  width: 25%;
}

.swiperc4 .con {
  display: block;
  padding-top: 35px;
  cursor: pointer;
  position: relative;
}

.swiperc4 .swiper-slide-active .con {
  padding: 0;
}

.swiperc4 .txt {
  display: block;
  margin: 0 auto 9px;
  width: 140px;
  height: 90px;
  max-width: 100%;
  text-align: center;
  background-color: #2c5ca5ad;
  border-radius: 9px 0 9px 9px;
  position: relative;
}

.swiperc4 .swiper-slide-active .txt {
  width: 180px;
  height: 125px;
  line-height: 1.4;
  padding: 10px 0;
  background-color: #70df79;
}

.swiperc4 .icn {
  display: block;
  width: 0;
  margin: 0 auto;
}

.swiperc4 .swiper-slide-active .icn {
  width: 46px;
}

.swiperc4 .icn img {
  display: block;
  width: 100%;
}

.swiperc4 .tit {
  font-size: 14px;
  color: #fff;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  width: 100%;
  padding: 5px 10px;
}

.swiperc4 .swiper-slide-active .tit {
  font-size: 16px;
  color: #fff;
  font-weight: bold;
  position: relative;
  top: 0;
  transform: translateY(-0);
  -webkit-transform: translateY(-0);
}

.swiperc4 .line {
  display: block;
  height: 2px;
  background-color: #2c5ca5ad;
  margin-bottom: 5px;
  position: relative;
}

.swiperc4 .line::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border: 4px solid #94979d;
  background-color: #3d6299;
  border-radius: 50%;
  z-index: 2;
}

.swiperc4 .swiper-slide-active .line::after {
  background-color: #70df79;
  border-color: #fff;
}


.swiperc4 .num {
  display: block;
  font-size: 29px;
  color: #3B4453;
  text-align: center;
  font-weight: bold;
  line-height: 55px;
}

.swiperc4 .swiper-slide-active .num {
  font-size: 55px;
  color: #70df79;
  letter-spacing: 5px;
}

.swiperc4 .m-line {
  position: absolute;
  top: 134px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #70df79;
  -webkit-transition: 1s;
  -moz-transition: 1s;
  -ms-transition: 1s;
  -o-transition: 1s;
  transition: 1s;
  z-index: 1;
}

@media only screen and (max-width: 1199px) {
  .swiperc4 .swiper-slide {
    width: 100%;
    padding: 0 8px;
  }

  .swiperc4 .swiper-slide.slide4 {
    width: 100%;
  }

  .swiperc4 .line {
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    margin-bottom: 0;
  }

  .swiperc4 .line i {
    display: none;
  }

  .swiperc4 .m-line {
    /*top: 0;*/
    /*left: 8px;*/
    /*width: 2px;*/
    display: none;
  }

  .swiperc4 .con {
    padding: 10px 0;
  }

  .swiperc4 .txt {
    /*height: 50px;*/
    /* line-height: 50px; */
    margin-bottom: 5px;
  }

  .swiperc4 .line {
    margin-bottom: 0;
  }

  .swiperc4 .num {
    line-height: 30px;
    font-size: 20px !important;;
  }

  .swiperc4 .swiper-slide-active .txt {
    padding: 10px 0;
    /*height: 80px;*/
  }

  .swiperc4 .swiper-slide-active .icn {
    width: 34px;
  }

  .swiperc4 .swiper-slide-active .tit {
    font-size: 16px !important;;
  }

  .swiperc4 .swiper-slide-active .num {
    font-size: 26px !important;;
    padding-left: 0;
    letter-spacing: 0;
  }
}

.bg {
  /*background-color: #8fa1bdad;*/
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 10px;
  overflow: hidden;
}

/*时间线样式*/
.historyMain {
  position: relative;
  /* min-height: 100vh; */

  background-image: url("https://www.absenenergy.com/en/dist/images/about_05.jpg");
  background-repeat: no-repeat;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
}

.historyMain::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #3b6de32b;
  /* 可以根据需要调整遮罩颜色和透明度 */
  z-index: 1;
  /* 确保遮罩层在背景图片之上 */
}

.historyMain>* {
  position: relative;
  z-index: 2;
  /* 确保内容在遮罩层之上 */
}

.historyYearBox {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.historyYear .swiper-container {
  overflow: visible;
}

.historyYearBox.fixed {
  position: fixed;
  /* Fixed position when in view */
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
}

.historyYearBox.absolute {
  position: absolute;
  /* Absolute position near the bottom */
  bottom: 0 !important;
  top: auto;
  left: 0;
  width: 100%;
  height: 100vh;
}

.historyYearBox .historyContainer .historyYearListBox {
  width: 40%;
  position: relative
}

.historyYearBox .historyContainer .historyYear {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 240px !important;
  height: 720px;
  overflow: hidden;
  position: relative
}

.historyYearBox .historyContainer .historyYear .Year {
  color: #fff;
  line-height: 240px;
  z-index: 2;
  position: relative;
  font-weight: 600;
  font-family: Arial, Helvetica, sans-serif
}

.historyYearBox .historyContainer .historyYear .YearList {
  line-height: 240px;
  height: 240px;
  position: relative;
  z-index: 2
}

.historyYearBox .historyContainer .historyYear .YearList .swiper-slide {
  color: rgba(240, 246, 244, 0.1);
  -webkit-transition: all 1s ease;
  -o-transition: all 1s ease;
  transition: all 1s ease;
  font-weight: 600;
  font-family: Arial, Helvetica, sans-serif
}

.historyYearBox .historyContainer .historyYear .YearList .swiper-slide.swiper-slide-active {
  color: #014099
}

.YearList.swiper-container {
  margin: 0;
}

.historyYearBox .historyContainer .roundBox {
  position: absolute;
  width: 440px;
  height: 440px;
  right: -22px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  border-radius: 100%
}

.historyYearBox .historyContainer .roundBox svg {
  position: absolute;
  left: -18px;
  top: -18px;
  right: -18px;
  bottom: -18px
}

.historyYearBox .historyContainer .roundBox svg .path-loop {
  fill: none;
  stroke: rgba(240, 246, 244, 0.1);
  stroke-miterlimit: 10;
  stroke-width: 0.1px
}

.historyYearBox .historyContainer .roundBox svg .path-loop1 {
  fill: none;
  stroke: #014099;
  stroke-miterlimit: 10;
  stroke-width: 0.1px;
  stroke-dashoffset: 157;
  -webkit-transition: all .8s ease;
  -o-transition: all .8s ease;
  transition: all .8s ease
}

.historyListMain {
  position: relative;
  z-index: 3
}

.historyListMain .historyContainer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.historyListMain .historyContainer .historyList {
  width: 100%
}

.historyListMain .historyContainer .historyList ul li {
  margin-top: 15px;
  padding: 0 0 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3)
}

.historyListMain .historyContainer .historyList ul li .imgBox {
  padding-top: 20px
}

.historyListMain .historyContainer .historyList ul li .imgBox img {
  width: 100%;
  display: block;
  border-radius: 20px
}

.historyListMain .historyContainer .historyList ul li:last-child {
  border-bottom: none
}


@media (min-width: 768px) {
  .historyYearBox .historyContainer .historyYear {
    font-size: 15vw;
    height: 45vw
  }

  .historyYearBox .historyContainer .historyYear .Year {
    line-height: 15vw
  }

  .historyYearBox .historyContainer .historyYear .YearList {
    line-height: 15vw;
    height: 15vw
  }

  .historyYearBox .historyContainer .roundBox {
    width: 27.5vw;
    height: 27.5vw;
    right: -1.375vw
  }

  .historyYearBox .historyContainer .roundBox svg {
    left: -1.125vw;
    top: -1.125vw;
    right: -1.125vw;
    bottom: -1.125vw
  }

  .historyListMain .historyContainer .historyList {
    width: 100%;
    padding: 3.125vw 0
  }

  .historyListMain .historyContainer .historyList ul li {
    margin-top: 2.188vw;
    padding: 0 0 2.188vw;
    border-bottom: 0.063vw solid rgba(255, 255, 255, 0.3)
  }

  .historyListMain .historyContainer .historyList ul li .listYear {
    font-size: 1.5vw;
    padding-bottom: 0.625vw
  }

  .historyListMain .historyContainer .historyList ul li .desc {
    font-size: 1vw;
    line-height: -webkit-calc(22 / 16);
    line-height: calc(22 / 16)
  }

  .historyListMain .historyContainer .historyList ul li .imgBox {
    padding-top: 1.25vw
  }

  .historyListMain .historyContainer .historyList ul li .imgBox img {
    border-radius: 1.25vw
  }

}

.historyListMain .historyContainer .historyList ul li .listYear {
  font-size: 18px;
  line-height: 1.2;
  padding-bottom: 10px;
  font-weight: 600;
  color: #fff;
  -webkit-transition: all .6s ease;
  -o-transition: all .6s ease;
  transition: all .6s ease
}

.historyListMain .historyContainer .historyList ul li .desc {
  font-size: 16px;
  color: #fff;
  -webkit-transition: all .6s ease;
  -o-transition: all .6s ease;
  transition: all .6s ease
}


@media (min-width: 992px) {
  .historyYearBox {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
  }

  .historyListMain .historyContainer .historyList {
    width: 33.313vw;
    padding-top: 45vh;
    padding-bottom: 26vw
  }

  .historyListMain .historyContainer .historyList ul li .listYear {
    color: rgba(255, 255, 255, 0.3)
  }

  .historyListMain .historyContainer .historyList ul li .desc {
    color: rgba(255, 255, 255, 0.3)
  }

  .historyListMain .historyContainer .historyList ul li.cur .listYear {
    color: #fff
  }

  .historyListMain .historyContainer .historyList ul li.cur .desc {
    color: #fff
  }


  .historyYearBox .historyContainer .historyYear {
    font-size: 12.5vw;
    height: 37.5vw
  }

  .historyYearBox .historyContainer .historyYear .Year {
    line-height: 12.5vw
  }

  .historyYearBox .historyContainer .historyYear .YearList {
    line-height: 12.5vw;
    height: 12.5vw
  }

  .historyYearBox .historyContainer .roundBox {
    width: 22.917vw;
    height: 22.917vw;
    right: -1.146vw
  }

  .historyYearBox .historyContainer .roundBox svg {
    left: -0.938vw;
    top: -0.938vw;
    right: -0.938vw;
    bottom: -0.938vw
  }

  .historyListMain .historyContainer .historyList {
    width: 40vw;
    padding-bottom: 10vw
  }

  .historyListMain .historyContainer .historyList ul li {
    margin-top: 1.823vw;
    padding: 0vw 0vw 1.823vw;
    border-bottom: 0.052vw solid rgba(255, 255, 255, 0.3)
  }

  .historyListMain .historyContainer .historyList ul li .listYear {
    font-size: 1.25vw;
    padding-bottom: 0.521vw
  }

  .historyListMain .historyContainer .historyList ul li .desc {
    font-size: 0.833vw
  }

  .historyListMain .historyContainer .historyList ul li .imgBox {
    padding-top: 1.042vw
  }

  .historyListMain .historyContainer .historyList ul li .imgBox img {
    border-radius: 1.042vw
  }
}

.historyContainer {
  max-width: 80vw;
  margin: 0 auto;
  width: 100%;
}

@media (min-width: 2000px) {
  .historyContainer {
    max-width: 70vw
  }

  .historyListMain .historyContainer .historyList {
    width: 35vw;
  }
}

@media (max-width: 1800px) {
  .historyContainer {
    max-width: 70vw
  }

  .historyListMain .historyContainer .historyList {
    width: 35vw;
  }
}

@media (max-width: 576px) {
  .historyListMain .historyContainer .historyList {
    width: 100vw;
  }
}

/*翻转卡片*/
.flip-card {
  perspective: 1000px;
  /* 这会给3D翻转效果添加深度 */
  width: 100%;
  /* 根据您的设计调整 */
  height: 400px;
  /* 根据您的设计调整 */
  transition: transform .75s ease-in-out, -webkit-transform .75s ease-in-out;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

@media (min-width: 1000px) and (max-width: 1300px) {
  .flip-card {
    height: 500px;
    /* 根据您的设计调整 */
  }
}

@media (min-width: 768px) and (max-width: 1000px) {
  .flip-card {
    height: 600px;
    /* 根据您的设计调整 */
  }
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  cursor: pointer;
  -webkit-transition: -webkit-transform .75s ease-in-out;
  /* 旧版WebKit浏览器 */
  -webkit-transform-style: preserve-3d;
  /* 旧版WebKit浏览器 */
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  /* 在翻转的时候隐藏背面 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.flip-card-front {
  /* 正面样式 */
  background-color: #93addb;
  color: black;
  /* 调整为您的文字颜色 */
}

.flip-card-back {
  /* 背面样式 */
  background-color: #4c78c7;
  /* 调整为您的背景颜色 */
  color: white;
  /* 调整为您的文字颜色 */
  transform: rotateX(180deg);
  /* 翻转背面 */
}

.flipped {
  transform: rotateX(180deg);
  /* 翻转动画 */
}

/*全球搜索*/
@media (max-width: 768px) {
  #map_container {
    display: none !important;
  }
}

.CompanyName .flex:first-child {
  font-size: 24px;
  /* 字体大小 */
  color: #014099;
  /* 字体颜色 */
  font-weight: bold;
  /* 字体加粗 */
  margin-bottom: 10px;
  /* 底部外边距 */
}
