// export const contactObj = {
// 	Tiktok: "https://www.tiktok.com/@fancymartin7?_t=8pxS3FQTdrK&_r=1",
// 	Linkedin: "https://www.linkedin.com/in/long-sheng-81763b26a/",
// 	Youtube: "https://www.youtube.com/@longsheng2019",
// 	Twitter: "https://twitter.com/longsheng2019",
// 	Pinterest: "https://www.pinterest.com/longshengmfg/",
// 	FaceBook: "https://www.facebook.com/profile.php?id=61558577050675",
// };
export const contactObj = {
  FaceBook: "https://www.facebook.com/",

  instagram: "https://www.instagram.com/",
  Youtube: "https://www.youtube.com/",
  Pinterest: "https://www.pinterest.com/",
  Linkedin: "https://www.linkedin.com/",

  Twitter: "https://twitter.com/",
  Tiktok: "https://www.tiktok.com/",

};


export const contactInfo = {
  email: "<EMAIL>",
  // 没提供电话
  phone: "(212)555-1234",
  whatsapp: "",
  wechat: "",
  address: "Unit1001-1005, Tower 2, Hongyuan building, No.51 Zhengxue Road, Shanghai, PR China, P.C 200433",
  addressLink: "https://www.google.com/maps/place/51+Zhengxue+Rd,+Yang+Pu+Qu,+Shang+Hai+Shi,+China,+200439/@31.3060945,121.5053091,17z/data=!3m1!4b1!4m10!1m2!2m1!1sTower+2,+Hongyuan+building,+No.51+Zhengxue+Road,+Shanghai!3m6!1s0x35b273bf146f82a5:0x7463805522c3621f!8m2!3d31.30609!4d121.51018!15sCjlUb3dlciAyLCBIb25neXVhbiBidWlsZGluZywgTm8uNTEgWmhlbmd4dWUgUm9hZCwgU2hhbmdoYWmSARBnZW9jb2RlZF9hZGRyZXNz4AEA!16s%2Fg%2F11r8fnmj3h?entry=ttu&g_ep=EgoyMDI1MDYzMC4wIKXMDSoASAFQAw%3D%3D",
  name: "Recell-ing & Ceiling",

}

export const FOLLOW = [
  {
    id: 1,
    href: "",
    image: "/image/footer-follow/shopify.png",
    image_hover: "/image/footer-follow/shopify-active.png",
    name: "Shopify",
  },
  {
    id: 2,
    href: "",
    image: "/image/footer-follow/jd.png",
    image_hover: "/image/footer-follow/jd-active.png",
    name: "JD",
  },
  {
    id: 3,
    href: "",
    image: "/image/footer-follow/tianmao.png",
    image_hover: "/image/footer-follow/tianmao-active.png",
    name: "Tianmao",
  },
  {
    id: 4,
    href: "",
    image: "/image/footer-follow/titok.png",
    image_hover: "/image/footer-follow/titok-active.png",
    name: "Tiktok",
  },
  {
    id: 5,
    href: "",
    image: "/image/footer-follow/ali.png",
    image_hover: "/image/footer-follow/ali-active.png",
    name: "AliExpress",
  },
  {
    id: 6,
    href: "",
    image: "/image/footer-follow/facebook.png",
    image_hover: "/image/footer-follow/facebook-active.png",
    name: "Facebook",
  },
  {
    id: 7,
    href: "",
    image: "/image/footer-follow/ig.png",
    image_hover: "/image/footer-follow/ig-active.png",
    name: "Instagram",
  }
]
export let payarr = [
  {
    alt: process.env.NEXT_PUBLIC_COMPANY_NAME,
    img: '/image/img/pay/pay6.svg',
  },
  {
    alt: process.env.NEXT_PUBLIC_COMPANY_NAME,
    img: '/image/img/pay/pay1.svg',
  },
  {
    alt: process.env.NEXT_PUBLIC_COMPANY_NAME,
    img: '/image/img/pay/pay2.svg',
  },
  {
    alt: process.env.NEXT_PUBLIC_COMPANY_NAME,
    img: '/image/img/pay/pay4.svg',
  },

  {
    alt: process.env.NEXT_PUBLIC_COMPANY_NAME,
    img: '/image/img/pay/pay8.svg',
  },

]