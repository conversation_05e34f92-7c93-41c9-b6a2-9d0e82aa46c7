import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { cookies } from "next/headers";

export const dynamic = "force-dynamic";

interface GoogleAuthResponse {
	detail: {
		saleor_user_email: string;
		saleor_user_id: string;
		saleor_user_token: string;
		refresh_token: string;
	};
}

enum GoogleAuthErrorType {
	NO_CODE = "no_code",
	NETWORK_ERROR = "network_error",
	INVALID_RESPONSE = "invalid_response",
	AUTH_FAILED = "auth_failed",
	INCOMPLETE_DATA = "incomplete_data",
	COOKIE_ERROR = "cookie_error",
	UNKNOWN = "unknown",
}

interface GoogleAuthError {
	type: GoogleAuthErrorType;
	message: string;
	details?: unknown;
}

const AUTH_ERROR_MESSAGES: Record<GoogleAuthErrorType, string> = {
	[GoogleAuthErrorType.NO_CODE]: "缺少授权码",
	[GoogleAuthErrorType.NETWORK_ERROR]: "Google API 网络请求错误",
	[GoogleAuthErrorType.INVALID_RESPONSE]: "Google API 返回数据解析错误",
	[GoogleAuthErrorType.AUTH_FAILED]: "Google 认证失败",
	[GoogleAuthErrorType.INCOMPLETE_DATA]: "用户数据不完整",
	[GoogleAuthErrorType.COOKIE_ERROR]: "设置 cookie 失败",
	[GoogleAuthErrorType.UNKNOWN]: "认证回调处理发生未知错误",
};

async function handleCallback(req: NextRequest) {
	console.log("🚀 ~ 请求url", req.url);
	try {
		const searchParams = req.nextUrl.searchParams;
		const code = searchParams.get("code");
		const cookieStore = cookies();

		if (!code) {
			console.error(AUTH_ERROR_MESSAGES[GoogleAuthErrorType.NO_CODE]);
			return NextResponse.redirect(new URL(`/auth/error?reason=${GoogleAuthErrorType.NO_CODE}`, req.url));
		}

		// 调用 Google Auth API
		let responseData: Response;
		try {
			responseData = await fetch(`${process.env.NEXT_PUBLIC_GOOGLE_AUTH_URL}${code}`);
		} catch (error) {
			console.error(AUTH_ERROR_MESSAGES[GoogleAuthErrorType.NETWORK_ERROR], error);
			return NextResponse.redirect(
				new URL(`/auth/error?reason=${GoogleAuthErrorType.NETWORK_ERROR}`, req.url),
			);
		}

		let res: GoogleAuthResponse;
		try {
			const jsonData = await responseData.json();
			if (!jsonData || typeof jsonData !== "object" || !("detail" in jsonData)) {
				throw new Error("Invalid response format");
			}
			res = jsonData as GoogleAuthResponse;
		} catch (error) {
			console.error(AUTH_ERROR_MESSAGES[GoogleAuthErrorType.INVALID_RESPONSE], error);
			return NextResponse.redirect(
				new URL(`/auth/error?reason=${GoogleAuthErrorType.INVALID_RESPONSE}`, req.url),
			);
		}

		if (!responseData.ok) {
			console.error(AUTH_ERROR_MESSAGES[GoogleAuthErrorType.AUTH_FAILED], res);
			return NextResponse.redirect(new URL(`/auth/error?reason=${GoogleAuthErrorType.AUTH_FAILED}`, req.url));
		}

		const { detail: data } = res;

		// 验证必要的用户数据
		if (!data?.saleor_user_email || !data?.saleor_user_id || !data?.saleor_user_token) {
			console.error(AUTH_ERROR_MESSAGES[GoogleAuthErrorType.INCOMPLETE_DATA], data);
			return NextResponse.redirect(
				new URL(`/auth/error?reason=${GoogleAuthErrorType.INCOMPLETE_DATA}`, req.url),
			);
		}

		const redirectPath = cookieStore.get("googleLoginRedirectPath")?.value;
		console.log("浏览器的获取的cookie", redirectPath);

		// 确保重定向URL是有效的，如果无效则使用默认值
		const defaultRedirectPath = process.env.NEXT_PUBLIC_SITE_URL;
		const safeRedirectPath = redirectPath || defaultRedirectPath;
		console.log("重定向的url", safeRedirectPath);

		const url = new URL(safeRedirectPath);
		// cookieStore.delete("googleLoginRedirectPath");

		const response = NextResponse.redirect(url);

		// 在响应中设置 cookies
		try {
			response.cookies.set(
				"__user__login__info",
				JSON.stringify({
					username: data.saleor_user_email,
					user: {
						email: data.saleor_user_email,
						id: data.saleor_user_id,
					},
					email: data.saleor_user_email,
					token: data.saleor_user_token,
					refreshToken: data.refresh_token,
				}),
			);
		} catch (error) {
			console.error(AUTH_ERROR_MESSAGES[GoogleAuthErrorType.COOKIE_ERROR], error);
			return NextResponse.redirect(
				new URL(`/auth/error?reason=${GoogleAuthErrorType.COOKIE_ERROR}`, req.url),
			);
		}

		return response;
	} catch (error) {
		console.error(AUTH_ERROR_MESSAGES[GoogleAuthErrorType.UNKNOWN], error);
		return NextResponse.redirect(new URL(`/auth/error?reason=${GoogleAuthErrorType.UNKNOWN}`, req.url));
	}
}

export { handleCallback as GET };
