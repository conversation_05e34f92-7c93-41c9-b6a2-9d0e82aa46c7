"use client";
import React from "react";
import { Button } from "@/components/Button";
import { SubmitHandler, useForm } from "react-hook-form";
import { RingLoader } from "react-spinners";
import { getCookie, setCookie } from "cookies-next";
import { useTranslations } from "next-intl";
import { App } from "antd";

type FormValues = {
	email?: string;
	remember?: boolean;
	onSubmit: (data?: React.BaseSyntheticEvent<object, any, any> | undefined) => Promise<void>;
	setLoginModalOn?: any;
};

interface LoginFormProps {
	setLoginModalOn?: any;
}

const LoginForm = ({ setLoginModalOn }: LoginFormProps) => {
	const t = useTranslations();
	let get_form_info: any = getCookie("created__user__info");
	if (get_form_info) {
		get_form_info = JSON.parse(get_form_info);
	}
	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
	} = useForm<FormValues>();
	const { message } = App.useApp();
	const [loading, setLoading] = React.useState(false);

	const onSubmit: SubmitHandler<FormValues> = async (data: any) => {
		// console.log(data,'data');
		setLoading(true);

		try {
			// 调用API发送重置密码邮件
			const response = await fetch(process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + "/saleor/account_pwd_email", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					account_email: data.email,
				}),
			});

			if (response.ok) {
				message.success(t("common.Mailsuccess"));
			} else {
				message.error(t("common.Emailerror"));
			}
		} catch (error) {
			message.error(t("common.Emailfailed"));
		} finally {
			setLoading(false);
		}
	};
	return (
		<div>
			<div className="user mt-7">
				<form action="" onSubmit={handleSubmit(onSubmit)}>
					<input
						type="text"
						id="email"
						className={`w-full rounded-sm border bg-white px-5 py-3 text-xl  !text-black outline-none placeholder:text-lg ${
							errors.email ? "border-red-500" : "border-themeSecondary300"
						}`}
						defaultValue={get_form_info?.email}
						placeholder={t("form.109a2790cc6b48461908f883b7b41549f9b3")}
						{...register("email", { required: true })}
					/>
					<Button
						className={`mt-6 flex w-full items-center justify-center gap-4 !rounded-sm !bg-black ${
							loading ? "bg-themeSecondary800" : ""
						}`}
					>
						{loading ? <RingLoader color="#fff" size={30} /> : ""}
						{loading ? t("message.7f9e518a7a1d1e4bc3e8990bed1d9be4d404") + "..." : t("common.Sign_in")}
					</Button>
				</form>
			</div>
		</div>
	);
};

export default LoginForm;
