"use client";
import React, { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import clsx from "clsx";
import Price from "@/components/Price/price";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import CartList from "./new-cart-list";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { useRouter } from "@/navigation";
import { useUserStore } from "@/lib/store/user";
import { CartDrawerOpen } from "@/lib/store/CartDrawerOpen";
import MyEmpty from "@/components/MyEmpty";

const CartContent = () => {
	const { cartList, setisCart } = useShoppingCartStore();
	let { open, setOpen } = CartDrawerOpen();
	let locale = useLocale();
	let router = useRouter();
	const { userInfo } = useUserStore();
	const t = useTranslations();
	const [loading, setLoading] = useState(false);

	const [itemCount, setItemCount] = useState<number>(0);
	const { removeCartItem, updateLinesCount } = useShoppingCart();
	// 删除一行 购物车
	function handelupdateLinesCount(id: string) {
		removeCartItem([id]);
	}

	async function handelupdateCount(id: string, count: number): Promise<void> {
		try {
			await updateLinesCount({
				checkoutId: cartList.id,
				lines: [
					{
						lineId: id,
						quantity: count,
					},
				],
			});
		} catch (error) {
			console.error("Update cart item failed:", error);
			throw error; // 抛出错误，让CartList组件知道更新失败
		}
	}
	useEffect(() => {
		// 更新购物车数量
		setItemCount(cartList?.lines?.length || 0);
	}, [cartList?.lines?.length]);

	function tocheckout() {
		setisCart(true);
		router.push("/checkout");
		Close();
	}
	function Close() {
		setTimeout(() => {
			setOpen(false);
		}, 800);
	}

	return (
		<div>
			{cartList?.lines?.length > 0 && (
				<p className="text-gray-500 max-md:px-4">
					<span>({itemCount} </span>
					<span>{t("shop.items")})</span>
				</p>
			)}

			<div className="py-4">
				<div className="flex flex-col gap-y-8">
					{cartList && cartList?.lines?.length > 0 ? (
						<div>
							{cartList?.lines.map((product, index) => {
								return (
									<CartList
										key={index}
										product={product}
										isShowCount={true}
										count={product.quantity}
										handelupdateLinesCount={handelupdateLinesCount}
										handelupdateCount={handelupdateCount}
									/>
								);
							})}

							<div>
								{/* <ContactForm
										locale={locale}
										title={t("base.getInstantQuoteAndTS")}
										inquiry={cartList.lines}
										source="inquiry"
										// @ts-ignore
										titleContent={t("form.fillOutIn")}
										className="!py-0 "
										innerClassName="!shadow-none !rounded-none"
									></ContactForm> */}

								{/* 去支付 */}
								<section
									aria-labelledby="summary-heading"
									className={clsx(
										"mt-16 h-fit cursor-pointer rounded-sm bg-gray-50 px-4 py-6 sm:p-6 lg:mt-0 lg:p-8",
									)}
								>
									<div className="flex justify-between">
										<h2 id="summary-heading" className="text-lg font-medium text-gray-900">
											{t("shop.2725b01476ca66429dd8f0b0ef7585e5f645")}
										</h2>

										<Price className="!text-black" price={cartList?.totalPrice?.gross?.amount} />
									</div>

									{userInfo && (
										<div className="mt-6">
											<div
												onClick={tocheckout}
												className="block !w-full rounded-sm border border-transparent bg-black px-4 py-3 text-center text-base font-medium text-white shadow-sm duration-300 hover:bg-opacity-80"
											>
												{t("common.Checkout")}
											</div>
										</div>
									)}
									{!userInfo && (
										<div
											className="mt-6 cursor-pointer"
											onClick={() => {
												const login = document.querySelector("#web-login") as HTMLLIElement;
												login.click();
												Close();
											}}
										>
											<p className="block !w-full rounded-md border border-transparent bg-black px-4 py-3 text-center text-base font-medium text-white shadow-sm duration-300 hover:bg-opacity-80">
												{t("common.Checkout")}
											</p>
										</div>
									)}
								</section>
							</div>
						</div>
					) : (
						<MyEmpty text={""} description={t("nav.Nogoods")} className="py-20 max-md:py-4">
							<i className="ri-shopping-cart-2-fill text-4xl  !text-ddd"></i>
						</MyEmpty>
					)}
				</div>
			</div>
		</div>
	);
};

export default React.memo(CartContent);
