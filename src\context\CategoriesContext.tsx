"use client";
import React, { createContext, useContext, useEffect, useState } from "react";
import { ProductCategoriesDocument, ProductCategoriesQuery } from "@/gql/graphql";
import { defaultLocale } from "@/config";
import { executeGraphQL } from "@/lib/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";

interface CategoryContextType {
	categories: ProductCategoriesQuery["categories"] | undefined;
	setCategories: React.Dispatch<React.SetStateAction<ProductCategoriesQuery["categories"] | undefined>>;
}

const CategoryContext = createContext<CategoryContextType | undefined>(undefined);

export const useCategory = () => {
	const context = useContext(CategoryContext);
	if (!context) {
		throw new Error("useCategory must be used within a CategoryProvider");
	}
	return context;
};

export const CategoryProvider: React.FC<{ children: React.ReactNode; initialCategories?: ProductCategoriesQuery["categories"] ;locale:string}> = ({ children, initialCategories,locale }) => {
	const [categories, setCategories] = useState<ProductCategoriesQuery["categories"]>(initialCategories);

	useEffect( () => {
		const fetchCategory = async () => {
			const result = await executeGraphQL(ProductCategoriesDocument, {
				withAuth: false,
				variables: { locale: handleGraphqlLocale(locale||defaultLocale), first: 20 },
			});
			if (result && result.categories) {
				setCategories(result.categories);
			}
		};

		fetchCategory();
	}, []);
	return (
		<CategoryContext.Provider value={{ categories, setCategories }}>
			{children}
		</CategoryContext.Provider>
	);
};
