'use client'
import * as Icon from "@phosphor-icons/react/dist/ssr";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { Form, Input, message, Modal, Spin } from "antd";
import { sendForm } from "@/lib/api/form.ts";
import { SubmitBlogComment } from "@/lib/api/blog.ts";
import { Blog } from "@/lib/@types/api/blog";


const SumitCommentBtn=({blog_id}:{blog_id:number})=>{
	const t=useTranslations()
	const [sendComment,setsendComment]=useState("")
	const [isModalOpen,setIsModalOpen]=useState(false)

const opeComment=()=>{
		if(!sendComment){
			void message.warning('Please write something first!')
			return
		}
	setIsModalOpen(true)
	}

	return <>
		<div className='form-search relative w-full h-12'>
			<input className='py-2 px-4 w-full h-full border border-line rounded-lg pr-[85px]' type="text" value={sendComment}
						 placeholder="Write a message" onChange={e => setsendComment(e.target.value)} />
			<div className="absolute top-1/2 -translate-y-1/2 right-4 p-1 border-l border-l-gray-400 flex justify-center items-center pl-4 " onClick={()=>opeComment()}>
				<div className="rounded-full bg-main  p-1 ">
					<Icon.PaperPlaneRight className="  heading6 text-white  hover:text-black duration-300  cursor-pointer" />
				</div>
			</div>
		</div>
		<SendModal isModalOpen={isModalOpen} sendComment={sendComment} setIsModalOpen={setIsModalOpen} blog_id={blog_id} />
	</>

}
// "comment_content": "Content of a test comment.",
// 	"comment_blog_id": "72",
// 	"comment_obj": true,
// 	"anonymous_comment_firstname": "testfirstname",
// 	"anonymous_comment_lastname": "testlastname",
// 	"anonymous_comment_company": "testcompany",
// 	"comment_lang_code": {
// 	"lang_code": "en"
// }
const SendModal=({isModalOpen,setIsModalOpen,sendComment,blog_id}:{isModalOpen:boolean,sendComment:string;setIsModalOpen:Function,blog_id:number})=>{
	const t=useTranslations()

	const [form] = Form.useForm();
	const [loading,setLoading]=useState(false)

	const handleOk=async ()=>{
		try {
			if(!sendComment)return
			const values = form.getFieldsValue();
			const Obj:Blog.BlogCommentItem={comment_content:sendComment,...values,comment_lang_code:{lang_code:'en'},comment_blog_id:blog_id,comment_obj:true}
			setLoading(true)
			const response = await SubmitBlogComment(Obj);
			if (response && response.code === 200) {
				void message.success(t('form.sendSuccess'));
			} else {
				void message.error(response?.message || t('form.sendFail'));
			}
			setLoading(false)
			setIsModalOpen(false)
		}catch (e){
			setLoading(false)
			void message.error( t('form.sendFail'));

		}

}
	return <Modal title="Please leave your information " open={isModalOpen} onOk={handleOk} onCancel={()=>setIsModalOpen(false)} okText="Send">
		<div>
			<Form
				form={form}
				name="control-hooks"
				style={{ maxWidth: 600 }}
			>
				<Form.Item name="anonymous_comment_firstname" label="First name" rules={[{ required: true }]}>
					<Input />
				</Form.Item>
				<Form.Item name="anonymous_comment_lastname" label="Last ame" rules={[{ required: true }]}>
					<Input />
				</Form.Item>
				<Form.Item name="anonymous_comment_company" label="Company" rules={[{ required: true }]}>
					<Input />
				</Form.Item>

			</Form>
		</div>
	</Modal>
}
export default React.memo(SumitCommentBtn)
