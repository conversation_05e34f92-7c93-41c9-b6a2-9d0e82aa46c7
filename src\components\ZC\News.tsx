
import Blog from "../Resource/Blog";
import { Link } from "@/navigation";

export default function News({ blogList }: { blogList: Blog.BlogListItem[] }) {


	return (
		<div className="w-full pb-[80px] pt-[10px] max-md:pt-5">
			<div className="container mx-auto p-6">
				<div className="grid grid-cols-3 gap-6 max-md:grid-cols-1">

					{blogList.length>0&&blogList.map(
						(item, index) =>
							index < 3 && (
								<Link href={`/blog/${item.blog_slug}`} key={item.blog_id} className="text-black ">
									<div className=" p-4">
                  <img
												alt={item.blog_title}
												className="mb-4 h-[400px] w-[450px] max-lg:w-full object-cover rounded-sm" 
												src={item.blog_cover_origin}
												width="600"
											/>

										<h3 className="text-xl font-bold">{item.blog_title}</h3>
										<p
											className="mt-2 line-clamp-2 text-[#666666]"
											dangerouslySetInnerHTML={{ __html: item.blog_excerpt }}
										></p>
									</div>
								</Link>
							),
					)}
				</div>
			</div>
		</div>
	);
}
