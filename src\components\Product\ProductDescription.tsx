"use client";
import { defaultLocale } from "@/config";
import { Tabs } from "antd";
import { StyledArticle } from "../Blogs/BlogArticle";
import { useTranslations, useLocale } from "next-intl";
import { useState } from "react";
import { contactInfo } from "@/lib/contacts";
import { motion } from "framer-motion";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import ProductReviews from "./product-reviews";
const ProductDescription = ({ product, locale }) => {
	const t = useTranslations("nav");

	// 获取描述内容
	const descriptionContent = locale == defaultLocale
		? filterSortDsc(product.descriptionJson).longDsc
		: filterSortDsc(product.translation.descriptionJson).longDsc || filterSortDsc(product.descriptionJson).longDsc;

	// 检查是否有有效内容（去除HTML标签和空白字符后）
	const hasContent = descriptionContent && descriptionContent.replace(/<[^>]*>/g, '').trim().length > 0;

	return (
		<div>
			{/* 只在有内容时显示描述部分 */}
			{hasContent && (
				<StyledArticle>
					<div
						className="p-4"
						dangerouslySetInnerHTML={{
							__html: descriptionContent,
						}}
					/>
				</StyledArticle>
			)}
			<h2 className={`text-center text-4xl mt-32 max-md:mt-28 mb-8 ${locale === defaultLocale ? "ib" : "font-semibold"} uppercase`}>{t("CustomerReviews")}</h2>
			<ProductReviews productId={product.id} rating={product?.rating || 5} />
		</div>
	)
};

export default ProductDescription;

type dscList = {
	blocks: dscObj[];
};
type dscObj = {
	data: { text: string };
	type: string;
};
const filterSortDsc = (dsc: string) => {
	if (!dsc) {
		dsc = '{"blocks": []}';
	}

	const dscObj = JSON.parse(dsc) as dscList;
	let sortDsc = "";
	let longDsc = "";

	// 打印后立即检查blocks是否存在
	// console.log(dscObj, 'dscObj');

	// 首先检查blocks是否存在
	if (!dscObj.blocks) {
		// 如果blocks不存在，初始化为空数组
		dscObj.blocks = [];
	}

	// 现在可以安全地访问length属性
	if (dscObj.blocks.length > 0) {
		// 检查索引1是否存在
		if (dscObj.blocks.length > 1 && dscObj.blocks[1]?.data?.text) {
			sortDsc = dscObj.blocks[1].data.text;
		}

		// 检查索引0是否存在
		if (dscObj.blocks[0]?.data?.text) {
			longDsc = handlerInnerHtml(dscObj.blocks[0].data.text);
		}
	}

	return { sortDsc, longDsc };
};
const handlerInnerHtml = (html: string): string => {
	if (!html) return html;
	return html
		.replace(/&lt;/g, "<")
		.replace(/&gt;/g, ">")
		.replace(/&amp;/g, "&")
		.replace(/&quot;/g, '"')
		.replace(/&#8216;/g, "‘")
		.replace(/&#8217;/g, "’")
		.replace(/&#8211;/g, "–")
		.replace(/&nbsp;/g, " ");
};
