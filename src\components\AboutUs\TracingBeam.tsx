"use client";
import Image from "next/image";
import clsx, { ClassValue } from "clsx";
import React, { useEffect, useRef, useState } from "react";
import { motion, useTransform, useScroll, useVelocity, useSpring } from "framer-motion";
import { twMerge } from "tailwind-merge";
import { useTranslations } from "next-intl";
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
export function Tracing() {
  const t = useTranslations('nav');

  
  
  const dummyContent = [
    {
      title: t('IN 2017 THE COMPANY ESTABLISHED'),
      badge: "2017",
      image: "/image/default-image.webp",
    },
    {
      title:t('IN 2018 OUR FACTORY ESTABLISHED IN FOSHAN CITY'),
      badge: "2018",
      image: "/image/default-image.webp",
    },
    {
      title:t('IN 2019 WE GET IMPORT AND EXPORT QUALIFICATION'),
      badge: "2019",
      image: "/image/default-image.webp",
    },
    {
      title:t('IN 2021 WE PASSED 3A CREDIT SYSTEM CERTIFICATION'),
      badge: "2021",
      image: "/image/default-image.webp",
    },
  
    {
      title:t('IN 2023'),
      badge: "2023",
      image: "/image/default-image.webp",
    },
  
    {
      title:t('IN 2024'),
      badge: "2024",
      image: "/image/default-image.webp",
    },
  ];
  
  return (
    <TracingBeam className="w-full overflow-hidden">
      <div className="relative mx-auto ml-14 pt-4 antialiased">
        {dummyContent.map((item, index) => (
          <div key={`content-${index}`} className="mb-10">
            <div className="c-flex gap-10 max-md:flex-col">
              <div className={"mb-4 flex-1 text-xl max-md:!order-1 p-3"} style={{ order: index % 2 === 0 ? 2 : 1 }}>
                <div
                  className="w-full flex max-md:!justify-start items-center"
                  style={{ justifyContent: index % 2 === 0 ? "start" : "end" }}
                >
                  <div className={clsx("w-12 h-[1px] bg-main hidden ", index % 2 === 0 && " !block")}></div>
                  <h2 className=" w-fit   px-0 py-1 text-2xl  max-md:text-sm  text-black ">{item.badge}</h2>
                  <div className={clsx("w-12 h-[1px] bg-main hidden", index % 2 === 1 && " !block")}></div>
                </div>
                <div                   className="w-full text-left "
                 >
                  {item.title && <p className="mb-7 mt-7">{item.title}</p>}
                </div>
              </div>

              <div
                style={{ order: index % 2 === 0 ? 1 : 2 }}
                className="prose prose-sm dark:prose-invert flex-1 text-sm flex flex-col"
              >
                {item?.image && (
                  <Image
                    src={item.image}
                    alt="blog thumbnail"
                    height="1000"
                    width="1000"
                    className="mb-10 object-cover"
                  />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </TracingBeam>
  );
}



export const TracingBeam = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
  });

  const contentRef = useRef<HTMLDivElement>(null);
  const [svgHeight, setSvgHeight] = useState(0);

  useEffect(() => {
    if (contentRef.current) {
      setSvgHeight(contentRef.current.offsetHeight);
    }
  }, []);

  const y1 = useSpring(useTransform(scrollYProgress, [0, 0.8], [50, svgHeight]), {
    stiffness: 500,
    damping: 90,
  });
  const y2 = useSpring(useTransform(scrollYProgress, [0, 1], [50, svgHeight + 3000]), {
    stiffness: 500,
    damping: 90,
  });

  return (
    <motion.div ref={ref} className={cn("relative mx-auto h-full w-full", className)}>
      <div className="absolute top-3 left-1/2 max-md:left-0">
        <motion.div
          transition={{
            duration: 0.2,
            delay: 0.5,
          }}
          animate={{
            boxShadow: scrollYProgress.get() > 0 ? "none" : "rgba(0, 0, 0, 0.24) 0px 3px 8px",
          }}
          className="border-netural-200 ml-[27px] flex h-4 w-4 items-center justify-center rounded-full border shadow-sm"
        >
          <motion.div
            transition={{
              duration: 0.2,
              delay: 0.5,
            }}
            animate={{
              backgroundColor: scrollYProgress.get() > 0 ? "white" : "var(--emerald-500)",
              borderColor: scrollYProgress.get() > 0 ? "white" : "var(--emerald-600)",
            }}
            className="h-2 w-2 rounded-full border border-neutral-300 bg-white"
          />
        </motion.div>
        <svg
          viewBox={`0 0 20 ${svgHeight}`}
          width="20"
          height={svgHeight} // Set the SVG height
          className="ml-4 block"
          aria-hidden="true"
        >
          <motion.path
            d={`M 1 0V -36 l 18 24 V ${svgHeight * 0.8} l -18 24V ${svgHeight}`}
            fill="none"
            stroke="#9091A0"
            strokeOpacity="0.16"
            transition={{
              duration: 10,
            }}
          ></motion.path>
          <motion.path
            d={`M 1 0V -36 l 18 24 V ${svgHeight * 0.8} l -18 24V ${svgHeight}`}
            fill="none"
            stroke="url(#gradient)"
            strokeWidth="1.25"
            className="motion-reduce:hidden"
            transition={{
              duration: 10,
            }}
          ></motion.path>
          <defs>
            <motion.linearGradient
              id="gradient"
              gradientUnits="userSpaceOnUse"
              x1="0"
              x2="0"
              y1={y1} // set y1 for gradient
              y2={y2} // set y2 for gradient
            >
              <stop stopColor="#18CCFC" stopOpacity="0"></stop>
              <stop stopColor="#18CCFC"></stop>
              <stop offset="0.325" stopColor="#6344F5"></stop>
              <stop offset="1" stopColor="#AE48FF" stopOpacity="0"></stop>
            </motion.linearGradient>
          </defs>
        </svg>
      </div>
      <div ref={contentRef}>{children}</div>
    </motion.div>
  );
};
