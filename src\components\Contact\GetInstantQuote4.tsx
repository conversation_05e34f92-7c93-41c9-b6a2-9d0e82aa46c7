"use client";
import { Button } from "antd";
import { ArrowRightOutlined } from "@ant-design/icons";
import React from "react";
import { useTranslations } from "next-intl";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";

const GetInstantQuoteButton = ({ className, text,product }: { text?: React.ReactNode; className?: string;product?:any }) => {
	const t = useTranslations();
	const { openModalInquiry } = useModalInquiryContext();

  
	return (
		<div className={`${className}`} onClick={() => openModalInquiry(false)}>
	<span className="underline" >{text}</span>
		</div>
	);
};

export default GetInstantQuoteButton;
