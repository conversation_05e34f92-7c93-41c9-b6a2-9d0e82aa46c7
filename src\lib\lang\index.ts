import path from "path";
import fs from "fs";

export  const returnLangs=()=>{
	const infoDict = {};
	const langDir = path.join(process.cwd(), 'src/lib/lang');
	const files = fs.readdirSync(langDir);
	for (const file of files) {
		if (file.endsWith('.json')) {
			const filePath = path.join(langDir, file);
			const fileContent = fs.readFileSync(filePath, 'utf-8');
			Object.assign(infoDict,JSON.parse(fileContent))
		}
	}

	return infoDict
}
