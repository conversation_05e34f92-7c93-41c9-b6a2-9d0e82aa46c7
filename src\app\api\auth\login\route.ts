import { NextResponse } from "next/server";
import { object, string } from "yup";
import { defaultLocale } from "@/config";
import { translateStaticProps } from "@/lib/utils/translate";
import { executeGraphQL } from "@/lib/utils/graphql";
import { UserLoginDocument } from "@/gql/graphql";
import { PASSWORD_LENGTH } from "@/lib/constant";
import { handleGraphqlLocale } from "@/lib/utils/util";

/**
 * 处理用户登录请求
 * 使用email和password进行用户身份验证，并返回认证令牌
 *
 * @param req - 请求对象，包含用户提交的email和password
 * @returns 返回一个包含认证结果的响应对象
 */
export const POST = async (req: Request) => {
	// 获取请求头中的首选语言，如果没有提供，则使用默认语言
	// const locale = req.headers.get("accept-language")?.split(",")[0] || defaultLocale;
	try {
		// 解析请求体，提取出email和password
		const body = (await req.json()) as {
			password: string;
			email: string;

		};
		// // 定义验证schema，用于校验请求体中的email和password
		// const schema = object({
		// 	password: string()
		// 		.required("{Password} is required")
		// 		.min(
		// 			PASSWORD_LENGTH.min,
		// 			`{new password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
		// 		)
		// 		.max(
		// 			PASSWORD_LENGTH.max,
		// 			`{new password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
		// 		),
		// 	email: string().email().required("{Email} is required"),
		// });
		// // 使用schema验证请求体，如果验证失败则抛出错误
		// await schema.validate(body);
		// 执行GraphQL查询，使用提供的email和password进行用户登录操作
		const resp = await executeGraphQL(UserLoginDocument, {
			variables: {
				email: body.email,
				password: body.password,
			},
		});
		// 检查登录响应中的错误，如果有错误则抛出异常
		if (resp.tokenCreate?.errors?.length) {
			throw resp.tokenCreate?.errors[0];
		}
		// 如果登录成功，返回包含认证令牌和其他相关信息的响应
		if (
			resp.tokenCreate?.token &&
			resp.tokenCreate?.user &&
			resp.tokenCreate?.csrfToken &&
			resp.tokenCreate?.refreshToken
		) {
			return NextResponse.json({
				code: 200,
				msg: "success",
				data: {
					token: resp.tokenCreate?.token,
					refreshToken: resp.tokenCreate?.refreshToken,
					csrfToken: resp.tokenCreate?.csrfToken,
					user: resp.tokenCreate?.user,
				},
			});
		}
	} catch (e: any) {
		// 如果在处理过程中发生异常，翻译错误消息并返回包含错误信息的响应
		let message: string = e?.message || "Error";
		// message = (await translateStaticProps([{ message }], ["message"], defaultLocale, body.locale))[0]
		// 	.message as string;
		return NextResponse.json({ code: 500, msg: message, data: e });
	}
};
