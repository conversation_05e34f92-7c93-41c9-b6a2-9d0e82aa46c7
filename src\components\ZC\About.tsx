"use client"
import { useTranslations,useLocale } from 'next-intl';
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
import { Link } from '@/navigation';
import { motion } from 'framer-motion';
import { Button } from '../Button';
import { defaultLocale } from '@/config';
export default function About() {
    const t = useTranslations('about');
    const locale = useLocale();

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2,
                delayChildren: 0.3,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.6,
                ease: "easeOut",
            },
        },
    };

    return (
        <section className="relative min-h-[550px] max-md:min-h-[350px] w-full flex items-center justify-center overflow-hidden">
            {/* Background Image with Dark Overlay */}
            <div className="absolute inset-0 w-full h-full">
                <SEOOptimizedImage
                    src="/image/home/<USER>"
                    alt="Pickleball background"
                    width={1920}
                    height={1080}
                    className="w-full h-full object-cover"
                    priority
                    // quality={100}
                    unoptimized
                />
                {/* <div className="absolute inset-0 bg-black/40"></div> */}
            </div>

            {/* Content */}
            <div className="relative z-10 container">
                <motion.div
                    className="max-w-[800px] text-left"
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                >
                    <motion.h1
                        className={`text-3xl max-md:text-xl  ${locale === defaultLocale ? "ib" : "font-semibold"} text-white uppercase tracking-wider`}
                        variants={itemVariants}
                    >
                        {t('values_title')}
                    </motion.h1>
                    <motion.p
                        className="mt-4 text-base text-white irs"
                        variants={itemVariants}
                    >
                        {t('about_intro')}
                    </motion.p>
                    <motion.div className="mt-8" variants={itemVariants}>
                        <Link href="/about-us" className="bg-[#83c000] rounded irs inline-block text-white  py-3 px-6 max-sm:py-2 max-sm:px-4 uppercase transition-colors duration-300 text-sm hover:text-white hover:bg-opacity-80">
                                {t('moreButton')}
                        </Link>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}