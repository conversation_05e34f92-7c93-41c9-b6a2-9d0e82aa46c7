"use client";
import { Placeholder } from "@/components/Placeholder";
import { BodyText } from "@/components/BodyText";
import { Link } from "@/navigation";
import Price from "@/components/Price/price";
import Calculator from "@/components/Calculator";
import React, { useEffect, useMemo, useState } from "react";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { getImagesForProduct } from "../imageGallery";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/Button";
import { defaultLocale } from "@/config";
import Image from "next/image";
function CartList({
	product,
	count = 1,
	isShowCount = false,
	close = true,
	showCol = false,
	handelupdateLinesCount,
	handelupdateCount,
}: {
	product: any;
	count: number;
	isShowCount?: boolean;
	close?: boolean;
	showCol?: boolean;
	handelupdateLinesCount: (id: string) => void;
	handelupdateCount: (id:string, count:number) =>Promise<void>;
}) {
	const {
		addToCart,
		findCheckout,
		findOrCreateCheckoutId,
		createCheckout,
		removeCartItem,
		updateLinesCount,
	} = useShoppingCart();
	const [countValue, setCountValue] = useState(count);
	const [isLoading, setIsLoading] = useState(false);
	let locale = useLocale();
	const t = useTranslations("shop");
	// product
	let [Reaproduct] = useState(product.variant.product);
	// variant
	let [Reavariant] = useState(product.variant);
	const imagesList = getImagesForProduct(Reaproduct) as any[];

	const changeCount = async (val: number) => {
		try {
			setIsLoading(true);
			setCountValue(val);
			await handelupdateCount(product.id, val);
		} catch (error) {
			// 如果更新失败，恢复原来的数量
			setCountValue(count);
			console.error('Failed to update quantity:', error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
			<div className="flex flex-col border bg-white p-4 shadow mb-4 rounded-sm">
				<>
					<div className="flex items-start p-3 border-b border-gray-100  mb-3">
						<div className="w-20 h-20 flex-shrink-0 rounded-md overflow-hidden">
							<Image 
								src={imagesList[0]?.url} 
								alt={Reaproduct.name} 
								width={80} 
								height={80} 
								className="w-full h-full object-cover"
							/>
						</div>
						<div className="ml-4 flex-1 flex flex-col">
							<Link href={`/product/${Reaproduct.slug}`} className="hover:text-primary-600 transition-colors">
								<h4 className="font-medium text-sm text-gray-900 line-clamp-2">
									{locale === defaultLocale ? Reaproduct.name : Reaproduct.translation.name||Reaproduct.name}
								</h4>
							</Link>

							<div className="mt-1.5 flex flex-wrap gap-2 items-center">
								<span className="inline-flex px-2.5 py-1 text-xs font-medium bg-gray-50 border border-gray-200 rounded-sm text-gray-700">
									{locale == defaultLocale ? Reavariant.name : Reavariant.translation.name||Reavariant.name}
								</span>
							</div>
							
							<div className="mt-auto pt-2 flex justify-between items-center">
								<Price price={Reavariant?.pricing?.price?.gross.amount * (product.quantity || 1)} className="font-semibold" />
							</div>
						</div>
					</div>
					<ul className="space-y-4 max-md:space-y-2">
						<li className="b-flex text-themeSecondary600">
							<BodyText size="md">{t("Quantity")}</BodyText>
							{isShowCount ? (
								<div className="flex items-center">
									<Calculator
										initCount={product.quantity}
										className="h-[30px] max-md:max-w-[40px]"
										changeCount={changeCount}
										disabled={isLoading}
                    maxCount={Reavariant.quantityAvailable}
									/>

								</div>
							) : (
								<BodyText size="md" className="line-clamp-1">
									{countValue}
								</BodyText>
							)}
						</li>

						{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
							<li className="b-flex text-themeSecondary600">
								<BodyText size="md">{t("Subtotal")}</BodyText>
								<Price price={product.totalPrice.gross.amount} size="md" className="" />
							</li>
						)}
					</ul>
				</>
			</div>
		</>
	);
}

export default React.memo(CartList);
