import {
	CheckoutAddPromoCodeDocument,
	CheckoutBillingAddressUpdateDocument,
	CheckoutDeliveryMethodUpdateDocument,
	CheckoutEmailUpdateDocument,
	CheckoutRemovePromoCodeDocument,
	CheckoutShippingAddressUpdateDocument,
	GetShippingMethodsDocument,
	OrderByidDocument,
	UpdateCheckoutShippingMethodDocument,
} from "@/gql/graphql";
import { executeGraphQL } from "@/lib/utils/graphql";
import { defaultLocale, handleGraphqlLocale } from "../utils/util";

// 快递地址地址
export const CheckoutAddAddress = async (data) => {
	// console.log(data,'data99');
	return await executeGraphQL(CheckoutShippingAddressUpdateDocument, {
		clientWithAuth: true,
		variables: {
			...data,
		},
	});
};
// 账单地址
export const CheckoutAddBillingAddress = async (data) => {
	// console.log(data,'data99');
	return await executeGraphQL(CheckoutBillingAddressUpdateDocument, {
		clientWithAuth: true,
		variables: {
			...data,
		},
	});
};

// 获取所有配送方式列表
export const GetShippingMethodlist = async (checkoutId): Promise<any> => {
	return await executeGraphQL(GetShippingMethodsDocument, {
		clientWithAuth: true,
		variables: {
			checkoutId,
		},
	});
};

// 添加配送方式到Checkout
export const UpdateCheckoutShippingMethod = async (checkoutId, shippingMethodId) => {
	return await executeGraphQL(UpdateCheckoutShippingMethodDocument, {
		clientWithAuth: true,
		variables: {
			checkoutId,
			shippingMethodId,
		},
	});
};

// 更新 checkout 配送方式的 mutation
export const UpdateCheckoutMutation = async (deliveryMethodId, checkoutId) => {
	return await executeGraphQL(CheckoutDeliveryMethodUpdateDocument, {
		clientWithAuth: true,
		variables: {
			deliveryMethodId,
			checkoutId,
		},
	});
};

// 根据OrderID 查询
export const GetOrderBuyID = async (orderId) => {
	return await executeGraphQL(OrderByidDocument, {
		clientWithAuth: true,
		variables: {
			orderId,
			locale: handleGraphqlLocale(defaultLocale),
		},
	});
};

// 根据添加优惠券
export const ADDPromoCode = async (checkoutId, promoCode) => {
	return await executeGraphQL(CheckoutAddPromoCodeDocument, {
		clientWithAuth: true,
		variables: {
			checkoutId,
			promoCode,
		},
	});
};

//删除优惠券
export const DELPromoCode = async (checkoutId, promoCodeId) => {
	return await executeGraphQL(CheckoutRemovePromoCodeDocument, {
		clientWithAuth: true,
		variables: {
			checkoutId,
			promoCodeId,
		},
	});
};

//更新checkeout Email
export const EmailUpdate = async (checkoutId, email) => {
	return await executeGraphQL(CheckoutEmailUpdateDocument, {
		clientWithAuth: true,
		variables: {
			checkoutId,
			email,
		},
	});
};
