"use client"
import React from 'react';
import { Button } from 'antd';
import { useTranslations } from "next-intl";

const MapAndDirections = () => {
	const t=useTranslations()
	return (
		<div className="bg-gray-100 py-12">
			<div className="container mx-auto px-4">
				{/* 标题部分 */}
				<div className="text-center mb-8">
					<h2 className="text-4xl font-bold">{t('contactUS.MapAndDirections')}</h2>
					<p className="text-lg mt-4">
						{t('contactUS.mapTip')}
					</p>
				</div>

				{/* 地图部分 */}
				<div className="w-full h-96 mb-8">

					<iframe
						src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d7355.338889582856!2d113.6630581600369!3d22.81470919350084!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3403bb7d61123fbf%3A0x7f2a56227cc63e65!2z5Lit5Zu95bm_5Lic55yB5Lic6I6e5biC6JmO6Zeo6ZWHIOmCruaUv-e8lueggTogNTIzOTAw!5e0!3m2!1szh-CN!2sus!4v1725800506690!5m2!1szen!2sus"
						className="w-full h-full border-0"
						allowFullScreen={false}
						loading="lazy"
						referrerPolicy="no-referrer-when-downgrade"
					></iframe>
				</div>


			</div>
		</div>
	);
};

export default MapAndDirections;
