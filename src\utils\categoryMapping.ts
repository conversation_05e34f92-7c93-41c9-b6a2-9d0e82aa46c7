// 分类映射工具函数
export const getCategoryDirectory = (categorySlug: string): string => {
  const categoryMap: { [key: string]: string } = {
    'paddles': '球拍',
    'quasar-series': '球拍',
    'nova-series': '球拍',
    'pulse-series': '球拍',
    'axis-series': '球拍',
    'usap-approved': '球拍',
    'ip-collection': '球拍',
    'social-set': '球拍',
    'ball': '球',
    'accessories': '其他',
    'paddle-accessories': '其他',
    'bags': '包',
    'apparel': '服装',
    'mens':'服装',
    'womens':'服装',
    'grips': '手胶',
    'headwear': '帽子',
    'towels': '毛巾',
    'cups': '杯子',
    'socks': '袜子',
    'weight-tape': '配重贴',
    'others': '其他'
  };
  return categoryMap[categorySlug] || '其他';
};

// 生成颜色图片路径
export const getColorImagePath = (colorValue: string, categorySlug: string): string | null => {
  if (!colorValue || !categorySlug) return null;

  console.log("Checking category:", categorySlug, "Color value:", colorValue);

  const categoryDir = getCategoryDirectory(categorySlug);
  const imagePath = `/image/var-filter/${categoryDir}/${colorValue}.png`;
  console.log("Generated image path:", imagePath);

  return imagePath;
};
