import { contactInfo, contactObj } from "@/lib/contacts";
import { <PERSON> } from "@/navigation";
import React from "react";
import Image from "next/image";
export default function Follow() {
	const data = [
		{ icon: "ri-facebook-fill", text: "Facebook", backgroundColor: "#365493", link: contactObj.FaceBook },
		{ icon: "ri-instagram-line", text: "Instagram", backgroundColor: "#fd334e", link: contactObj.instagram },
		{ icon: "ri-youtube-fill", text: "Youtube", backgroundColor: "#ff0000", link: contactObj.Youtube },
		{ icon: "ri-pinterest-fill", text: "Pinterest", backgroundColor: "#da0123", link: contactObj.Pinterest },
		{ icon: "ri-linkedin-fill", text: "LinkedIn", backgroundColor: "#0274b3", link: contactObj.Linkedin },
	];
	const jsonLd = {
		"@context": "https://schema.org",
		"@type": "Organization",
		name: process.env.NEXT_PUBLIC_COMPANY_NAME, // 你公司的名字
		url: process.env.NEXT_PUBLIC_SITE_URL, // 官网首页
		logo: `${process.env.NEXT_PUBLIC_SITE_URL}/image/logo-r.png`, // logo 链接
		sameAs: data.map((item) => item.link),
	};
	return (
		<>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(jsonLd),
				}}
			/>
			<div className="right-content flex items-center gap-3">
				{data.map((item, index) => (
					<Link
						aria-label={item.text}
						key={index}
						href={item.link}
						target="_blank"
						className="flex h-[34px] w-[34px] flex-col items-center  justify-center gap-2  rounded-full border-[1px] border-ddd text-ddd hover:!border-main hover:!text-white"
					>
						<i className={item.icon}></i>
					</Link>
				))}
			</div>
		</>
	);
}
