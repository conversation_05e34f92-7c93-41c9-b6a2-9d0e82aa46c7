import React from "react";
import { type Metadata } from "next";
import { type MyPageProps } from "@/lib/@types/base";
import { getBlogList } from "@/lib/api/blog";
import { type Blog } from "@/lib/@types/api/blog";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { unstable_setRequestLocale } from "next-intl/server";
import Banner from "@/components/Home/banner";
import Sponsor from "@/components/ZC/Sponsor";
import Category from "@/components/ZC/Category";
import { executeGraphQL } from "@/lib/graphql";
import { defaultLocale, handleGraphqlLocale } from "@/lib/utils/util";
import { ProductCategoriesDocument } from "@/gql/graphql";
import HomeProducts from "@/components/ZC/HomeProducts";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { fetchProductData, getCollectionProducts } from "@/lib/api/product";
import Partner from "@/components/ZC/Partner";
import PickleBallPaddleSeries from "@/components/ZC/PickleBallPaddleSeries";
import { CollectionListDocument } from "@/gql/graphql"
import About from "@/components/ZC/About";
import GetReadyToPlay from "@/components/ZC/GetReadyToPlay";
import PickleballAccessories from "@/components/ZC/PickleballAccessories";
import RcDifference from "@/components/ZC/RcDifference";
import PopularCollections from "@/components/ZC/PopularCollections";
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

// 异步获取博客列表，确保是一个 Promise
export async function fetchBlogList(
	Params: Blog.GetBlogListParams,
): Promise<{ blogList: Blog.BlogListItem[]; blogCount: number }> {
	try {
		const res = await getBlogList(Params);
		return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
	} catch (error) {
		console.error("Error fetching blog list:", error);
		return { blogList: [], blogCount: 0 }; // 如果发生错误，返回一个空数组
	}
}

export default async function Page(props: MyPageProps) {
	unstable_setRequestLocale(props.params.locale);

	//获取商品
	const channel = (await getChannelLanguageMap())[defaultLocale];
	// 请求集合数据，添加错误处理
	let collections = { edges: [] };
	try {
		const resultColletion = await executeGraphQL(CollectionListDocument, {
			withAuth: false, // 公共接口不需要认证
			variables: {
				first: 10,
				channel, // 替换成你的实际频道
				locale: handleGraphqlLocale(props.params.locale || defaultLocale)
			},
			revalidate: 60,
		});
		// 确保数据存在，否则使用默认值
		collections = resultColletion?.collections || { edges: [] };
	} catch (error) {
		console.error("Failed to fetch collections:", error);
		// 发生错误时使用空数组，确保不影响页面渲染
		collections = { edges: [] };
	}
	let pickleballAccessoriesData = { edges: [] };
	try {
		const pickleballAccessoriesResult = await getCollectionProducts({
			slug: "pickleball-accessories",
			locale: props.params.locale,
			channel,
		});
		console.log("pickleballAccessoriesResult=====", pickleballAccessoriesResult);
		pickleballAccessoriesData = pickleballAccessoriesResult?.collection?.products || { edges: [] };
	} catch (error) {
		console.error("Failed to fetch 'pickleball-accessories' collection:", error);
		pickleballAccessoriesData = { edges: [] };
	}

	return (
		<>
			<Banner />
			<PickleBallPaddleSeries collectionList={collections} channel={channel} locale={props.params.locale} />
			<About />
			<GetReadyToPlay />
			<PickleballAccessories data={pickleballAccessoriesData} locale={props.params.locale} />
			<RcDifference />
			<PopularCollections />
			<Partner />
		</>
	);
}
