'use client'
import React from "react";
import { useModalCustomerContext } from "@/context/CustomerContext";
import  Image from 'next/image'
const   CustomerBtn=({className}:{className?:string})=>{
	const { isCustomerModalOpen, openModalCustomer } = useModalCustomerContext();

	return (
		<>
				<button
					onClick={openModalCustomer}
          aria-label="CustomerBtn"
					className={`fixed bottom-10 max-md:bottom-20   left-3 bg-white   text-black  rounded-full shadow-mainShadow z-40    transition-all duration-300  group  cursor-pointer  hover:-translate-y-1.5 ${isCustomerModalOpen ?'opacity-0' :'opacity-100'}`}
				>
          <div className="h-[50px] cursor-pointer c-flex w-[50px] border text-white bg-white rounded-full shadow-md"><i className="ri-chat-smile-2-line ri-xl text-black"></i></div>
				</button>
		</>
	);
}

export  default  CustomerBtn
