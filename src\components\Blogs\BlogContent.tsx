"use client";
import { Blog } from "@/lib/@types/api/blog";
import BlogList from "./BlogList";
import { Pagination } from "antd";
import { useEffect, useState } from "react";
import { getBlogList } from "@/lib/api/blog";

// 异步获取博客列表，确保是一个 Promise
async function fetchBlogList(
	Params: Blog.GetBlogListParams,
): Promise<{ blogList: Blog.BlogListItem[]; blogCount: number }> {
	try {
		const res = await getBlogList(Params);
		return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
	} catch (error) {
		console.error("Error fetching blog list:", error);
		return { blogList: [], blogCount: 0 }; // 如果发生错误，返回一个空数组
	}
}
interface BlogsProps {
	blogLists: Blog.BlogListItem[];
	locale: string;
	search: string;
	currentPage: number;
	total: number;
	pageSize: number;
	params: any;
}
const BlogContent = ({
	params,
	blogLists,
	locale,
	search,
	currentPage,
	total,
	pageSize: initialPageSize,
}: BlogsProps) => {
	const [currentPageState, setCurrentPageState] = useState(currentPage);
	const [searchLoading, setSearchLoading] = useState(false);
	const [blogList, setBlogList] = useState<Blog.BlogListItem[]>(blogLists);
	const limit = 6;

	useEffect(() => {
		if (currentPageState === currentPage) {
			return; // 如果是初始页面，直接使用传入的数据
		}
		setSearchLoading(true);
		fetchBlogList({
			lang_code: { lang_code: params.locale },
			page: currentPageState,
			limit,
			cls_slug_in: [{ slug: params.slug }],
		})
			.then((res) => {
				setBlogList(res.blogList);
			})
			.finally(() => {
				setSearchLoading(false);
			});
	}, [currentPageState, currentPage]);

	const handlePageChange = (page: number) => {
		setCurrentPageState(page);
	};
	return (
		<>
			<BlogList blogList={blogList} />
			{total > 1 && (
				<div className="mt-6 flex w-full items-center justify-center md:mt-10">
					<Pagination
						current={currentPageState}
						total={total}
						pageSize={limit}
						onChange={handlePageChange}
						showSizeChanger={false}
					/>
				</div>
			)}
		</>
	);
};

export default BlogContent;
