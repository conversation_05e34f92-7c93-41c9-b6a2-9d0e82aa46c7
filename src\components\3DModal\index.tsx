"use client";
import React, { Suspense, useEffect, useRef } from "react";
import { Canvas, useLoader, use<PERSON>rame } from "@react-three/fiber";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
// @ts-ignore
import { STLLoader } from "three/examples/jsm/loaders/STLLoader";
import { Environment, Html, OrbitControls } from "@react-three/drei";
import * as THREE from "three";
import { Spin } from "antd";
import { chooseLoader } from "@/components/3DNormalModal";
import { LoadStep } from "@/lib/utils";

// 定义 Load3DModal 组件的 Props 类型
interface Load3DModalProps {
	className?: string;
	modalUrl: string;
}

// Load3DModal 组件
const Load3DModal: React.FC<Load3DModalProps> = ({ className, modalUrl }) => {
	return (
		<Canvas
			frameloop="demand"
			className={` ${className || ""}`}
			camera={{ position: [0, 20, -300], zoom: 5 }}
			onCreated={({ gl }) => {
				if (typeof window !== "undefined") {
					gl.setPixelRatio(window.devicePixelRatio);
				}
			}}
		>
			<ambientLight intensity={1.5} color={0xffffff} />
			<directionalLight position={[1, 1, 1]} intensity={0.5} />
			<Environment preset={null} background={false} path='/3DModals/' files={["st_fagans_interior_1k.hdr"]} />
			<Suspense fallback={  <Html center>
				<Spin size="large" /> {/* 使用 Ant Design 的 Spin 组件 */}
			</Html>}>
				<Model modalUrl={modalUrl} />
			</Suspense>
			<OrbitControls makeDefault />
		</Canvas>
	);
};

// Model 组件的 Props 类型
interface ModelProps {
	modalUrl: string;
}



// Model 组件
const Model: React.FC<ModelProps> = ({ modalUrl }) => {
			const loaderType = chooseLoader(modalUrl);

	const { scene } = useLoader(loaderType, modalUrl);
	const meshRef = useRef<THREE.Group | null>(null);

	useEffect(() => {
		scene.traverse(function (child: any) {
			if (child instanceof THREE.Mesh) {
				const material = child.material as THREE.Material;
				child.material = material.clone();
				child.material = new THREE.MeshStandardMaterial({
					color: 0x8a7611,
					metalness: 0.8,
					roughness: 0.1,
					envMapIntensity: 1,
				});
			}
		});
	}, [scene]);

	// 使用 useFrame 实现自动旋转
	useFrame((state, delta, frame) => {
		if (meshRef.current) {
			meshRef.current.rotation.y +=delta; // 控制旋转速度
		}
	});

	return <primitive object={scene} dispose={null} ref={meshRef} />;
};

export default Load3DModal;

