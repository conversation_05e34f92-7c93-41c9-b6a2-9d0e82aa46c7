'use client'

import React, { useState } from 'react'

import { useModalInquiryContext } from '@/context/ModalInquiryContext'
import ContactForm from "@/components/InquryForm/ContactForm";
import { CloseCircleOutlined } from "@ant-design/icons";
import { useTranslations } from 'next-intl';

const ModalInquiry = ({locale}:{locale:string}) => {
	const { isModalOpen, closeModalInquiry } = useModalInquiryContext();
  const t = useTranslations();

	return (
		<section className="">
			<div className={`modal-wishlist-block !flex !justify-center items-center `} onClick={closeModalInquiry}>
				<div
					className={`modal-wishlist-main !static !w-fit   max-md:mt-10    ${isModalOpen ? 'open' : ''}`}
					onClick={(e) => {
						e.stopPropagation()
					}}>

					<div className="relative    !overflow-y-auto box-border p-8 "  >
            <h2 className='text-2xl ml-6 mb-3 max-md:text-xl'>{t('form.RequestAQuote')}</h2>
						<ContactForm locale={locale} className="!py-0"></ContactForm>
						<div className="absolute right-5 top-5  cursor-pointer" onClick={closeModalInquiry}>
							<CloseCircleOutlined className="text-xl text-main hover:text-main" />
						</div>
					</div>
				</div>
			</div>
		</section>
	)
}

export default ModalInquiry
