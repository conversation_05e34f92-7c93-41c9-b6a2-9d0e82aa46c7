import { contactInfo } from "@/lib/contacts";
import React, { useEffect, useState } from "react";

const Sidebar = () => {
	const [activeIndex, setActiveIndex] = useState<number | null>(null);
	const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(true);
	const [isVisible, setIsVisible] = useState<boolean>(false);

	// const items = [
	// 	{ icon: "ri-facebook-fill", link: "https://www.facebook.com/profile.php?id=" },
	// 	{ icon: "ri-instagram-line", link: "https://www.instagram.com/" },
	// 	{ icon: "ri-youtube-fill", link: "https://www.youtube.com/channel/" },
	// 	{ icon: "ri-pinterest-fill", link: "https://www.pinterest.com/" },
	// 	{ icon: "ri-linkedin-fill", link: "https://www.linkedin.com/in/" },
	// 	{ icon: "ri-whatsapp-fill", link: "https://wa.me/8615088991103" },
	// ];

  const items = [
		{ icon: "/image/WhatsApp6.png",text:'WhatsApp', link: `https://wa.me/${contactInfo.whatsapp}` },
		{ icon: "/image/WhatsApp6.png",text:'WhatsApp', link:`https://wa.me/${contactInfo.whatsapp}` },
		{ icon: "/image/WhatsApp6.png",text:'WhatsApp', link: `https://wa.me/${contactInfo.whatsapp}` },
		{ icon: "/image/WhatsApp6.png",text:'WhatsApp', link: `https://wa.me/${contactInfo.whatsapp}` },
		{ icon: "/image/WhatsApp6.png",text:'WhatsApp', link: `https://wa.me/${contactInfo.whatsapp}`},
	];
 

	// 添加滚动监听器
	useEffect(() => {
		const handleScroll = () => {
			const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
			if (scrollTop > 1000) {
				setIsVisible(true);
			} else {
				setIsVisible(false);
			}
		};

		window.addEventListener("scroll", handleScroll);

		return () => {
			window.removeEventListener("scroll", handleScroll);
		};
	}, []);
	return isVisible ? (
		<div className={`sidebar-container max-md:hidden ${isSidebarOpen ? "open" : "closed"}`}>
			<div className={`sidebar ${isSidebarOpen ? "open" : "closed"}`}>
				{items.map((item, index) => (
					<a
          
						key={index}
						href={item.link}
						target="_blank"
						rel="noopener noreferrer"
						className={`sidebar-item box-border p-1  cursor-pointer  ${items.length != index+1 ? "border-b-[1px] border-[#979797]" : ""}`}
						onMouseEnter={() => setActiveIndex(index)}
						onMouseLeave={() => setActiveIndex(null)}
					>
						<img src={item.icon} alt="11" className="w-[32px] h-[32px] object-cover relative !top-1" />
            <span className="!text-[12px] !text-[#2b2b2b] hover:!text-main scale-[0.8] mixBlendmode">{item.text}</span>
					</a>
				))}
				{/* <div className="close-button" onClick={() => setIsSidebarOpen(false)}>
					<i className="ri-close-line"></i>
				</div> */}
			</div>
			{/* {!isSidebarOpen && (
				<div className="open-button" onClick={() => setIsSidebarOpen(true)}>
					<i className="ri-skip-right-line"></i>
				</div>
			)} */}
		</div>
	) : null;
};

export default React.memo(Sidebar);
