'use client'

import React, { createContext, useContext, useState, type ReactNode } from 'react';

interface ModalCustomerContextProps {
	children: ReactNode;
}

interface ModalCustomerContextValue {
	isCustomerModalOpen: boolean;
	openModalCustomer: () => void;
	closeModalCustomer: () => void;
}

const ModalCustomerContext = createContext<ModalCustomerContextValue | undefined>(undefined);

export const useModalCustomerContext = (): ModalCustomerContextValue => {
	const context = useContext(ModalCustomerContext);
	if (!context) {
		throw new Error('useModalCustomerContext must be used within a ModalSearchProvider');
	}
	return context;
};

export const ModalCustomerProvider: React.FC<ModalCustomerContextProps> = ({ children }) => {
	const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);

	const openModalCustomer = () => {
		setIsCustomerModalOpen(true);
	};

	const closeModalCustomer = () => {
		setIsCustomerModalOpen(false);
	};

	const contextValue: ModalCustomerContextValue = {
		isCustomerModalOpen,
		openModalCustomer,
		closeModalCustomer,
	};

	return (
		<ModalCustomerContext.Provider value={contextValue}>
			{children}
		</ModalCustomerContext.Provider>
	);
};
