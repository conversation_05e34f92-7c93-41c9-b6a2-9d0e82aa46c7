import { Subscribe } from "../@types/api/subscribe";


const baseUrl=process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL
export const    subscribeEmail=async (params:Subscribe.subscribeForm)=>{

	const url = `${baseUrl}/website/email_subscribe`;
	const res = (await fetch(url, {
		method: "POST",
		body: JSON.stringify({
			...params,
		}),
		headers: {
			"Content-Type": "application/json"
		}
	}).then((r) => r.json())) as Subscribe.BaseResp<any>;
	if (res.code === 200) {
		return res;
	} else {
		throw new Error(res.message);
	}
}
