import { getChannelLanguageMap, getChannelList } from "@/lib/api/channel";
import { Channel } from "@/lib/@types/api/channel";

export async function fetchChannelLangsData() {
	/* 同步获取频道语言映射和频道列表 */
	const channelPromise = await Promise.allSettled([
		getChannelLanguageMap(), // 获取语言与频道的映射
		getChannelList(),
	]);
	/* 解析频道语言映射和频道列表的结果 */
	const channelLanguageMap = channelPromise[0].status === "fulfilled" ? channelPromise[0].value : null;
	const channelList = channelPromise[1].status === "fulfilled" ? channelPromise[1].value : null;
	/* 初始化频道语言映射对象 */
	let languageMapChannel: Channel.ILanguageMapChannel = {};
	/* 如果频道语言映射和频道列表都存在，则构建频道语言映射 */
	if (channelList && channelLanguageMap) {
		/* 遍历频道语言映射 */
		Object.keys(channelLanguageMap).map((k) => {
			/* 遍历频道列表 */
			Array.isArray(channelList.channels) &&
				channelList.channels.map((item: any) => {
					/* 如果频道slug与语言映射匹配，则添加到频道语言映射中 */
					if (item.slug === channelLanguageMap[k as keyof typeof channelLanguageMap]) {
						languageMapChannel[k] = item;
					}
				});
		});
	}

	return { channelLanguageMap, channelList, languageMapChannel };
}
