"use client"
import React, { useEffect, useRef, useState } from 'react';
import { Button } from 'antd';
import GetInstantQuoteButton from "@/components/Contact/GetInstantQuote";

const ContactBanner = () => {
	const [isVisible, setIsVisible] = useState(false);
	const ref = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const observer = new IntersectionObserver(
			(entries) => {
				const entry = entries[0];
				if (entry.isIntersecting) {
					setIsVisible(true);
					observer.disconnect(); // 动画触发后停止观察
				}
			},
			{
				root: null, // 使用浏览器视窗为根
				threshold: 0.1, // 当元素 10% 可见时触发
			}
		);

		if (ref.current) {
			observer.observe(ref.current);
		}

		return () => {
			if (ref.current) {
				observer.unobserve(ref.current);
			}
		};
	}, []);

	return (
		<div className="bg-black text-white">
			<div
				ref={ref}
				className=" grid grid-cols-1 md:grid-cols-2 items-center "
			>
				{/* 左侧图片部分 */}
				<div className={`relative ${isVisible ? 'animate__animated animate__fadeInLeft' : 'opacity-0'}`}>
					<img
						src="/image/contact/bg.png"
						alt="Contact Us"
						className="w-full h-[550px] max-md:h-[350px] object-top object-cover"
					/>
				</div>

				{/* 右侧文本和按钮部分 */}
				<div className={`pl-8 ${isVisible ? 'animate__animated animate__fadeInRight' : 'opacity-0'}`}>
					<h2 className="text-4xl font-bold mb-6">
						Get in Touch With Us <br/>Today For All Your<br/> Manufacturing Needs!
					</h2>
					<div className="inline-block">
						<GetInstantQuoteButton />
					</div>

				</div>
			</div>
		</div>
	);
};

export default ContactBanner;
