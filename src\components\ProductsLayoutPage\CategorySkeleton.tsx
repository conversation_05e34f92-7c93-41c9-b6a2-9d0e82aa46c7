"use client"
import React, { useState, useEffect } from 'react';

const CategorySkeleton = () => {
  // 根据屏幕宽度决定显示的骨架屏数量
  const [skeletonCount, setSkeletonCount] = useState(6);

  useEffect(() => {
    const updateSkeletonCount = () => {
      const width = window.innerWidth;
      if (width >= 1280) {
        setSkeletonCount(7); // 对应大屏幕显示6个
      } else if (width >= 1024) {
        setSkeletonCount(4); // 对应中等屏幕显示4个
      } else if (width >= 768) {
        setSkeletonCount(3); // 对应平板显示3个
      } else {
        setSkeletonCount(2); // 对应手机显示2个
      }
    };

    updateSkeletonCount();
    window.addEventListener('resize', updateSkeletonCount);
    return () => window.removeEventListener('resize', updateSkeletonCount);
  }, []);

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4 mb-10">
      {[...Array(skeletonCount)].map((_, index) => (
        <div key={index} className="relative bg-gray-100 rounded-lg overflow-hidden">
          <div className="aspect-[1/1] bg-gray-200" />
        </div>
      ))}
    </div>
  );
};

export default CategorySkeleton; 