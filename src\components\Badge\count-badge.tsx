import React from "react";

function CountBadge({count = 0}) {
    return <>
        {
            count > 0 && <span className="absolute -top-full -right-[60%] p-0.5 bg-linksColor rounded-full text-white
                                    inline-flex items-center  text-xs font-medium ring-1 ring-inset ring-gray-500/10 bg-[#d53a3d] justify-center w-[18px] h-[18px] !text-[10px]">
                                        {count > 99 ? 99 : count}
                                    </span>
        }
    </>;
}

export default React.memo(CountBadge);
