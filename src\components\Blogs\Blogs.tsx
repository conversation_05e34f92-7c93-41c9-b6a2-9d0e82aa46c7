"use client";
import BlogList from "@/components/Blogs/BlogList";
import React, { useEffect, useState } from "react";
import { Pagination, Spin } from "antd";
import type { Blog } from "@/lib/@types/api/blog";
import { getBlogList } from "@/lib/api/blog";

interface BlogsProps {
	blogLists: Blog.BlogListItem[];
	locale: string;
	search: string;
	currentPage: number;
	total: number;
	pageSize: number;
}

const Blogs = ({ blogLists, locale, search, currentPage, total, pageSize: initialPageSize }: BlogsProps) => {
	const [blogList, setBlogList] = useState<Blog.BlogListItem[]>(blogLists);
	const [count, setCount] = useState(total);
	const [currentPageState, setCurrentPageState] = useState(currentPage);
	const [searchLoading, setSearchLoading] = useState(false);
	const limit = 6;

	const fetchBlogList = async (
		Params: Blog.GetBlogListParams,
	): Promise<{ blogList: Blog.BlogListItem[]; blogCount: number }> => {
		// setSearchLoading(true);
		try {
			const res = await getBlogList(Params);
			// setSearchLoading(false);
			return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
		} catch (error) {
			// setSearchLoading(false);
			console.error("Error fetching blog list:", error);
			return { blogList: [], blogCount: 0 };
		}
	};

	const handlePageChange = (page: number) => {
		if (page !== currentPageState) {
			setCurrentPageState(page);
		}
	};

	useEffect(() => {
		// 当搜索条件或页码改变时重新获取数据
		fetchBlogList({
			lang_code: { lang_code: locale },
			page: currentPageState,
			limit,
			blog_title: search || "",
		}).then((res) => {
			if (res) {
				setBlogList(res.blogList);
				setCount(res.blogCount);
			}
		});
	}, [currentPageState, search, locale]);

	return (
		<div className="left flex justify-center xl:w-3/4 xl:pr-2">
			{searchLoading ? (
				<Spin size="large" />
			) : (
				<section className="w-full">
					<BlogList blogList={blogList} />
					{count > 0 && (
						<div className="list-pagination mt-6 flex w-full items-center justify-center md:mt-10">
							<Pagination
								current={currentPageState}
								total={count}
								pageSize={limit}
								onChange={handlePageChange}
								showSizeChanger={false}
							/>
						</div>
					)}
				</section>
			)}
		</div>
	);
};

export default React.memo(Blogs);
