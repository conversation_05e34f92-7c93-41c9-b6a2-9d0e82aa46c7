"use client";
import React, { useState } from "react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { Form, Input, Button, Upload, Radio, App } from "antd";
import { InboxOutlined } from "@ant-design/icons";
import { touristUploadFile } from "@/lib/api/media"; // 确保这个路径是正确的
import { useSessionStore } from "@/lib/store/session";
import { sendForm } from "@/lib/api/form";
import { useTranslations } from "next-intl";
import { useCategory } from "@/context/CategoriesContext";
import { defaultLocale } from "@/config";
import clsx from "clsx";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";

import { get_inquiry_blocking_keyword } from "@/lib/api/common";

const { TextArea } = Input;
export async function getBlockKeys() {
	try {
		return await get_inquiry_blocking_keyword();
	} catch (e) {
		console.log(e);
		return { content_blocking_keyword_list: [], email_blocking_keyword_list: [] };
	}
}
const SolutionContactForm = ({
	title,
	slug,
	children,
	className,
	locale,
	innerClassName,
	inquiry,
	source,
	cls,
}: {
	slug?: string;
	children?: React.ReactNode;
	title?: string;
	className?: string;
	locale: string;
	innerClassName?: string;
	inquiry?: any[] | null;
	source?: string | null;
	cls?: string | null;
}) => {
	const [loadingBtn, setLoadingBtn] = useState(false);
	const { session_uuid } = useSessionStore();
	const [form] = Form.useForm();
	const t = useTranslations();
	const { message } = App.useApp();
    const { is3d ,Preloaded3DObj} = useModalInquiryContext();

	const onFinish = async (values: any) => {
		// 获取屏蔽关键词
		const blockObj = await getBlockKeys();
		const { content_blocking_keyword_list, email_blocking_keyword_list } = blockObj;
		// 检查是否包含屏蔽关键词的通用函数
		const containsBlockedKeyword = (input: string | undefined, keywords: string[]): boolean => {
			// 处理空输入或空关键词列表
			if (!input || !keywords.length) return false;
			return keywords.some((key) => input.includes(key));
		};

		// 验证内容和邮箱
		const isContentInvalid =
			containsBlockedKeyword(values.message, content_blocking_keyword_list) ||
			containsBlockedKeyword(values.email, email_blocking_keyword_list);
		if (isContentInvalid) {
			message.error(t("form.contentisnotstandardized"));
			return;
		}

		try {
            const sendObj = { source:is3d ? 'inquiry' : (source || 'contact-us'), type: 0, chart: "" };
			const inquiry_info: any = {
				form: [],
				contact_method: values.email && values.mobile ? 3 : 1,
				products: [],
				html: `<h1>您的网站 <a href="${process.env.NEXT_PUBLIC_SITE_URL}" target="_blank">${process.env.NEXT_PUBLIC_SITE_URL}</a> 有新询盘，请前往网站后台查看详细内容！</h1>
<h2>询盘信息</h2>
<p style="padding: 5px 10px;border:1px solid #ccc;border-radius: 10px;margin: 10px;">名字：${values.name}</p>
<p style="padding: 5px 10px;border:1px solid #ccc;border-radius: 10px;margin: 10px;">公司：${values.company}</p>
<p style="padding: 5px 10px;border:1px solid #ccc;border-radius: 10px;margin: 10px;">邮箱：<a href='mailto:${values.email}' target="_blank"> ${values.email}</a></p>
<p style="padding: 5px 10px;border:1px solid #ccc;border-radius: 10px;margin: 10px;">手机号：<a href='https://wa.me/${values.mobile}' target="_blank"> ${values.mobile}</a></p>
<p style="padding: 5px 10px;border:1px solid #ccc;border-radius: 10px;margin: 10px;">服务类型：${values.Service}</p>
<p style="padding: 5px 10px;border:1px solid #ccc;border-radius: 10px;margin: 10px;">留言：${values.message}</p>
`,
			};
            if(is3d){
                inquiry_info['TDTextures']=Preloaded3DObj.Textures,
                inquiry_info.form.push(
                  {label: 'inquiry', type: 'inquiry', value:Preloaded3DObj.inquiryData}
                  )
              }
			Object.keys(values).forEach((key) => {
				if (key === "message") {
					inquiry_info.form.push({ label: key, type: "textarea", value: values[key] });
					inquiry_info[key] = values[key];
				} else if (key === "Upload") {
					if (values[key] && values[key].length > 0) {
						const list: any[] = [];
						values[key].forEach((item: any) => {
							list.push({
								file_id: item.response.detail.file_ret[0][item.name],
								file_size: item.size,
								file_type: item.type,
								file_name: item.name,
							});
						});
						inquiry_info["files"] = list;
						inquiry_info.form.push({ label: key, type: "files", value: list });
					}
				} else {
					inquiry_info.form.push({ label: key, type: "input", value: values[key] });
					inquiry_info[key] = values[key];
				}
			});

			// 如果有商品
			if (inquiry) {
				inquiry_info.form.push({ label: "inquiry", type: "inquiry", value: inquiry });
			}

			console.log(inquiry_info, "inquiry_info", sendObj);

			sendObj.chart = JSON.stringify({ inquiry_info });
			setLoadingBtn(true);
			const response = (await sendForm(sendObj)) as any;
			if (response && response.code === 200) {
				void message.success(t("form.sendSuccess"));
			} else {
				void message.error(response?.message || t("form.sendFail"));
			}
		} catch (error) {
			message.error(t("form.sendFail"));
		}
		setLoadingBtn(false);
	};

	const onFinishFailed = (errorInfo: any) => {
		console.error("Failed:", errorInfo);
		message.error(t("Error.inqFillTip"));
	};

	const normFile = (e: any) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e?.fileList;
	};

	const handleUpload = async (options: any) => {
		const { file, onSuccess, onError } = options;
		try {
			const response = await touristUploadFile({
				upload_list: [file],
				session_uuid: session_uuid, // 这里需要替换为实际的 session_uuid
			});
			if (response && response.code === 200) {
				message.success(t("form.fileUploadSuccess"));
				onSuccess(response, file);
			} else {
				message.error(response?.message || t("form.fileUploadFail"));
				onError(new Error(response?.message || t("form.fileUploadFail")));
			}
		} catch (error) {
			message.error(t("form.fileUploadFail"));
			onError(error);
		}
	};

	// const t2 = useTranslations(`${slug}-faq`);
	const { categories } = useCategory();

	return (
		<div className={` bg-white py-10 ${className}`}>
			<div
				className={`animate__animated animate__fadeIn  container flex w-full gap-x-5 rounded-xl bg-white   max-md:flex-col ${innerClassName}`}
			>
				<div className=" w-full overflow-y-auto  bg-white ">
					{/* <div className={`mb-8 ${title ? "text-left" : "text-center"} `}>
                        <h2 className={clsx("text-4xl font-bold max-md:text-2xl",cls)} >{title || t("menu.contactUs")}</h2>
                        
                    </div> */}
					<Form
						form={form}
						layout="vertical"
						onFinish={onFinish}
						onFinishFailed={onFinishFailed}
						initialValues={{ remember: true, variant: "filled" }}
					>
						<div className="grid grid-cols-1 gap-6 max-md:gap-0 md:grid-cols-2 ">
							<Form.Item
								name={t("form.name")}
								// label={t("form.name")}
								rules={[{ required: true, message: t("form.pleaseName") }]}
								className="capitalize"
							>
								<Input placeholder={t("form.nameTip")} className="!rounded-sm p-2" />
							</Form.Item>
							<Form.Item
								name={t("form.company")}
								// label={t("form.company")}
								rules={[{ required: true, message: t("form.pleaseCompany") }]}
								className="capitalize"
							>
								<Input placeholder={t("form.companyTip")} className="!rounded-sm p-2" />
							</Form.Item>
						</div>
						<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
							<Form.Item
								name={t("form.email")}
								// label={t("form.email")}
								rules={[
									{ required: true, message: t("form.pleaseEmail") },
									{ type: "email", message: t("form.vailEmailTip") },
								]}
								className="capitalize"
							>
								<Input placeholder={t("form.emailTip")} />
							</Form.Item>
							<Form.Item
								name={t("form.phone")}
								// label={t("form.phone")}
								rules={[{ required: true, message: t("form.pleasePhone") }]}
								className="capitalize"
							>
								<PhoneInput
									country={"us"}
									enableSearch
									inputStyle={{
										width: "100%",
										borderRadius: "0.375rem",
										borderColor: "#d1d5db",
									}}
									buttonStyle={{
										borderRadius: "0.375rem 0 0 0.375rem",
										borderColor: "#d1d5db",
									}}
								/>
							</Form.Item>
						</div>
						{/* <Form.Item name={t("menu.service")} label={t("menu.service")} className="capitalize">
                            <Radio.Group className="!grid !grid-cols-3 gap-2">
                                {categories?.edges.map((cate) => {
                                    return (
                                        <Radio value={cate.node.name} key={cate.node.id}>
                                            {" "}
                                            {locale === defaultLocale
                                                ? cate.node.name
                                                : cate.node.translation
                                                    ? cate.node.translation.name
                                                    : cate.node.name}{" "}
                                        </Radio>
                                    );
                                })}
                                <Radio value="Others"> {t("base.Others")} </Radio>
                            </Radio.Group>
                        </Form.Item> */}
						<Form.Item
							name={t("form.message")}
							// label={t("form.message")}
							rules={[{ required: true, message: t("form.pleaseMessage") }]}
							className="capitalize"
						>
							<TextArea rows={4} placeholder={t("form.messageTip")} />
						</Form.Item>

						<Form.Item>
							<Button
								type="primary"
								htmlType="submit"
								className="mt-4 w-full !rounded-sm !bg-[main] !bg-black !py-4 text-white"
								loading={loadingBtn}
							>
								{t("base.Submit")}
							</Button>
						</Form.Item>
					</Form>
				</div>
				{children}
			</div>
		</div>
	);
};

export default SolutionContactForm;
