import React from "react";
import BlogList from "@/components/Blogs/BlogList";
import type { Blog } from "@/lib/@types/api/blog";
import { Link } from "@/navigation";
import { Empty } from "antd";
import { getTranslations } from "next-intl/server";

// 生成博客列表的结构化数据
const generateBlogListStructuredData = (blogList: Blog.BlogListItem[]) => {
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "Blog",
        "name": "Blog List",
        "blogPost": blogList.map(blog => ({
            "@type": "BlogPosting",
            "headline": blog.blog_title,
            "description": blog.blog_excerpt,
            "image": blog.blog_cover_type ? {
                "@type": "ImageObject",
                "url": blog.blog_cover_origin,
                "width": 600,
                "height": 350,
                "caption": blog.blog_title
            } : {
                "@type": "VideoObject",
                "contentUrl": blog.blog_cover_origin,
                "thumbnailUrl": blog.blog_thumb,
                "name": blog.blog_title,
                "description": blog.blog_excerpt
            },
            "datePublished": blog.blog_upload_time,
            "author": {
                "@type": "Organization",
                "name": process.env.NEXT_PUBLIC_COMPANY_NAME
            },
            "publisher": {
                "@type": "Organization",
                "name": process.env.NEXT_PUBLIC_COMPANY_NAME
            },
            "url": `/blog/${blog.blog_slug}`,
            "keywords": blog.blog_tag_list?.map(tag => tag.tag_name).join(", "),
            "articleSection": blog.blog_classification_list?.map(cls => cls.cls_name).join(", ")
        }))
    };
    
    return structuredData;
};

// 服务端分页组件
async function BlogPagination({
	currentPage,
	totalCount,
	pageSize,
	locale,
	searchQuery,
	basePath = "/blog",
}: {
	currentPage: number;
	totalCount: number;
	pageSize: number;
	locale: string;
	searchQuery: string;
	basePath?: string;
}) {
	const t = await getTranslations("common");
	const totalPages = Math.ceil(totalCount / pageSize);
	const pages = [];

	// 生成页码数组
	const startPage = Math.max(1, currentPage - 2);
	const endPage = Math.min(totalPages, currentPage + 2);

	for (let i = startPage; i <= endPage; i++) {
		pages.push(i);
	}

	const buildUrl = (page: number) => {
		const params = new URLSearchParams();
		if (page > 1) params.set("page", page.toString());
		if (searchQuery) params.set("search", searchQuery);
		const queryString = params.toString();
		// Link组件会自动处理语言代码，所以这里不需要手动添加locale
		return `${basePath}${queryString ? `?${queryString}` : ""}`;
	};

	if (totalPages <= 1) return null;

	return (
		<div className="list-pagination mt-6 flex w-full items-center justify-center md:mt-10">
			<nav className="flex items-center space-x-2">
				{/* 上一页 */}
				{currentPage > 1 && (
					<Link
						href={buildUrl(currentPage - 1)}
						className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 hover:border-main transition-colors hover:text-black"
					>
						← {t("previouspage")}
					</Link>
				)}

				{/* 第一页 */}
				{startPage > 1 && (
					<>
						<Link
							href={buildUrl(1)}
							className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:text-black hover:bg-gray-50 hover:border-main transition-colors"
						>
							1
						</Link>
						{startPage > 2 && <span className="px-2 py-2 text-sm text-gray-500">...</span>}
					</>
				)}

				{/* 页码 */}
				{pages.map((page) => (
					<Link
						key={page}
						href={buildUrl(page)}
						className={`rounded-md border px-3 py-2 text-sm font-medium transition-colors ${
							page === currentPage
								? "border-main bg-main text-white hover:text-white"
								: "border-gray-300 bg-white text-gray-500 hover:bg-gray-50 hover:border-main hover:text-black"
						}`}
					>
						{page}
					</Link>
				))}

				{/* 最后一页 */}
				{endPage < totalPages && (
					<>
						{endPage < totalPages - 1 && <span className="px-2 py-2 text-sm text-gray-500">...</span>}
						<Link
							href={buildUrl(totalPages)}
							className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:text-black hover:bg-gray-50 hover:border-main transition-colors"
						>
							{totalPages}
						</Link>
					</>
				)}

				{/* 下一页 */}
				{currentPage < totalPages && (
					<Link
						href={buildUrl(currentPage + 1)}
						className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium hover:text-black text-gray-500 hover:bg-gray-50 hover:border-main transition-colors"
					>
						{t("nextpage")} →
					</Link>
				)}
			</nav>
		</div>
	);
}

interface BlogsServerProps {
    blogList: Blog.BlogListItem[];
    totalCount: number;
    currentPage: number;
    limit: number;
    searchTerm: string;
    locale: string;
    basePath?: string;
}

const BlogsServer: React.FC<BlogsServerProps> = ({
    blogList,
    totalCount,
    currentPage,
    limit,
    searchTerm,
    locale,
    basePath = "/blog"
}) => {
    // 如果没有博客数据，显示空状态
    if (!blogList || blogList.length === 0) {
        return (
            <div className="left xl:w-3/4 xl:pr-2 flex justify-center">
                <section className="w-full">
                    <Empty />
                </section>
            </div>
        );
    }

    // 生成结构化数据
    const structuredData = generateBlogListStructuredData(blogList);

    return (
        <div className="left xl:w-3/4 xl:pr-2 flex justify-center">
            {/* 博客列表结构化数据 */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(structuredData)
                }}
            />
            
            <section className="w-full">
                <BlogList blogList={blogList} />
                
                {totalCount > limit && (
                    <BlogPagination
                        currentPage={currentPage}
                        totalCount={totalCount}
                        pageSize={limit}
                        locale={locale}
                        searchQuery={searchTerm}
                        basePath={basePath}
                    />
                )}
            </section>
        </div>
    );
};

export default BlogsServer; 