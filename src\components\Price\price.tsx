"use client";

import { useProductStore } from "@/lib/store/product.store";
import { formatPrice } from "@/lib/utils";


type Props = {
  price: string | number;
  className?: string;
  size?: "xs" | "sm" | "md" | "lg" | "xl";
}
export default function Price(props: Props) {
  const { price, className = "", size = "md" } = props;
  const { currencyUnit } = useProductStore();
  if (+price <= 0) return null;
  // const pce = price?.toString().startsWith(currencyUnit) ? price : `$${formatPrice(price.toString())}`;
  // 修改为固定美元符号判断
  const pce = price?.toString().startsWith('$') ? price : `$${formatPrice(price.toString())}`;

  return <span className={`${className} text-${size} text-main`}>
    {pce}
  </span>;
}
