"use client";
import React, { useEffect, useState } from 'react';
import { HomeTaile } from "@/components/Contact/ConcatPage";
import { useTranslations } from 'next-intl';
import { Select } from 'antd';
import useIsMobile from '@/lib/hooks/useIsMobile';
import { useBreakpointColumns } from '@/lib/store/breakpointColumnsObj';
import BreadcrumbBanner from "@/components/AboutPage/breadcrumb-banner";
import { usePathname } from '@/navigation';
import { defaultLocale } from '@/config';
import { Link } from '@/navigation';
import { ProductCategoriesListDocument } from "@/gql/graphql";
import { executeGraphQL } from '@/lib/graphql';
import { handleGraphqlLocale } from '@/lib/utils/util';
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
import { motion } from 'framer-motion';
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// 导入抽离的组件
import CategorySkeleton from './CategorySkeleton';
import ViewSwitcher from './ViewSwitcher';
import FilterDrawer from './FilterDrawer';
import FilterBar from './FilterBar';
import CategoryCarousel from './CategoryCarousel';
import ProductBreadcrumb from './ProductBreadcrumb';
// 添加不需要请求的slug映射
const EMPTY_CATEGORY_SLUGS = [
  'nova-series',
  'axis-series',
  'pulse-series',
  'quasar-series',
  'ip-collection',
  'usap-approved',
  'social-set',
  'paddle-accessories',
  'eyewear',
  "jewelry",
  "headwear",
  "others",
  "bags",
  "mens",
  "womens",
  "ball"
];

// 添加需要显示筛选按钮的slug映射
export const FILTER_ENABLED_SLUGS = [
  'paddle',
  'social-set',
  'nova-series',
  'axis-series',
  'pulse-series',
  'quasar-series',
  'ip-collection',
  'usap-approved',
  'accessories',
  // 'paddle-accessories',
  // 'eyewear',
  // 'jewelry',
  // 'headwear',
  // 'others',
  // 'bags',
  'apparel',
  // 'mens',
  // 'womens'
];

function Index({ children, channel, locale, categoriesData }) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);
  const pathname = usePathname()
  const [Loading, setLoading] = useState<boolean>(false)
  const [showFilter, setShowFilter] = useState(false);

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  let isMobile = useIsMobile();
  let { changeBreakpointColumns, col, setcol } = useBreakpointColumns();

  function changeBreakpointColumnsObj(col: number) {
    changeBreakpointColumns(col)
    setcol(col)
  }

  // 监听屏幕尺寸变化，在移动端下强制使用两列布局
  useEffect(() => {
    const handleResize = () => {
      const isMobileView = window.innerWidth < 768; // 小于768px视为移动端
      if (isMobileView && col !== 2) {
        changeBreakpointColumnsObj(2);
      }
    };

    // 初始化时运行一次
    handleResize();

    // 添加resize事件监听
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => window.removeEventListener('resize', handleResize);
  }, [col]);

  const [pageTitle, setPageTitle] = useState(t('common.Products'));
  const productsPathMatch = pathname.match(/\/products\/([^/]+)/);
  const [secondCategories, setSecondCategories] = useState<any>(null)
  const [parentCategory, setParentCategory] = useState<any>(null); // 添加父分类状态

  //获取当前二级分类
  const fetchProductCategoriesList = async () => {
    try {
      // 获取当前路径中的 slug
      const pathParts = pathname.split('/');
      const currentSlug = pathParts[pathParts.length - 1];
      // 检查是否在不需要请求的slug列表中
      if (EMPTY_CATEGORY_SLUGS.includes(currentSlug)) {
        // setSecondCategories([]);
        setLoading(false);
      } else {
        setLoading(true);
      }
      const res = await executeGraphQL(ProductCategoriesListDocument, {
        withAuth: false,
        variables: { channel, locale: handleGraphqlLocale(locale), first: 100 },
        revalidate: 60,
      });

      // 在所有分类中查找匹配的 slug
      const matchedCategory = res.categories.edges.find(edge =>
        edge.node.slug === currentSlug
      );

      // 扁平化所有分类以便于查找父子关系
      let allCategories = [];
      const categoryParentMap = new Map(); // 用于存储子分类到父分类的映射

      const flattenCategories = (categories, parent = null) => {
        if (!categories) return;

        categories.forEach(category => {
          if (category && category.node) {
            allCategories.push(category.node);
            
            // 如果有父分类，记录映射关系
            if (parent) {
              categoryParentMap.set(category.node.slug, parent);
            }

            // 递归处理子分类
            if (category.node.children &&
              category.node.children.edges &&
              category.node.children.edges.length > 0) {
              flattenCategories(category.node.children.edges, category.node);
            }
          }
        });
      };

      flattenCategories(res.categories.edges);

      if (matchedCategory) {
        console.log("matchedCategory----", matchedCategory);
        setSecondCategories(matchedCategory.node.children.edges);
        // 这是一级分类，将父分类设为null
        setParentCategory(null);
      } else {
        setSecondCategories([]);
        
        // 检查当前slug是否有父分类
        const parent = categoryParentMap.get(currentSlug);
        if (parent) {
          setParentCategory(parent);
        } else {
          setParentCategory(null);
        }
      }

    } catch (err) {
      console.log(err);
      setSecondCategories([]);
      setParentCategory(null);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchProductCategoriesList()
  }, [pathname]) // 当路径变化时重新获取数据

  // 检查当前slug是否应该显示筛选按钮
  useEffect(() => {
    if (productsPathMatch && productsPathMatch[1]) {
      const currentSlug = productsPathMatch[1];
      setShowFilter(FILTER_ENABLED_SLUGS.includes(currentSlug));
    } else {
      setShowFilter(false);
    }
  }, [pathname, productsPathMatch]);

  useEffect(() => {
    console.log("categoriesData----", categoriesData)
    // 检查当前路径是否为产品分类页面

    if (productsPathMatch && productsPathMatch[1]) {
      const slug = productsPathMatch[1];

      // 从 categoriesData 数据中查找匹配的 slug
      if (categoriesData && categoriesData.edges && categoriesData.edges.length > 0) {
        // 扁平化所有分类节点以便于查找
        let allCategories = [];
        const flattenCategories = (categories) => {
          if (!categories) return;

          categories.forEach(category => {
            if (category && category.node) {
              allCategories.push(category.node);

              // 递归处理子分类
              if (category.node.children &&
                category.node.children.edges &&
                category.node.children.edges.length > 0) {
                flattenCategories(category.node.children.edges);
              }
            }
          });
        };

        flattenCategories(categoriesData.edges);

        // 查找匹配的分类
        const matchedCategory = allCategories.find(cat =>
          cat.slug === slug ||
          (cat.translation && cat.translation.slug === slug)
        );

        if (matchedCategory) {
          // 如果找到匹配的分类，使用其名称作为标题
          const categoryName = locale === defaultLocale ? matchedCategory.name : matchedCategory.translation ?
            matchedCategory.translation.name :
            (matchedCategory.name || t('common.Product'));

          setPageTitle(categoryName);
        } else {
          // 如果在 categoriesData 中找不到匹配的分类，使用默认标题
          setPageTitle(t('common.Products'));
        }
      } else {
        setPageTitle(t('common.Products'));
      }
    } else {
      // 如果不是产品分类页面，使用默认标题
      setPageTitle(t('common.Products'));
    }
  }, [pathname, t, categoriesData, productsPathMatch]);


  return (
    <>
      {productsPathMatch ? (
        <BreadcrumbBanner
          title={pageTitle}
          textPosition="left"
        />
      ) : (
        <HomeTaile msg={t('common.Products')} />
      )}
      <section className="pb-10 md:pb-20">
        <div className="container">
          {/* 面包屑导航 */}
          <ProductBreadcrumb
            parentCategory={parentCategory}
            currentTitle={pageTitle}
            locale={locale}
            isProductsPage={!!productsPathMatch}
          />
          {/* <div>
            {Loading ? (
              <CategorySkeleton />
            ) : secondCategories && (
              <>
                <CategoryCarousel categories={secondCategories} locale={locale} />
              </>
            )}
          </div> */}
          {/* 筛选栏 */}
          <FilterBar
            showFilter={showFilter}
            showDrawer={showDrawer}
            col={col}
            changeBreakpointColumnsObj={changeBreakpointColumnsObj}
            isMobile={isMobile}
          />

          <div className="flex justify-between gap-y-12 max-xl:flex-col">
            <div className="flex flex-1 flex-col">{children}</div>
          </div>
        </div>
      </section>

      {/* 筛选抽屉 */}
      <FilterDrawer
        open={open}
        onClose={onClose}
        isMobile={isMobile}
        secondCategories={secondCategories}
        showSubcategoryFilter={
          !parentCategory && secondCategories && secondCategories.length > 0 && productsPathMatch && FILTER_ENABLED_SLUGS.includes(productsPathMatch[1])
        }
      />

    </>
  );
}

export default React.memo(Index);
