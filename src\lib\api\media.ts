import { type BaseMicroservicesResponse } from "@/lib/@types/api/base";
import { type MediaType } from "@/lib/@types/api/media";

const baseUrl = process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL;

// 用户上传文件
export const touristUploadFile = async ({
	upload_list,
	session_uuid,
}: {
	upload_list: File[];
	session_uuid: string;
}) => {
	const formData = new FormData();
	upload_list.forEach((file) => {
		formData.append("upload_list", file);
	});

	formData.append("session_uuid", session_uuid);
	return (await fetch(`${baseUrl}/medias/material_upload`, {
		method: "POST",
		body: formData,
	}).then((r) => r.json())) as BaseMicroservicesResponse<MediaType.TouristUploadFileResp>;
};
