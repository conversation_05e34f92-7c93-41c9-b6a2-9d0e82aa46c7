"use client";
import { getOrderList } from "@/lib/api/order";
import React, { useEffect, useState } from "react";
import { Table, Card, Tag, Space, Button, message, Pagination, Empty, Modal } from "antd";
import { useTranslations } from "next-intl";
import { useProductStore } from "@/lib/store/product.store";
import { 
  FileTextOutlined,    // 草稿
  ClockCircleOutlined, // 未确认
  InboxOutlined,       // 未发货
  SyncOutlined,        // 部分发货
  RollbackOutlined,    // 部分退货
  UndoOutlined,        // 已退货
  CheckCircleOutlined, // 已发货
  CloseCircleOutlined, // 已取消
  StopOutlined,
  QuestionCircleOutlined,        // 已过期
} from '@ant-design/icons';
import moment from "moment";

  // 添加解析media的函数
 export const parseProductMedia = (mediaString: string) => {
    try {
      return JSON.parse(mediaString);
    } catch (e) {
      return [];
    }
  };
function OrderViews({ order }: { order: any }) {
  const t = useTranslations();
  const { currencyUnit } = useProductStore();
  const [newOrder, setNewOrder] = useState(order);

  // 订单状态映射
  const orderStatusMap = {
    DRAFT: { 
      color: '#8c8c8c',
      bgColor: '#f5f5f5',
      text: t('order.Draft'),
      icon: <FileTextOutlined />
    },
    UNCONFIRMED: { 
      color: '#faad14',
      bgColor: '#fff7e6',
      text: t('order.Unconfirmed'),
      icon: <ClockCircleOutlined />
    },
    UNFULFILLED: { 
      color: '#fa8c16',
      bgColor: '#fff2e8',
      text: t('order.Unfulfilled'),
      icon: <InboxOutlined />
    },
    PARTIALLY_FULFILLED: { 
      color: '#1890ff',
      bgColor: '#e6f7ff',
      text: t('order.Partially Fulfilled'),
      icon: <SyncOutlined spin />
    },
    PARTIALLY_RETURNED: { 
      color: '#faad14',
      bgColor: '#fff7e6',
      text: t('order.Partially Returned'),
      icon: <RollbackOutlined />
    },
    RETURNED: { 
      color: '#ff4d4f',
      bgColor: '#fff1f0',
      text: t('order.Returned'),
      icon: <UndoOutlined />
    },
    FULFILLED: { 
      color: '#52c41a',
      bgColor: '#f6ffed',
      text: t('order.Fulfilled'),
      icon: <CheckCircleOutlined />
    },
    CANCELED: { 
      color: '#ff4d4f',
      bgColor: '#fff1f0',
      text: t('order.Canceled'),
      icon: <CloseCircleOutlined />
    },
    EXPIRED: { 
      color: '#8c8c8c',
      bgColor: '#f5f5f5',
      text: t('order.Expired'),
      icon: <StopOutlined />
    }
  } as const;
  




  return (
    <div className="py-2 text-left">
      {/* 订单基本信息 */}
      {/* 订单基本信息 */}
      <div className="border-b border-gray-200 pb-4">
        <div className="text-themeGray py-3">
          {/* 修改这里，移除嵌套的 p 标签 */}
          <div>
            {t("order.Order")} <span className="text-main font-bold">#{newOrder.node.id}</span>{" "}
          </div>
          
          <div>
            {t("order.e6719024a8d3944cb6b903393784ad6bfbc4")}{" "}
          </div>
          <div>
            <span className="text-main font-bold">
              {moment(newOrder.node.created).format("YYYY-MM-DD HH:mm:ss")}
            </span>

            <span className="mx-2">{t("order.727d48a64d986a4b16aab4144948355af1ae")}</span>
            
            <span className="text-main font-bold">{orderStatusMap[newOrder.node.status].text}</span>
          </div>
        </div>
      </div>


      {/* 收货地址信息 */}
      <div className="mt-4 border-b border-gray-200 pb-4">
        <h3 className="font-bold mb-2">{t("order.Shipping Address")}</h3>
        <div className="bg-gray-50 p-4 rounded">
          <p className="mb-2">
            <span className="font-medium">{t("order.Recipient")}:</span>{" "}
            {newOrder.node.shippingAddress.firstName} {newOrder.node.shippingAddress.lastName}
          </p>
          <p className="mb-2">
            <span className="font-medium">{t("order.Company")}:</span>{" "}
            {newOrder.node.shippingAddress.companyName}
          </p>
          <p className="mb-2">
            <span className="font-medium">{t("order.Address")}:</span>{" "}
            {newOrder.node.shippingAddress.country.country}{" "}
            {newOrder.node.shippingAddress.countryArea}{" "}
            {newOrder.node.shippingAddress.city}{" "}
            {newOrder.node.shippingAddress.streetAddress1}
          </p>
          <p>
            <span className="font-medium">{t("order.Postal Code")}:</span>{" "}
            {newOrder.node.shippingAddress.postalCode}
          </p>
        </div>
      </div>

      {/* 订单商品信息 */}
      <div className="mt-4 border-b border-gray-200 pb-4">
        <h3 className="font-bold mb-2">{t("order.Order Items")}</h3>
        <div className="space-y-4">
        {newOrder.node.lines.map((line: any) => {
  const mediaList = parseProductMedia(line.variant.product.media);
  const subtotal = (line.quantity * parseFloat(line.totalPrice.gross.amount)).toFixed(2);
  
  return (
    <div key={line.id} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
      <div className="flex gap-6 max-md:flex-col">
        {/* 商品图片 */}
        <div className="shrink-0">
          {mediaList && (
            <div className="relative w-24 h-24 rounded-lg overflow-hidden">
              <img 
                src={mediaList[0]?.url||'/image/default-image.webp'} 
                alt={'wewr'}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
          )}
        </div>

        {/* 商品信息 */}
        <div className="flex-1">
          <div className="flex justify-between items-start mb-3">
            <h4 className="text-lg font-medium text-gray-800 hover:text-blue-600 transition-colors">
              {line.variant.product.name}
            </h4>
            <div className="text-right">
              <div className="text-lg font-semibold text-blue-600">
                {currencyUnit} {subtotal}
              </div>
              <div className="text-sm text-gray-500">
                {currencyUnit} {line.totalPrice.gross.amount} × {line.quantity}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-3 items-center max-md:hidden">
            {/* 商品ID */}
            <div className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
            {t("order.ID")}: {line.variant.product.id}
            </div>

            {/* 数量标签 */}
            <div className="flex items-center gap-1.5 bg-blue-50 text-blue-600 px-3 py-1 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
              </svg>
              <span className="text-sm font-medium">
                {t("order.Quantity")}: {line.quantity}
              </span>
            </div>

            {/* 状态标签 */}
            <Tag 
              color="processing"
              className="rounded-full px-3 py-1"
              icon={<CheckCircleOutlined />}
            >
              {t("order.In Stock")}
            </Tag>
          </div>

          {/* 可选：添加更多商品信息或操作按钮 */}
          {/* <div className="mt-3 flex gap-3 max-md:hidden">
            <Button type="text" size="small" className="text-gray-500 hover:text-blue-600">
              <FileTextOutlined /> {t("order.View Details")}
            </Button>
            <Button type="text" size="small" className="text-gray-500 hover:text-blue-600">
              <QuestionCircleOutlined /> {t("order.Support")}
            </Button>
          </div> */}
        </div>
      </div>
    </div>
  );
})}
        </div>
      </div>

      {/* 价格汇总 */}
      <div className="mt-4">
        {
          newOrder.node.shippingPrice.gross.amount>0&&<div className="flex justify-between items-center py-2">
          <span>{t("order.Subtotal")}:</span>
          <span>{currencyUnit} {newOrder.node.shippingPrice.gross.amount}</span>
        </div>
        }
       
        <div className="flex justify-between items-center py-2 font-bold">
          <span>{t("order.Total")}:</span>
          <span className="text-main">
            {currencyUnit} {newOrder.node.totalCharged.amount}
          </span>
        </div>
      </div>
    </div>
  );
}
export default OrderViews