"use client"
import React from 'react';
import { useTranslations,useLocale } from 'next-intl';
import { defaultLocale } from '@/config';

interface ViewSwitcherProps {
  col: number;
  changeBreakpointColumnsObj: (col: number) => void;
}

const ViewSwitcher = ({ col, changeBreakpointColumnsObj }: ViewSwitcherProps) => {
  const t = useTranslations();
  const locale = useLocale();
  return (
    <div className="flex items-center">
      <span className={`text-sm whitespace-nowrap mr-2 ${locale === defaultLocale ? "ib" : "font-semibold"}`}>{t('common.View')}:</span>
      <div className="flex items-center overflow-hidden">
        <button 
          onClick={() => changeBreakpointColumnsObj(4)} 
          className={`px-1 py-1 max-md:hidden`}
          aria-label="4 columns"
        >
          <div className="flex gap-0.5">
            {[...Array(4)].map((_, index) => (
              <div key={index} className={`w-2 h-4 ${col === 4 ? 'bg-black' : 'bg-gray-400'}`}></div>
            ))}
          </div>
        </button>
        <button 
          onClick={() => changeBreakpointColumnsObj(3)} 
          className={`px-1 py-1 max-sm:hidden`}
          aria-label="3 columns"
        >
          <div className="flex gap-0.5">
            {[...Array(3)].map((_, index) => (
              <div key={index} className={`w-2 h-4 ${col === 3 ? 'bg-black' : 'bg-gray-400'}`}></div>
            ))}
          </div>
        </button>
        <button 
          onClick={() => changeBreakpointColumnsObj(2)} 
          className={`px-1 py-1`}
          aria-label="2 columns"
        >
          <div className="flex gap-0.5">
            {[...Array(2)].map((_, index) => (
              <div key={index} className={`w-2 h-4 ${col === 2 ? 'bg-black' : 'bg-gray-400'}`}></div>
            ))}
          </div>
        </button>
      </div>
    </div>
  );
};

export default ViewSwitcher; 