"use client";
import clsx from "clsx";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import React from "react";
type Props = {
	text?: string;
  description?: string;
	className?: string;
	children?: React.ReactNode;
  link?: string;
  bottomText?: string;

};
function Index({ text = "", className = "", children,link="/products",description,bottomText='' }: Props) {
    const t = useTranslations();
	return (
		<div className={clsx(className)}>
			<div className=" animate__animated  animate__fadeInUp  text-center flex flex-col items-center justify-center ">
				<div className="mb-6">
					{children}
				</div>

        {
          text&&<h2 className="mb-4 text-4xl font-normal max-md:text-2xl text-black">{text}</h2>
        }

	
  {
    description&&<p className="mb-8 max-w-lg text-gray-600">
    {description}
  </p>
  }

				
        {/* <Link href={link} className="inline-block border border-gray-300 px-8 py-3 text-black transition-colors hover:text-white hover:bg-black">
        
        {bottomText||t('nav.Return_to_shop')}
					</Link> */}

			</div>
		</div>
	);
}

export default React.memo(Index);
