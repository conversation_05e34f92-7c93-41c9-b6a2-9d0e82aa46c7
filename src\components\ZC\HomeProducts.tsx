"use client";
import React, { useState, useCallback, useEffect } from "react";
import { defaultLocale } from "@/config";
import { useLocale, useTranslations } from "next-intl";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";
import Svg from "../Header/Menu/Svg";
import clsx from "clsx";
import { IconLove } from "../Product/product-card";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css"; // 需要引入样式
import  Image from 'next/image'
import { Tooltip } from "antd";
import { useCompareStore } from "@/lib/store/Compare.store";

import CompareList from "../CompareList";
import {useRouter  } from "@/navigation";
import useIsMobile from "@/lib/hooks/useIsMobile";
function Index({ productData }) {
	const [products] = useState(productData.products.edges);
	const locale = useLocale();
	const t = useTranslations("nav");
  let router=useRouter()

	// 对比

	let { compareIds, show, changeShow, setcCmpareProducts } = useCompareStore();
	const [isOpen, setIsOpen] = useState(false);
	const [imageUrl, setimageUrl] = useState(""); // 替换为你的图片 URL

	// 动画配置
	const containerHoverVariants = {
		hidden: { opacity: 0 },
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.1, // 子元素依次延迟显示
				delayChildren: 0.2, // 动画整体延迟
			},
		},
	};

	const iconVariants = {
		hidden: { opacity: 0, y: 20 },
		visible: { opacity: 1, y: 0 },
	};

	const handleImageHover = useCallback(
		(setIndex) => (enter) => {
			setIndex(enter ? 1 : 0);
		},
		[],
	);

	const renderPrice = (price) => {
		if (!price?.start || !price?.stop) return null;
		return (
			<div className="flex items-center space-x-2">
				<span className="text-gray-400 line-through">
					{price.start.gross.currency} {price.start.gross.amount}
				</span>
				<span className="font-medium text-red-600">
					{price.stop.gross.currency} {price.stop.gross.amount}
				</span>
			</div>
		);
	};
	// 添加对比
	const addOrDelCompare = (event: React.MouseEvent, product) => {
		event.stopPropagation();
		console.log(product, "product");

		const id = product?.id;
		if (id) {
			setcCmpareProducts(id);
      changeShow(true);
		}

	};

	return (
		<div className="w-full py-[60px]">
			<div className="mx-auto">
				<h2 className="mb-4 text-center text-4xl font-medium">{t("PickleballPaddle")}</h2>
				<p className="mb-12 text-center text-gray-600 ">{t("Choose from")}</p>
				<motion.div
					initial="hidden"
					whileInView="visible"
					viewport={{ once: true, amount: 0.5 }}
					variants={containerVariants}
					className="grid grid-cols-3 gap-4 max-md:grid-cols-2"
				>
					{products.map((productItem) => {
						const pt = productItem.node.media;
						const imgs =  JSON.parse(pt) || [];
						const [currentImageIndex, setCurrentImageIndex] = useState(0);

						const [isHovered, setIsHovered] = useState(false); // 控制动画容器状态
						const handleMouseEnter = () => {
							setIsHovered(true);
							setCurrentImageIndex(1); // 切换到第二张图片
						};
						const handleMouseLeave = () => {
							setIsHovered(false);
							setCurrentImageIndex(0); // 切换回第一张图片
						};
            let ProductName=locale === defaultLocale
										? productItem.node.name
										: productItem.node.translation?.name || productItem.node.name

						return (
							<motion.div
								key={productItem.node.id}
								variants={itemVariants}
								className="list relative border-[1px] border-[#ececec] p-[33px] max-md:p-2"
								onMouseEnter={handleMouseEnter}
								onMouseLeave={handleMouseLeave}
							>
								<div className="relative mb-5 overflow-hidden">
									{currentImageIndex===0?<Image
										width={1000}
										height={1000}
										quality={70}
										onClick={() => router.push(`/product/${productItem.node.slug}`)}
										alt={ProductName || "Product image unavailable"}
										className="w-full transition-all transform  duration-700 ease-in-out hover:scale-105 max-md:rounded-sm"
										src={imgs[0]?.url || "/image/default-image.webp"}
									/>:		<Image
										width={1000}
										height={1000}
										quality={70}
										onClick={()=>router.push(`/product/${productItem.node.slug}`)}
										alt={ProductName || "Product image unavailable"}
										className="w-full transform transition-all duration-700 ease-in-out hover:scale-105 max-md:rounded-sm"
										src={imgs[1]?.url || "/image/default-image.webp"}
									/>}
									{/* 动画容器 */}
									<motion.div
										className="absolute bottom-[10px] left-0 flex w-full items-center justify-center gap-x-3"
										initial="hidden"
										animate={isHovered ? "visible" : "hidden"} // 动画状态绑定到 isHovered
										variants={containerHoverVariants}
									>
										{/* //爱心 */}
										<Tooltip title={t("Like")}>
											<motion.span
												className="boxShadow flex h-[42px] w-[42px] cursor-pointer items-center justify-center rounded-sm bg-white "
												variants={iconVariants} // 子元素动画
											>
												<IconLove product={productItem.node} />
											</motion.span>
										</Tooltip>

										{/* //对比 */}
										<div className="max-md:hidden">
											<Tooltip title={t("Add to Compare")}>
												<motion.span
													className="boxShadow flex h-[42px] w-[42px] cursor-pointer items-center justify-center rounded-sm bg-white "
													variants={iconVariants} // 子元素动画
												>
													<div onClick={(e) => addOrDelCompare(e, productItem.node)}>
														{compareIds.includes(productItem.node.id) ? (
															<i className={clsx("ri-check-line ri-xl")}></i>
														) : (
															<Svg isScrolled={isHovered} />
														)}
													</div>
												</motion.span>
											</Tooltip>
										</div>

										{/* //查看 */}
										<Tooltip title={t("Quick View")}>
											<motion.span
												className="boxShadow flex h-[42px] w-[42px] cursor-pointer items-center justify-center rounded-sm bg-white "
												variants={iconVariants} // 子元素动画
											>
												<i
													className={clsx("ri-eye-line ri-xl")}
													onClick={(event) => {
                            event.stopPropagation();
														setimageUrl(imgs[currentImageIndex]?.url || "/image/default-image.webp");
														setIsOpen(true);
													}}
												></i>
											</motion.span>
										</Tooltip>
									</motion.div>
								</div>
								{/* 产品名称 */}
								<h2  onClick={()=>router.push(`/product/${productItem.node.slug}`)} className="cursor-pointer max-md:text-md mb-2 text-lg font-medium max-md:line-clamp-2">
									{ProductName}
								</h2>
								{/* 产品价格 */}
								{renderPrice(productItem.node.pricing?.priceRange)}
							</motion.div>
						);
					})}
				</motion.div>
			</div>
			{isOpen && <Lightbox mainSrc={imageUrl} onCloseRequest={() => setIsOpen(false)} />}

			<div className="max-md:hidden">
				<CompareList />
			</div>
		</div>
	);
}

 export default React.memo(Index);
