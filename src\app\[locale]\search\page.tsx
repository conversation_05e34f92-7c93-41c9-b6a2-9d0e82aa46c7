import { notFound } from "next/navigation";
import ProductList from "@/components/ProductList/ProductList";
import { defaultLocale } from "@/config";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { fetchSearchProductsData, searchProducts } from "@/lib/api/product";
import { getTranslations } from "next-intl/server";
import SearchProducts from "@/components/SearchProducts";

export default async function Page({
	searchParams,
	params,
}: {
	searchParams: Record<"query" | "cursor", string | string[] | undefined>;
	params: { channel: string; locale: string };
}) {
	const searchValue = searchParams.query as string;
	const { locale } = params;

	const channel = (await getChannelLanguageMap())[defaultLocale];
	if (!searchValue) {
		notFound();
	}

	const page = "1";
	let limit = "9";

	const { detail } = (await searchProducts({ searchValue, page: page, limit: limit })) as any;

	const ids = detail?.res?.map((item) => item.product_id) || [];
	const { products } = await fetchSearchProductsData({ ids: ids, locale, channel});

	// console.log(products, "products");

	return (
		<>
			<SearchProducts
				products={products}
				page={page}
				locale={locale}
				searchValue={searchValue}
				channel={channel}
				total={detail.totalCount}
				limit={limit}
			/>
		</>
	);
}
