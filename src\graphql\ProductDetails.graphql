query ProductDetails(
	$slug: String!
	$channel: String!
	$locale: LanguageCodeEnum!
	$filterKeywordKey: String!
) {
	product(slug: $slug, channel: $channel) {
		id
		name
		slug
		seoTitle
		seoDescription
		description
		seoKeywords: metafield(key: $filterKeywordKey)
		translation(languageCode: $locale) {
			name
			descriptionJson
			seoTitle
			seoDescription
		}
		rating
		isAvailable
		defaultVariant {
			id
		}
		productType {
			isShippingRequired
		}
		collections {
			name
			slug
			id
			translation(languageCode: $locale) {
				name
			}
		}
		metadata {
			key
			value
		}
		descriptionJson
		mp4: metafield(key: "cover_mp4")
		media: metafield(key: "media")
		threeModel: metafield(key: "threeModel")
		threeModeCustoms: metafield(key: "threeModel_customs")
		category {
			id
			name
			slug
			translation(languageCode: $locale) {
				name
			}
		}
		attributes {
			attribute {
				name
				translation(languageCode: $locale) {
					name
				}
			}
			values {
				name
				value
				translation(languageCode: $locale) {
					name
				}
				inputType
				file {
					contentType
					url
				}
			}
		}
		variants {
			...ProductDetailVariantItem
		}
		pricing {
			priceRange {
				start {
					gross {
						amount
						currency
					}
				}
				stop {
					gross {
						amount
						currency
					}
				}
			}
		}
	}
}

fragment ProductDetailVariantItem on ProductVariant {
	quantityAvailable
	sku
	media: metafield(key: "media")
	weight {
		unit
		value
	}
	attributes {
		attribute {
			name
			translation(languageCode: $locale) {
				name
			}
		}
		values {
			inputType
			file {
				url
				contentType
			}
			name
			translation(languageCode: $locale) {
				name
			}
		}
	}
	pricing {
		price {
			tax {
				amount
				currency
			}
			net {
				amount
				currency
			}
			gross {
				amount
				currency
			}
		}
	}
	translation(languageCode: $locale) {
		name
	}
	name
	id
}
