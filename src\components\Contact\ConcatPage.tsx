
"use client"
import { contactInfo, contactObj, FOL<PERSON><PERSON> } from '@/lib/contacts';
import { useTranslations } from 'next-intl';
import React from 'react'
import ContactForm from '../InquryForm/ContactForm';
import clsx from 'clsx';
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
import { Link } from "@/navigation";
import { motion } from 'framer-motion';
import Image from 'next/image';
import { YoutubeOutlined } from '@ant-design/icons'

export default function ConcatPage({ locale }: { locale: string }) {
  const t = useTranslations('nav');

  const navigation = {
    contact: [
      { title: t('Address'), value: `${contactInfo.address}`, hrefs: contactInfo.addressLink },
      // { title: t('Phone'), value: `${contactInfo.phone}` },
      { title: t('Email'), value: `${contactInfo.email}`, hrefs: `mailto:${contactInfo.email}` },
    ],
  };

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const slideInLeft = {
    hidden: { x: -100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const slideInRight = {
    hidden: { x: 100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="bg-[#F2F4F5] min-h-screen">
      <HomeTaile msg={t("Contact Us")} />

      {/* 地图和信息面板 */}
      <div className='w-full bg-white'>
        <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 min-h-[600px] py-[64px] px-4">
          {/* 左侧地图 */}
          <motion.div
            className="w-full h-[400px] lg:h-[600px]"
            variants={slideInLeft}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3408.9149926137675!2d121.50760507707984!3d31.306094557694735!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x35b273bf146f82a5%3A0x7463805522c3621f!2s51%20Zhengxue%20Rd%2C%20Yang%20Pu%20Qu%2C%20Shang%20Hai%20Shi%2C%20China%2C%20200439!5e0!3m2!1sen!2s!4v1751882293667!5m2!1sen!2s"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
            ></iframe>
          </motion.div>

          {/* 右侧信息面板 */}
          <motion.div
            className="bg-white p-8 lg:p-12 flex flex-col justify-center"
            variants={slideInRight}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.h3
              className="text-[#333] text-[28px] mb-8 ib"
              variants={itemVariants}
            >
              {t('Visit Our Store')}
            </motion.h3>

            <motion.div
              className="space-y-6"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {navigation.contact.map((item, index) => (
                <motion.div
                  key={index}
                  className="space-y-1"
                  variants={itemVariants}
                >
                  <div className="text-[#333] font-medium text-[16px]">{item.title}</div>
                  <Link href={item.hrefs} className="text-gray-400 text-[14px] hover:text-main irs" target="_blank">{item.value}</Link>
                </motion.div>
              ))}

              {/* 社交媒体图标 */}
              <motion.div
                className="flex gap-2 flex-wrap"
                variants={itemVariants}
              >
                {
                  FOLLOW.map((item, index) => (
                    <motion.div
                      key={item.id}
                      className="group"
                      initial={{ scale: 0, opacity: 0 }}
                      whileInView={{ scale: 1, opacity: 1 }}
                      viewport={{ once: true }}
                      transition={{
                        duration: 0.4,
                        ease: "easeOut"
                      }}
                    >
                      <div className="relative w-[28px] h-[28px]">
                        <Image
                          src={item.image}
                          width={40}
                          height={40}
                          alt={item.name}
                          className="transition-opacity duration-200 group-hover:opacity-0 w-full h-full object-contain"
                        />
                        <Image
                          src={item.image_hover}
                          width={40}
                          height={40}
                          alt={item.name}
                          className="absolute top-0 left-0 transition-opacity duration-200 opacity-0 group-hover:opacity-100 w-full h-full object-contain"
                        />
                      </div>
                    </motion.div>
                  ))
                }
                <div className="w-[28px] h-[28px] bg-[#858788] group hover:bg-[#ff0033] rounded-full flex items-center justify-center">
                  <span className="text-black text-base group-hover:text-white"><YoutubeOutlined /></span>
                </div>
                <div className="w-[28px] h-[28px] bg-[#858788] group hover:bg-[#0c61bf] rounded-full flex items-center justify-center relative overflow-hidden">
                  <Image
                    src="/image/footer-follow/in.png"
                    alt="linkedin"
                    width={100}
                    height={100}
                    className="w-[13px] h-[13px] object-contain transition-opacity duration-200 group-hover:opacity-0"
                  />
                  <Image
                    src="/image/footer-follow/in21.png"
                    alt="linkedin"
                    width={100}
                    height={100}
                    className="w-[13px] h-[13px] object-contain absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transition-opacity duration-200 opacity-0 group-hover:opacity-100"
                  />
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* 联系表单区域 */}
      <div
        className='w-full bg-[#f8f9fa] py-16 lg:py-20'
      >
        <div className="max-w-4xl mx-auto px-4 lg:px-8">
          <motion.div
            className="bg-white rounded-lg shadow-sm p-8 lg:p-12"
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <motion.h3
              className="text-[#333] font-semibold text-[28px] mb-4 text-center"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              {t('Get in Touch')}
            </motion.h3>
            <motion.p
              className="text-[#666] text-center mb-8 irs"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              {t('getInstantQuoteAndTS1')}
            </motion.p>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <ContactForm
                locale={locale}
                title=""
                className="!py-0"
                innerClassName="!shadow-none !rounded-none"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export function HomeTaile({ msg }) {
  return (
    <div className="relative py-[80px] overflow-hidden">
      {/* 背景图片 */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        initial={{ scale: 1.1, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1, ease: "easeOut" }}
      >
        <SEOOptimizedImage
          src="/image/img/ct/11.png"
          alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} ${msg} Banner`}
          width={1920}
          height={400}
          className="w-full h-full object-cover"
        />
      </motion.div>

      {/* 内容层 */}
      <motion.div
        className="relative z-10 container max-md:mt-5"
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <h2 className="text-center ib text-black text-[42px] max-md:text-2xl">{msg}</h2>
      </motion.div>
    </div>
  )
}
