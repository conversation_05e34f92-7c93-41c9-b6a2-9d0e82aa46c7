"use client";
import { useSearchParams } from "next/navigation";
import { useRouter } from "@/navigation";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Placeholder } from "@/components/Placeholder";
import { BodyText } from "@/components/BodyText";
import { Link } from "@/navigation";
import { Button } from "@/components/Button";
import { App, Tooltip } from "antd";
import axios from "axios";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import { GetOrderBuyID } from "@/lib/api/Checkout";
import { getImagesForProduct } from "../Product/imageGallery";
import React from "react";
import { useProductStore } from "@/lib/store/product.store";
import EmptyState from "../EmptyState";
import { defaultLocale } from "@/config";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
type OrderStatus =
	| "DRAFT" // 草稿
	| "UNCONFIRMED" // 未确认
	| "UNFULFILLED" // 未发货
	| "PARTIALLY_FULFILLED" // 部分发货
	| "PARTIALLY_RETURNED" // 部分退货
	| "RETURNED" // 已退货
	| "FULFILLED" // 已发货
	| "CANCELED" // 已取消
	| "EXPIRED"; // 已过期

interface StatusConfig {
	color: string;
	bgColor: string;
	icon: string;
	borderColor: string;
}

function PaypalState() {
	const searchParams = useSearchParams();
	const {
		addToCart,
		findCheckout,
		findOrCreateCheckoutId,
		createCheckout,
		removeCartItem,
		updateLinesCount,
	} = useShoppingCart();
	//购物车数据
	const { isCart, cartList } = useShoppingCartStore() as any;
	let router = useRouter();
	const { isLogin } = useUserAuth();
	let { currencyUnit } = useProductStore();
	const t = useTranslations();
	const { message } = App.useApp();
	const [loading, setLoading] = useState(true);
	const [orderStatus, setOrderStatus] = useState<{
		status: string;
		orderId?: string;
	}>({
		status: "pending",
	});
	let locale = useLocale();
	const [orderData, setorderData] = useState(null);
	useEffect(() => {
		const checkoutId = searchParams.get("checkout_id");

		if (!checkoutId) {
			// message.error(t("message.invalid_checkout_id"));
			return;
		}
		if (isCart) {
			// 安全地检查 cartList 是否存在且有 lines 属性
			if (cartList?.lines?.length > 0) {
				removeCartItem(cartList.lines.map((item: any) => item.id));
				// 刷新购物车数据
				findCheckout("default-channel");
			}
		}

		// 查询支付状态
		const checkPaymentStatus = async () => {
			try {
				const { order } = await GetOrderBuyID(checkoutId);
				setorderData(order);
				console.log(order, "response");

				// if (response.data.code === 200) {
				//   setOrderStatus({
				//     status: "success",
				//     orderId: response.data.order_id
				//   });
				// } else {
				//   setOrderStatus({
				//     status: "failed"
				//   });
				//   message.error(response.data.message);
				// }
			} catch (error) {
				setOrderStatus({
					status: "failed",
				});
				// message.error(t("message.payment_check_failed"));
			} finally {
				setLoading(false);
			}
		};

		checkPaymentStatus();
	}, []);

	if (loading) {
		return (
			<div className="mt-5 flex min-h-[90vh] items-center justify-center">
				<div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-main"></div>
			</div>
		);
	}

	// 空状态或错误状态
	if (!orderData) {
		return (
			<div className="flex min-h-[90vh] items-center justify-center">
				<EmptyState />
			</div>
		);
	}

	console.log(orderData, "orderData");

	function toOrder() {
		// if (!isLogin()) {
		//   const login = document.querySelector("#web-login") as HTMLLIElement;
		//   return  login.click();
		// };
		router.push(`/center/order`);
	}

	const copyOrderId = () => {
		if (orderData?.id) {
			navigator.clipboard.writeText(orderData.id);
			message.success(t("order.order_id_copied"));
		}
	};

	return (
		<div className="container mx-auto min-h-[90vh] bg-gray-50 px-4 py-8">
			{orderData && (
				<div className="mx-auto max-w-3xl space-y-8">
					{/* 订单状态卡片 - 重新设计 */}
					<div className="relative overflow-hidden rounded-2xl border border-gray-100 bg-white p-8 text-center shadow-lg">
						{/* 背景装饰 */}
						<div className="absolute left-0 top-0 h-1 w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
						<div className="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-blue-50 opacity-20"></div>
						<div className="absolute -bottom-20 -left-20 h-40 w-40 rounded-full bg-purple-50 opacity-20"></div>

						<div className="relative">
							{/* 成功图标动画 */}
							<div className="mb-6 flex justify-center">
								<div className="relative">
									<div className="absolute inset-0 animate-ping rounded-full bg-green-100 opacity-30"></div>
									<Placeholder
										src={"/image/checkout/zhifuchenggong.png"}
										imageWidth={160}
										imageHeight={160}
										alt="Payment Status"
										className="relative z-10 transform !bg-transparent transition-transform hover:scale-105"
									/>
								</div>
							</div>

							{/* 订单状态 */}
							<div className="mb-6">
								<h2 className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
									{t("order.payment_successful")}
								</h2>
								<p className="mt-2 text-gray-500">{orderData?.status}</p>
							</div>

							{/* 订单ID部分 */}
							<div className="inline-flex items-center gap-3 rounded-full bg-gray-50 px-4 py-2 shadow-sm transition-all hover:shadow-md">
								<Tooltip placement="top" title={t("order.Theorder99")}>
									<>
										{" "}
										<span className="text-gray-600">{t("order.order_id")}:</span>
										<span className="font-mono font-medium text-gray-800">{orderData?.id}</span>
									</>
								</Tooltip>

								<button
									onClick={copyOrderId}
									type="button"
									className="rounded-full p-1.5 transition-all duration-200 hover:bg-white hover:shadow-sm"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-4 w-4 text-gray-500"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
										/>
									</svg>
								</button>
							</div>
						</div>
					</div>

					{/* 订单商品列表 - 优化样式 */}
					<div className="overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-lg">
						<div className="border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white p-6">
							<h3 className="flex items-center gap-2 text-lg font-semibold">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									className="h-5 w-5 text-gray-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
									/>
								</svg>
								{t("order.items")}
								<span className="ml-2 rounded-full bg-gray-100 px-2 py-0.5 text-sm text-gray-600">
									{orderData?.lines?.length}
								</span>
							</h3>
						</div>
						<div className="divide-y divide-gray-100">
							{orderData?.lines &&
								orderData?.lines?.map((item: any, index: number) => (
									<div key={index} className="p-6 transition-all hover:bg-gray-50">
										<div className="flex gap-6">
											{/* 商品图片 */}
											<div className="h-24 w-24 flex-shrink-0">
												<div className="aspect-square overflow-hidden rounded-xl border border-gray-200 shadow-sm transition-transform hover:scale-105">
													<img
														src={
															getImagesForProduct(item.variant.product)[0]?.url ||
															"/image/default-image2.webp"
														}
														alt={item.variant?.product?.name || "Product image"}
														className="h-full w-full object-cover"
													/>
												</div>
											</div>

											{/* 商品信息 */}
											<div className="flex-1">
												<h4 className="mb-2 text-lg font-medium transition-colors hover:text-blue-600">
													{locale == defaultLocale
														? item.variant?.product?.name
														: item.variant?.product?.translation.name}
												</h4>
												<div className="space-y-2 text-sm text-gray-600">
													<p className="flex items-center gap-2">
														<span className="text-gray-500">{t("order.SKU")}:</span>
														<span className="font-mono">{item.variant?.sku}</span>
													</p>
													<div className="flex items-center gap-6 max-md:flex-col max-md:items-start max-md:gap-1">
														<span className="flex items-center gap-2">
															<span className="text-gray-500">{t("order.quantity")}:</span>
															<span className="font-medium">{item.quantity}</span>
														</span>
														<span className="flex items-center gap-2">
															<span className="text-gray-500">{t("order.price")}:</span>
															<span className="font-medium text-blue-600">
																{currencyUnit} {item.totalPrice.gross.amount}
															</span>
														</span>
													</div>
												</div>
											</div>
										</div>
									</div>
								))}
						</div>
					</div>

					{/* 地址信息 - 重新设计 */}
					<div className="relative overflow-hidden rounded-2xl border border-gray-100 bg-white p-8 shadow-lg max-md:p-4">
						{/* 背景装饰 */}
						<div className="absolute right-0 top-0 h-40 w-40">
							<svg className="h-full w-full text-gray-50" fill="currentColor" viewBox="0 0 100 100">
								<path d="M95 50C95 74.8528 74.8528 95 50 95C25.1472 95 5 74.8528 5 50C5 25.1472 25.1472 5 50 5C74.8528 5 95 25.1472 95 50Z" />
							</svg>
						</div>

						<div className="relative">
							{/* 标题部分 */}
							<div className="mb-6 flex items-center gap-4">
								<div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-50">
									<svg
										className="h-6 w-6 text-blue-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth="2"
											d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
										/>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth="2"
											d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
										/>
									</svg>
								</div>
								<div>
									<h3 className="text-xl font-semibold text-gray-900">{t("order.shipping_address")}</h3>
									<p className="text-sm text-gray-500">{t("order.delivery_info")}</p>
								</div>
							</div>

							{/* 地址卡片 */}
							<div className="rounded-xl border border-gray-100 bg-gradient-to-br from-gray-50 to-white p-6 shadow-sm">
								{/* 收件人信息 */}
								<div className="mb-4 flex items-center gap-3">
									<div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-50">
										<svg
											className="h-5 w-5 text-blue-600"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
											/>
										</svg>
									</div>
									<div>
										<h4 className="font-medium text-gray-900">
											{orderData.shippingAddress.companyName}
											{orderData.shippingAddress.lastName && (
												<span className="ml-1">- {orderData.shippingAddress.lastName}</span>
											)}
										</h4>
										{orderData.shippingAddress.phone && (
											<p className="mt-1 flex items-center gap-2 text-sm text-gray-600">
												<svg
													className="h-4 w-4 text-gray-400"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
												>
													<path
														strokeLinecap="round"
														strokeLinejoin="round"
														strokeWidth="2"
														d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
													/>
												</svg>
												{orderData.shippingAddress.phone}
											</p>
										)}
									</div>
								</div>

								{/* 详细地址 */}
								<div className="pl-13 space-y-2">
									<div className="flex items-start gap-2">
										<div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-50">
											<svg
												className="mt-0.5 h-5 w-5 text-gray-400"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth="2"
													d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
												/>
											</svg>
										</div>

										<div className="flex-1">
											<p className="font-medium text-gray-700">{orderData.shippingAddress.streetAddress1}</p>
											<p className="text-gray-600">
												{[
													orderData.shippingAddress.city,
													orderData.shippingAddress.countryArea,
													orderData.shippingAddress.country.code,
												]
													.filter(Boolean)
													.join(", ")}
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					{/* 操作按钮 */}
					<div className="flex justify-center gap-4 pt-4">
						<Link href="/">
							<Button
								size="lg"
								className="duration-2000 bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md transition-all hover:bg-gray-200 hover:from-blue-700 hover:to-purple-700 hover:shadow-lg"
							>
								{t("message.back_to_home")}
							</Button>
						</Link>
						{isLogin() && (
							<div onClick={toOrder}>
								<Button
									size="lg"
									className="hover:bg-primary-dark bg-gradient-to-r from-blue-600  to-purple-600 text-white shadow-md transition-all duration-200 hover:from-blue-700 hover:to-purple-700 hover:shadow-lg"
								>
									{t("message.view_order")}
								</Button>
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
}

export default PaypalState;
